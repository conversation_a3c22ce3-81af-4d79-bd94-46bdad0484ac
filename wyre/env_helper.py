import os, json
from pathlib import Path

###########################################################

# SET ENVIRONMENT VARIABLES
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

def load_env():
    try:
        # Try to load from .env/ directory first (original behavior)
        env_data = json.loads(open(BASE_DIR / '.env/', "r").read())
    except (FileNotFoundError, IsADirectoryError):
        # If that fails, try to load from .env file
        env_data = json.loads(open(BASE_DIR / '.env', "r").read())

    for key, value in env_data.items():
        # Set environment variables
        os.environ[key] = str(value)

###########################################################