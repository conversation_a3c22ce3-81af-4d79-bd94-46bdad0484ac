"""wyre URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

import debug_toolbar
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import (TokenObtainPairView, TokenRefreshView,)
from account.views import CustomTokenObtainPairView
from filebrowser.sites import site


urlpatterns = [
    path('admin/filebrowser/', site.urls),
    path('__debug__/', include(debug_toolbar.urls)),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('admin/', admin.site.urls),
    path('api/v1/', include('account.urls')),
    path('api/v1/accounts/', include('account.urls')),
    path('api/v1/', include('main.urls')),
    path('api/v1/', include('adminapp.urls')),
    path('posts/', include('posts.urls')),
    path('cadmin/', include('adminapp.urls')),
    path('tinymce/', include('tinymce.urls')),
    path('socials/', include('socials.urls')),
    path('api/v2/', include('adminapp.urls')),
    path('vendor/', include('vendor.urls')),

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
