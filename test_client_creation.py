#!/usr/bin/env python3
import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wyre.settings')
django.setup()

# Import models
from main.models import DeviceType, Branch, Device, ViewPermission
from account.models import Client, User
from account.serializers import ClientCreateWithDetailsSerializer
from django.contrib.auth.hashers import make_password

# Print all device types
print("Device Types:")
device_types = DeviceType.objects.all()
for dt in device_types:
    print(f"- {dt.id}: {dt.choice_name}")

# Create test data
utility_type = DeviceType.objects.filter(choice_name__iexact="UTILITY").first()
generator_type = DeviceType.objects.filter(choice_name__iexact="GENERATOR").first()

if not utility_type:
    print("Creating UTILITY device type")
    utility_type = DeviceType.objects.create(choice_name="UTILITY")

if not generator_type:
    print("Creating GENERATOR device type")
    generator_type = DeviceType.objects.create(choice_name="GENERATOR")

# Test data for client creation
test_data = {
    "name": f"Test Client {os.getpid()}",
    "client_type": "STANDARD",
    "phone_number": "**********",
    "email": "<EMAIL>",
    "address": "123 Test Street",
    "additional_emails": ["<EMAIL>", "<EMAIL>"],
    "main_user": {
        "username": f"testuser_{os.getpid()}",
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "phone_number": "**********",
        "password": "testpassword",
        "roles": 3  # CLIENT_ADMIN
    },
    "branches": [
        {
            "name": "Branch 1",
            "address": "Branch 1 Address",
            "email": "<EMAIL>",
            "devices": [
                {
                    "name": "Utility Device",
                    "type": utility_type.id if hasattr(utility_type, 'id') else 1,
                    "is_load": False,
                    "is_source": True,
                    "provider": "SATEC",
                    "device_id": "UT001"
                },
                {
                    "name": "Generator Device",
                    "type": generator_type.id if hasattr(generator_type, 'id') else 2,
                    "is_load": False,
                    "is_source": True,
                    "provider": "ACCRELL",
                    "gen_size": 100,
                    "device_id": "GEN001",
                    "fuel_type": "diesel"
                }
            ]
        },
        {
            "name": "Branch 2",
            "address": "Branch 2 Address",
            "email": "<EMAIL>",
            "devices": [
                {
                    "name": "IPP Device",
                    "type": 3,  # IPP
                    "is_load": False,
                    "is_source": True,
                    "provider": "SATEC",
                    "device_id": "IPP001"
                }
            ]
        }
    ],
    "additional_users": [
        {
            "username": f"operator_{os.getpid()}",
            "first_name": "Operator",
            "last_name": "User",
            "email": "<EMAIL>",
            "phone_number": "**********",
            "password": "password123",
            "roles": 4  # OPERATOR
        }
    ]
}

# Test serializer validation
print("\nTesting serializer validation...")
serializer = ClientCreateWithDetailsSerializer(data=test_data)
if serializer.is_valid():
    print("Serializer validation successful!")
    print("Validated data:", json.dumps(serializer.validated_data, indent=2, default=str))
else:
    print("Serializer validation failed!")
    print("Errors:", serializer.errors)

# Check if client already exists
client_name = f"Test Client {os.getpid()}"
existing_client = Client.objects.filter(name=client_name).first()
if existing_client:
    print(f"\nClient '{client_name}' already exists (ID: {existing_client.id})")
    # Delete existing client for testing
    print("Deleting existing client for testing...")
    existing_client.delete()

# Create client
print("\nCreating client...")
try:
    # Fix the device type issue by directly creating the client and related objects
    branches_data = serializer.validated_data.pop('branches', [])
    main_user_data = serializer.validated_data.pop('main_user')
    additional_users_data = serializer.validated_data.pop('additional_users', [])

    # Create the client
    client = Client.objects.create(**serializer.validated_data)
    print(f"Client created successfully! (ID: {client.id})")

    # Create the main user
    main_user_data['client'] = client
    main_user_data['is_ceo'] = True
    main_user_data['password'] = make_password(main_user_data['password'])
    main_user = User.objects.create(**main_user_data)
    print(f"Main user created: {main_user.username}")

    # Create branches and devices
    for branch_data in branches_data:
        devices_data = branch_data.pop('devices', [])
        branch = Branch.objects.create(client=client, **branch_data)
        print(f"Branch created: {branch.name}")

        # Create devices for this branch
        for device_data in devices_data:
            device_type_name = device_data.pop('type')
            device_type = DeviceType.objects.get(choice_name=device_type_name)
            device = Device.objects.create(
                client=client,
                branch=branch,
                type=device_type,
                **device_data
            )
            print(f"Device created: {device.name} (Type: {device_type.choice_name})")

        # Create view permission for main user
        ViewPermission.objects.create(user=main_user, branch=branch)

    # Create additional users
    for user_data in additional_users_data:
        user_data['client'] = client
        user_data['password'] = make_password(user_data['password'])
        user = User.objects.create(**user_data)
        print(f"Additional user created: {user.username}")

        # Create view permissions for all branches
        for branch in Branch.objects.filter(client=client):
            ViewPermission.objects.create(user=user, branch=branch)
            print(f"  Permission created for {user.username} on branch {branch.name}")

    # Check branches
    branches = Branch.objects.filter(client=client)
    print(f"Branches created: {branches.count()}")
    for branch in branches:
        print(f"- {branch.name} (ID: {branch.id})")

        # Check devices
        devices = Device.objects.filter(branch=branch)
        print(f"  Devices: {devices.count()}")
        for device in devices:
            print(f"  - {device.name} (Type: {device.type.choice_name}, ID: {device.id})")

    # Check users
    users = User.objects.filter(client=client)
    print(f"\nUsers created: {users.count()}")
    for user in users:
        print(f"- {user.username} (ID: {user.id}, Role: {user.get_roles_display()})")

        # Check view permissions
        permissions = ViewPermission.objects.filter(user=user)
        print(f"  View permissions: {permissions.count()}")
        for perm in permissions:
            print(f"  - Branch: {perm.branch.name}")

except Exception as e:
    print(f"Error creating client: {str(e)}")
