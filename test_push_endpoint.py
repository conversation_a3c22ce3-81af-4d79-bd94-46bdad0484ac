#!/usr/bin/env python3
"""
Simple test script for the Device Push Data API endpoint
"""

import requests
import json

# Test data matching the interface specification
test_payload = {
    "flag": "00",
    "deviceUserid": 385,
    "parentUserId": "217",
    "sensorsDates": [
        {
            "times": "14:16:21",
            "sensorsId": 11922,
            "isAlarm": "0",
            "sensorsTypeId": 1,
            "isLine": 1,
            "reVal": "5.0000",
            "value": "5.0"
        },
        {
            "times": "14:16:21",
            "sensorsId": 11923,
            "isAlarm": "0",
            "sensorsTypeId": 1,
            "isLine": 1,
            "reVal": "2.7434",
            "value": "28.6"
        },
        {
            "times": "14:16:21",
            "sensorsId": 11924,
            "isAlarm": "0",
            "sensorsTypeId": 1,
            "isLine": 1,
            "reVal": "0.0077",
            "value": "0"
        }
    ],
    "time": "2019-05-10 14:16:21",
    "rawData": "235254552C352E303030302C322E373433342C302E303037370D0A",
    "deviceId": 2864  # Make sure this device exists in your database
}

def test_endpoint(base_url="http://localhost:8000"):
    """Test the push data endpoint"""
    url = f"{base_url}/posts/device/getPushData/"
    
    print(f"Testing endpoint: {url}")
    print(f"Payload: {json.dumps(test_payload, indent=2)}")
    
    try:
        # Test POST request
        response = requests.post(
            url,
            json=test_payload,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Push data processed successfully!")
        else:
            print("❌ ERROR: Request failed")
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to server. Make sure Django is running.")
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")

def test_get_endpoint(base_url="http://localhost:8000"):
    """Test the GET method of the endpoint"""
    url = f"{base_url}/posts/device/getPushData/"
    
    try:
        response = requests.get(url)
        print(f"\nGET Response Status: {response.status_code}")
        print(f"GET Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ GET endpoint is active!")
        else:
            print("❌ GET endpoint failed")
            
    except Exception as e:
        print(f"❌ GET ERROR: {str(e)}")

if __name__ == "__main__":
    print("Device Push Data API Test Script")
    print("=" * 40)
    
    # Test both GET and POST
    test_get_endpoint()
    test_endpoint()
    
    print("\n" + "=" * 40)
    print("Test completed!")
