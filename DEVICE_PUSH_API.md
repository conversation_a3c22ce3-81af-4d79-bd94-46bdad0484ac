# Device Push Data API

This document describes the Device Push Data API endpoint that processes incoming data from external devices.

## Endpoint Details

- **URL**: `/posts/device/getPushData/`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Authentication**: Not required (allows external systems to post data)

## Request Format

The API expects a JSON payload with the following structure:

```json
{
  "flag": "00",
  "deviceUserid": 385,
  "parentUserId": "217",
  "sensorsDates": [
    {
      "times": "14:16:21",
      "sensorsId": 11922,
      "isAlarm": "0",
      "sensorsTypeId": 1,
      "isLine": 1,
      "reVal": "5.0000",
      "value": "5.0"
    },
    {
      "times": "14:16:21",
      "sensorsId": 11923,
      "isAlarm": "0",
      "sensorsTypeId": 1,
      "isLine": 1,
      "reVal": "2.7434",
      "value": "28.6"
    }
  ],
  "time": "2019-05-10 14:16:21",
  "rawData": "235254552C352E303030302C322E373433342C302E303037370D0A",
  "deviceId": 2864
}
```

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `flag` | String | Yes | Status flag |
| `deviceUserid` | Integer | Yes | Device user ID |
| `parentUserId` | String | Yes | Parent user ID |
| `sensorsDates` | Array | Yes | Array of sensor readings |
| `time` | String | Yes | Timestamp in format "YYYY-MM-DD HH:MM:SS" |
| `rawData` | String | No | Raw data from device |
| `deviceId` | Integer | Yes | Device ID (must exist in system) |

### Sensor Data Structure

Each item in the `sensorsDates` array should contain:

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `times` | String | Yes | Time in format "HH:MM:SS" |
| `sensorsId` | Integer | Yes | Sensor ID |
| `isAlarm` | String | Yes | Alarm status ("0" or "1") |
| `sensorsTypeId` | Integer | Yes | Sensor type ID |
| `isLine` | Integer | Yes | Line status |
| `reVal` | String | Yes | Reference value |
| `value` | String | Yes | Sensor value |

## Response Format

### Success Response (200 OK)

```json
{
  "message": "Push data processed successfully",
  "id": 123
}
```

### Error Responses

#### Device Not Found (404 Not Found)
```json
{
  "error": "Device with ID 2864 not found"
}
```

#### Missing Device ID (400 Bad Request)
```json
{
  "error": "deviceId is required"
}
```

#### Invalid Data Format (422 Unprocessable Entity)
```json
{
  "error": "Invalid data format",
  "details": {
    "field_name": ["error description"]
  }
}
```

#### Internal Server Error (500 Internal Server Error)
```json
{
  "error": "Internal server error"
}
```

## Data Storage

The API stores data in two main models:

1. **DevicePushData**: Stores the main push data record
2. **SensorData**: Stores individual sensor readings linked to the push data

Both models have a foreign key relationship to the `Device` model.

## Testing

You can test the endpoint using curl:

```bash
curl -X POST http://your-domain.com/posts/device/getPushData/ \
  -H "Content-Type: application/json" \
  -d '{
    "flag": "00",
    "deviceUserid": 385,
    "parentUserId": "217",
    "sensorsDates": [
      {
        "times": "14:16:21",
        "sensorsId": 11922,
        "isAlarm": "0",
        "sensorsTypeId": 1,
        "isLine": 1,
        "reVal": "5.0000",
        "value": "5.0"
      }
    ],
    "time": "2019-05-10 14:16:21",
    "rawData": "235254552C352E303030302C322E373433342C302E303037370D0A",
    "deviceId": 2864
  }'
```

## Admin Interface

The new models are registered in the Django admin interface:

- **DevicePushData**: View and manage push data records with inline sensor data
- **SensorData**: View and manage individual sensor readings

## Database Migration

After adding the new models, run the following commands to create and apply migrations:

```bash
python manage.py makemigrations
python manage.py migrate
```

## Logging

The API includes comprehensive logging for debugging and monitoring:

- Successful data processing
- Device not found warnings
- Validation errors
- Internal server errors

Check your Django logs for detailed information about API usage and any issues.
