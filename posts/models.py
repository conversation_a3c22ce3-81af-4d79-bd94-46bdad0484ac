from django.db import models

# Create your models here.
{
    "client":0,
    "branch":0,
    "device":0,
    "post_datetime":0,
    "post_date":0,
    "post_time":0,
    "voltage_l1_l12":0,
    "voltage_l2_l23":0,
    "voltage_l3_l31":0,
    "current_l1":0,
    "current_l2":0,
    "current_l3":0,
    "kw_l1":0,
    "kw_l2":0,
    "kw_l3":0,
    "kvar_l1":0,
    "kvar_l2":0,
    "kvar_l3":0,
    "kva_l1":0,
    "kva_l2":0,
    "kva_l3":0,
    "power_factor_l1":0,
    "power_factor_l2":0,
    "power_factor_l3":0,
    "total_kw":0,
    "total_kvar":0,
    "total_kva":0,
    "total_pf":0,
    "avg_frequency":0,
    "neutral_current":0,
    "volt_thd_l1_l12":0,
    "volt_thd_l2_l23":0,
    "volt_thd_l3_l31":0,
    "current_thd_l1":0,
    "current_thd_l2":0,
    "current_thd_l3":0,
    "current_tdd_l1":0,
    "current_tdd_l2":0,
    "current_tdd_l3":0,
    "kwh_import":0,
    "kwh_export":0,
    "kvarh_import":0,
    "kvah_total":0,
    "max_amp_demand_l1":0,
    "max_amp_demand_l2":0,
    "max_amp_demand_l3":0,
    "max_sliding_window_kw_demand":0,
    "accum_kw_demand":0,
    "max_sliding_window_kva_demand":0,
    "present_sliding_window_kw_demand":0,
    "present_sliding_window_kva_demand":0,
    "accum_kva_demand":0,
    "pf_import_at_maximum_kva_sliding_window_demand":0,
}