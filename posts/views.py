from django.shortcuts import render
from django.core import exceptions as djangoExceptions
from rest_framework.decorators import permission_classes
from rest_framework.response import Response
from .serializers import ReadingSerializer, DatalogSerializer, DevicePushDataSerializer
from main.models import Branch, Device, Reading, Datalog, DevicePushData
from rest_framework.views import APIView
from rest_framework import permissions, status
from django.utils import timezone
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ReadingDetailApiView(APIView):

    detail_serializer = ReadingSerializer
    create_serializer = ReadingSerializer

    def post(self, request):
        device = Device.objects.filter(
            device_id=request.data.get("device_id", 99999)
            )
        print(device)

        if device.exists():
            device.update(last_posted=timezone.now())
            device = device[0]
            branch = device.branch.id
            client = device.branch.client.id

            request.data["client"] = client
            request.data["branch"] = branch
            request.data["device"] = device.id

            serializer = self.create_serializer(data=request.data)

            if serializer.is_valid():
                reading_qs = Reading.objects.filter(device_id=device.id, kwh_import__gt = 0)
                last_reading = reading_qs.last()

                # Check if kwh_import is zero

                # print("ZERO DATA::::::::", (serializer.validated_data.get("kwh_import", 0)))
                # print("ZERO DATA::::::::", (serializer.validated_data.get("kwh_import", 0)))
                # print("LAST READING DATA::::::::", last_reading.kwh_import)
                # print("LAST READING DATA::::::::", last_reading.kwh_import)
                # print("LAST READING DATA::::::::", last_reading.kwh_import)
                # print("ZERO RESOLUTION::::::::", int(serializer.validated_data.get("kwh_import", 0)) == 0)
                # if int(serializer.validated_data.get("kwh_import", 0)) == 0 and last_reading:
                #     # Use the last reading's kwh_import if the new one is zero
                #     request.data["kwh_import"] = last_reading.kwh_import
                #     request.data["zero_updated"] = True

                if int(serializer.validated_data.get("kwh_import", 0)) == 0:
                    return Response(serializer.data, status=status.HTTP_201_CREATED)

                obj = serializer.save()
                serializer = self.detail_serializer(obj)

                return Response(serializer.data, status=status.HTTP_201_CREATED)

            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        return Response({"error": "Invalid device id"}, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):

        data = Reading.objects.all()[:2]
        serializer   = self.create_serializer(data, many = True)

        if data.exists():


            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class DatalogDetailApiView(APIView):

    detail_serializer = DatalogSerializer
    create_serializer = DatalogSerializer


    def post(self, request):

        device = Device.objects.filter(device_id = request.data.get("device_id", 99999))
        print(device)

        if device.exists():

            device = device[0]
            branch = device.branch.id
            client = device.branch.client.id

            request.data["client"] = client
            request.data["branch"] = branch
            request.data["device"] = device.id

            serializer   = self.create_serializer(data = request.data)

            if serializer.is_valid(raise_exception=True):

                datalog_qs = Datalog.objects.filter(device__id = device.id)
                datalog:Datalog = datalog_qs.last()

                # if int(datalog.summary_energy_register_1) > int(serializer.validated_data["summary_energy_register_1"]) or int(datalog.summary_energy_register_1) == 0:

                #     serializer.validated_data["summary_energy_register_1"] = datalog.summary_energy_register_1 + serializer.validated_data["summary_energy_register_1"]
                #     serializer.validated_data["zero_updated"] = True
                if int(serializer.validated_data.get("summary_energy_register_1", 0)) == 0:
                    return Response(serializer.data, status=status.HTTP_201_CREATED)

                obj = serializer.save()
                serializer   = self.detail_serializer(obj)

                return Response(serializer.data, status=status.HTTP_201_CREATED)

            return Response(serializer.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        return Response({"error":"Invalid device id"}, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):

        data = Datalog.objects.all()[:2]
        serializer   = self.create_serializer(data, many = True)

        if data.exists():


            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DevicePushDataApiView(APIView):
    """
    API View to handle incoming push data from external devices
    Endpoint: /posts/device/getPushData/
    Method: POST
    """
    permission_classes = [permissions.AllowAny]  # Allow external systems to post data

    def post(self, request):
        """
        Process incoming push data from external devices
        Expected payload format matches the interface specification
        """
        try:
            # Extract device ID from the payload
            device_id = request.data.get("deviceId")
            if not device_id:
                return Response(
                    {"error": "deviceId is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Find the device
            try:
                device = Device.objects.get(device_id=device_id)
            except Device.DoesNotExist:
                logger.warning(f"Device with ID {device_id} not found")
                return Response(
                    {"error": f"Device with ID {device_id} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Prepare data for serializer
            serializer_data = {
                'flag': request.data.get('flag'),
                'device_user_id': request.data.get('deviceUserid'),
                'parent_user_id': request.data.get('parentUserId'),
                'timestamp': request.data.get('time'),
                'raw_data': request.data.get('rawData', ''),
                'sensors_dates': request.data.get('sensorsDates', [])
            }

            # Validate and save the data
            serializer = DevicePushDataSerializer(data=serializer_data)
            if serializer.is_valid():
                # Add device to validated data
                serializer.validated_data['device'] = device

                # Save the push data
                push_data = serializer.save()

                # Update device's last_posted timestamp
                device.last_posted = timezone.now()
                device.save(update_fields=['last_posted'])

                logger.info(f"Successfully processed push data for device {device_id}")

                return Response(
                    {"message": "Push data processed successfully", "id": push_data.id},
                    status=status.HTTP_200_OK
                )
            else:
                logger.error(f"Serializer validation failed: {serializer.errors}")
                return Response(
                    {"error": "Invalid data format", "details": serializer.errors},
                    status=status.HTTP_422_UNPROCESSABLE_ENTITY
                )

        except Exception as e:
            logger.error(f"Error processing push data: {str(e)}")
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get(self, request):
        """
        Optional GET method for testing the endpoint
        """
        return Response(
            {"message": "Device Push Data API endpoint is active"},
            status=status.HTTP_200_OK
        )