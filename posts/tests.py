from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from main.models import Device, DevicePushData, SensorData, DeviceType, Client, Branch
from account.models import User
import json


class DevicePushDataApiTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        # Create test client
        self.client_obj = Client.objects.create(
            name="Test Client",
            address="Test Address",
            client_type="COMMERCIAL"
        )

        # Create test branch
        self.branch = Branch.objects.create(
            name="Test Branch",
            client=self.client_obj,
            address="Test Branch Address"
        )

        # Create device type
        self.device_type = DeviceType.objects.create(
            choice_name="Generator"
        )

        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            type=self.device_type,
            client=self.client_obj,
            branch=self.branch,
            device_id="2864"
        )

        # Sample payload matching the interface specification
        self.sample_payload = {
            "flag": "00",
            "deviceUserid": 385,
            "parentUserId": "217",
            "sensorsDates": [
                {
                    "times": "14:16:21",
                    "sensorsId": 11922,
                    "isAlarm": "0",
                    "sensorsTypeId": 1,
                    "isLine": 1,
                    "reVal": "5.0000",
                    "value": "5.0"
                },
                {
                    "times": "14:16:21",
                    "sensorsId": 11923,
                    "isAlarm": "0",
                    "sensorsTypeId": 1,
                    "isLine": 1,
                    "reVal": "2.7434",
                    "value": "28.6"
                }
            ],
            "time": "2019-05-10 14:16:21",
            "rawData": "235254552C352E303030302C322E373433342C302E303037370D0A",
            "deviceId": 2864
        }

    def test_successful_push_data_processing(self):
        """Test successful processing of push data"""
        url = reverse('device_push_data')
        response = self.client.post(url, self.sample_payload, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['message'], 'Push data processed successfully')

        # Verify data was saved
        self.assertEqual(DevicePushData.objects.count(), 1)
        self.assertEqual(SensorData.objects.count(), 2)

        # Verify device last_posted was updated
        self.device.refresh_from_db()
        self.assertIsNotNone(self.device.last_posted)

    def test_invalid_device_id(self):
        """Test handling of invalid device ID"""
        invalid_payload = self.sample_payload.copy()
        invalid_payload['deviceId'] = 99999

        url = reverse('device_push_data')
        response = self.client.post(url, invalid_payload, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)

    def test_missing_device_id(self):
        """Test handling of missing device ID"""
        invalid_payload = self.sample_payload.copy()
        del invalid_payload['deviceId']

        url = reverse('device_push_data')
        response = self.client.post(url, invalid_payload, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_get_endpoint(self):
        """Test GET method for endpoint status"""
        url = reverse('device_push_data')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
