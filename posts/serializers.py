from rest_framework import serializers
from main.models import Reading, Datalog, DevicePushData, SensorData



class ReadingSerializer(serializers.ModelSerializer):

    class Meta:
        model = Reading
        exclude = ("id",)

class DatalogSerializer(serializers.ModelSerializer):

    class Meta:
        model = Datalog
        exclude = ("id",)


class SensorDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = SensorData
        fields = ['sensor_id', 'sensor_type_id', 'times', 'is_alarm', 'is_line', 're_val', 'value']


class DevicePushDataSerializer(serializers.ModelSerializer):
    sensors_dates = SensorDataSerializer(many=True, write_only=True)

    class Meta:
        model = DevicePushData
        fields = ['flag', 'device_user_id', 'parent_user_id', 'timestamp', 'raw_data', 'sensors_dates']
        extra_kwargs = {
            'timestamp': {'source': 'timestamp', 'input_formats': ['%Y-%m-%d %H:%M:%S']},
        }

    def create(self, validated_data):
        sensors_data = validated_data.pop('sensors_dates', [])
        push_data = DevicePushData.objects.create(**validated_data)

        for sensor_data in sensors_data:
            SensorData.objects.create(push_data=push_data, **sensor_data)

        return push_data
