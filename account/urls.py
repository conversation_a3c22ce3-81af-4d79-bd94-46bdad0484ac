from account.views import add_user, get_users
from django.urls import path
from account import views

urlpatterns = [
    path('get_users/', views.get_users),
    path('add_user_to_org/<int:user_id>', views.add_user),
    path('create_user/', views.create_user),
    path('add_org/', views.add_client),
    path('auth/', views.user_login),
    path('reset_password/', views.reset_password),

    # New endpoints for client creation with details
    path('create_client_with_details/', views.ClientWithDetailsView.as_view(), name='create_client_with_details'),

    path('create-client-with-regions/', views.ClientCreateWithRegionsView.as_view(), name='client-create-with-regions'),
    path('view-update-client/<int:client_id>/', views.ClientCreateWithRegionsView.as_view(), name='update-client-regions'),
    path('client/<int:client_id>/main-user/', views.MainClientUserCreateView.as_view(), name='main-client-user-create'),
    path('client/<int:client_id>/additional-user/', views.AdditionalUserCreateView.as_view(), name='additional-user-create'),
    path('client/<int:client_id>/branches/', views.BranchBulkCreateView.as_view(), name='client-branch-bulk-create'),
    path('update-branch/<int:branch_id>/', views.BranchBulkCreateView.as_view(), name='update-branch-bulk'),
    path('client/<int:client_id>/regions/', views.client_regions, name='client_regions'),
    path('client/<int:client_id>/add-regions/', views.client_regions_branches, name='add_region'),
    path('client/<int:client_id>/regions-branches/', views.client_regions_branches, name='client_regions_branches'),


    path('device_types/', views.get_device_types, name='get_device_types'),

    path('region/<int:region_id>/', views.RegionUpdateDeleteView.as_view(), name='region-update-delete'),

]
