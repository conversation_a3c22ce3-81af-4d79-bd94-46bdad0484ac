from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from account.models import Client, User
from main.models import Branch, Device, DeviceType, ViewPermission
import json

class ClientCreationWithDetailsTest(TestCase):
    def setUp(self):
        # Create a test admin user
        self.client_obj = Client.objects.create(name="Test Client")
        self.admin_user = User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="adminpassword",
            client=self.client_obj
        )
        
        # Create device types for testing
        self.utility_type = DeviceType.objects.create(choice_name="UTILITY")
        self.generator_type = DeviceType.objects.create(choice_name="GENERATOR")
        
        # Set up API client
        self.api_client = APIClient()
        self.api_client.force_authenticate(user=self.admin_user)
        
        # URL for the endpoint
        self.url = reverse('create_client_with_details')
        
    def test_create_client_with_details(self):
        """Test creating a client with branches, devices, and users"""
        
        # Prepare test data
        data = {
            "name": "New Test Client",
            "client_type": "STANDARD",
            "phone_number": "1234567890",
            "email": "<EMAIL>",
            "address": "123 Test Street",
            "main_user": {
                "username": "testuser",
                "first_name": "Test",
                "last_name": "User",
                "email": "<EMAIL>",
                "phone_number": "0987654321",
                "password": "testpassword",
                "roles": 3  # CLIENT_ADMIN
            },
            "branches": [
                {
                    "name": "Branch 1",
                    "address": "Branch 1 Address",
                    "email": "<EMAIL>",
                    "devices": [
                        {
                            "name": "Utility Device",
                            "type": self.utility_type.id,
                            "is_load": False,
                            "is_source": True,
                            "provider": "SATEC",
                            "device_id": "UT001"
                        },
                        {
                            "name": "Generator Device",
                            "type": self.generator_type.id,
                            "is_load": False,
                            "is_source": True,
                            "provider": "ACCRELL",
                            "gen_size": 100,
                            "device_id": "GEN001",
                            "fuel_type": "diesel"
                        }
                    ]
                },
                {
                    "name": "Branch 2",
                    "address": "Branch 2 Address",
                    "email": "<EMAIL>",
                    "devices": [
                        {
                            "name": "Utility Device 2",
                            "type": self.utility_type.id,
                            "is_load": False,
                            "is_source": True,
                            "provider": "SATEC",
                            "device_id": "UT002"
                        }
                    ]
                }
            ],
            "additional_users": [
                {
                    "username": "operator1",
                    "first_name": "Operator",
                    "last_name": "One",
                    "email": "<EMAIL>",
                    "phone_number": "**********",
                    "password": "operatorpassword",
                    "roles": 4  # OPERATOR
                },
                {
                    "username": "viewer1",
                    "first_name": "Viewer",
                    "last_name": "One",
                    "email": "<EMAIL>",
                    "phone_number": "**********",
                    "password": "viewerpassword",
                    "roles": 5  # VIEWER
                }
            ]
        }
        
        # Make the request
        response = self.api_client.post(self.url, data, format='json')
        
        # Check response status
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify client was created
        self.assertTrue(Client.objects.filter(name="New Test Client").exists())
        client = Client.objects.get(name="New Test Client")
        
        # Verify branches were created
        self.assertEqual(Branch.objects.filter(client=client).count(), 2)
        
        # Verify devices were created
        self.assertEqual(Device.objects.filter(client=client).count(), 3)
        
        # Verify users were created
        self.assertEqual(User.objects.filter(client=client).count(), 3)  # main_user + 2 additional users
        
        # Verify view permissions were created
        main_user = User.objects.get(username="testuser")
        self.assertEqual(ViewPermission.objects.filter(user=main_user).count(), 2)  # One for each branch
        
        # Verify additional users have view permissions
        operator = User.objects.get(username="operator1")
        self.assertEqual(ViewPermission.objects.filter(user=operator).count(), 2)
        
        viewer = User.objects.get(username="viewer1")
        self.assertEqual(ViewPermission.objects.filter(user=viewer).count(), 2)
