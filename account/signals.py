from django.db.models.signals import post_save
from django.dispatch import receiver
from django.template.loader import render_to_string
from django.conf import settings
import random
import string

from account.models import User
from main.scripts.mailgun import Mailer

@receiver(post_save, sender=User)
def send_credentials_email(sender, instance, created, **kwargs):
    """
    Signal to send an email with login credentials when a new user is created.
    """
    if created and instance.email:
        # Generate a random password if one wasn't set
        raw_password = None
        if instance.has_no_password:
            # Generate a random password
            raw_password = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
            instance.password = raw_password
            instance.has_no_password = False
            instance.save(update_fields=['password', 'has_no_password'])

        # Prepare email content
        context = {
            'username': instance.username,
            'password': raw_password,  # Only included if we generated a new password
            'client_name': instance.client.name if instance.client else 'Wyre',
            'role': instance.get_roles_display(),
            'login_url': settings.FRONTEND_URL + '/login' if hasattr(settings, 'FRONTEND_URL') else '/login',
            'support_email': settings.SUPPORT_EMAIL if hasattr(settings, 'SUPPORT_EMAIL') else '<EMAIL>',
        }

        # Create email subject and message
        subject = f"Welcome to {context['client_name']} - Your Account Details"

        # Create HTML message
        html_message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4CAF50; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                .credentials {{ background-color: #f9f9f9; padding: 15px; margin: 15px 0; border-left: 4px solid #4CAF50; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>Welcome to {context['client_name']}</h2>
                </div>
                <div class="content">
                    <p>Hello {instance.first_name or instance.username},</p>

                    <p>Your account has been created on the Wyre Energy Management Platform. You can now log in to access your dashboard and monitor your energy usage.</p>

                    <div class="credentials">
                        <p><strong>Your login credentials:</strong></p>
                        <p>Username: {context['username']}</p>
                        {f"<p>Password: {context['password']}</p>" if context['password'] else "<p>Please use the password you provided during registration.</p>"}
                        <p>Role: {context['role']}</p>
                    </div>

                    <p>You can log in at: <a href="{context['login_url']}">{context['login_url']}</a></p>

                    <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:{context['support_email']}">{context['support_email']}</a>.</p>

                    <p>Thank you for choosing Wyre Energy Management Platform!</p>
                </div>
                <div class="footer">
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Send the email
        try:
            print(Mailer.send_simple_message_html(
                sender=2,  # Using WYRE-ALERTS as the sender
                title=subject,
                message=html_message,
                receievers=[instance.email]
            ))
            print(f"Credentials email sent to {instance.email}")
        except Exception as e:
            print(f"Failed to send credentials email: {str(e)}")
