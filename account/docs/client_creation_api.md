# Client Creation API Documentation

This document describes how to use the Client Creation API to create a client with branches, devices, and users in a single request.

## Endpoints

### 1. Create Client with Details

**URL**: `/api/v1/accounts/create_client_with_details/`

**Method**: `POST`

**Authentication**: Required (Admin user)

**Description**: Creates a new client with branches, devices, and users in a single request.

**Request Body**:

```json
{
  "name": "Client Name",
  "logo": null,
  "client_type": "STANDARD",
  "phone_number": "**********",
  "email": "<EMAIL>",
  "address": "Client Address",
  "additional_emails": ["<EMAIL>"],
  "main_user": {
    "username": "mainuser",
    "first_name": "Main",
    "last_name": "User",
    "email": "<EMAIL>",
    "phone_number": "**********",
    "password": "password123",
    "roles": 3
  },
  "branches": [
    {
      "name": "Branch 1",
      "address": "Branch 1 Address",
      "email": "<EMAIL>",
      "devices": [
        {
          "name": "Utility Device",
          "type": 1,
          "is_load": false,
          "is_source": true,
          "provider": "SATEC",
          "device_id": "UT001"
        },
        {
          "name": "Generator Device",
          "type": 2,
          "is_load": false,
          "is_source": true,
          "provider": "ACCRELL",
          "gen_size": 100,
          "device_id": "GEN001",
          "fuel_type": "diesel"
        }
      ]
    }
  ],
  "additional_users": [
    {
      "username": "operator1",
      "first_name": "Operator",
      "last_name": "One",
      "email": "<EMAIL>",
      "phone_number": "**********",
      "password": "password123",
      "roles": 4
    }
  ]
}
```

**Response**:

```json
{
  "status": true,
  "message": "Client created successfully with all related data",
  "data": {
    "client_id": 123,
    "client_name": "Client Name",
    "branches_count": 1,
    "users_count": 2
  }
}
```

### 2. Get Device Types

**URL**: `/api/v1/accounts/device_types/`

**Method**: `GET`

**Authentication**: Required

**Description**: Returns a list of all available device types.

**Response**:

```json
{
  "status": true,
  "message": "Device types retrieved successfully",
  "data": [
    {
      "id": 1,
      "choice_name": "UTILITY"
    },
    {
      "id": 2,
      "choice_name": "GENERATOR"
    },
    {
      "id": 3,
      "choice_name": "IPP"
    }
  ]
}
```

## Field Descriptions

### Client Fields

- `name`: Name of the client (required)
- `logo`: Client logo image (optional)
- `client_type`: Type of client (STANDARD, BESPOKE, RESELLER, BULK_MONITORING)
- `phone_number`: Client phone number
- `email`: Client email address
- `address`: Client physical address
- `additional_emails`: List of additional email addresses for the client

### User Fields

- `username`: Username for login (required)
- `first_name`: User's first name
- `last_name`: User's last name
- `email`: User's email address
- `phone_number`: User's phone number
- `password`: User's password (required)
- `roles`: User role (1=SUPERADMIN, 2=ADMIN, 3=CLIENT_ADMIN, 4=OPERATOR, 5=VIEWER)
- `is_ceo`: Whether the user is a CEO (boolean)

### Branch Fields

- `name`: Name of the branch (required)
- `address`: Branch physical address
- `email`: Branch email address

### Device Fields

- `name`: Name of the device (required)
- `type`: ID of the device type (required)
- `is_load`: Whether the device is a load (boolean)
- `is_source`: Whether the device is a source (boolean)
- `provider`: Device provider (ACCRELL, SATEC, ACREL-ACB)
- `gen_size`: Generator size (for generator devices)
- `device_id`: Unique identifier for the device (required)
- `fuel_type`: Type of fuel (for generator devices)
- `operating_hours_start`: Start time for operating hours (format: HH:MM)
- `operating_hours_end`: End time for operating hours (format: HH:MM)

## Error Handling

If there's an error during the creation process, the API will return an error response:

```json
{
  "status": false,
  "message": "Error creating client",
  "error": "Error message details"
}
```

For validation errors:

```json
{
  "status": false,
  "message": "Invalid data provided",
  "errors": {
    "field_name": [
      "Error message"
    ]
  }
}
```
