import json
from logging import exception
import os
from pyexpat import model
import sys
import pandas as pd
import uuid
from wyre import settings
from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON>, BaseUserManager
from django.utils import timezone
from django.core.paginator import Paginator, EmptyPage
from main.scripts import time_helpers
# from main.models import Reading, Device, Datalog, Cost, FuelConsumption 
import main, datetime
from django.db.models import Avg, Sum, Min, Max
from django.db.models.functions import TruncMonth
from dateutil.relativedelta import relativedelta
from django.utils.translation import gettext_lazy as _
from django.core.validators import EmailValidator
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from datetime import timedelta
from django.db.models import Sum, Avg, Min, Max
from decimal import Decimal
# Create your models here.

class Client(models.Model):

    TYPE_CHOICES = (
                    ("BESPOKE", "BESPOKE"),
                    ("STANDARD", "STANDARD"),
                    ("RESELLER", "RESELLER"),
                    ("BULK_MONITORING", "BULK_MONITORING"),
                    ("WYRE", "WYRE"),
                )

    name = models.CharField(max_length=20)
    logo = models.ImageField(upload_to='images/', null=True)
    logo_url = models.CharField(max_length=250, null=True)
    client_type = models.CharField(max_length=15, choices=TYPE_CHOICES, default="STANDARD")
    phone_number = models.CharField(max_length=20, default="0")
    email = models.EmailField(null=True, max_length = 100)
    address = models.CharField(null=True, max_length=250)

    # New field for multiple emails
    additional_emails = ArrayField(
        models.EmailField(validators=[EmailValidator()]),
        blank=True,
        null=True,
        help_text="List of additional email addresses for this client"
    )
    is_active = models.BooleanField(default=True, null=True, blank=True, help_text="Set to False to suspend this client.")

    def __str__(self):
        return f"Client {self.id} - {self.name}"

    @property
    def number_of_branches(self):
        branches = main.models.Branch.objects.filter(client = self)
        number_of_branches = len(branches)

        return  number_of_branches

    def get_all_emails(self):
        """Returns a list of all email addresses associated with this client."""
        all_emails = [self.email] if self.email else []
        if self.additional_emails:
            all_emails.extend(self.additional_emails)
        return all_emails

    def total_energy(self, start_date, end_date):

        devices = main.models.Device.objects.filter(client=self)
        total_energy = []
        total_diesel = []
        total_utility = []

        for device in devices:
            energy = device.get_total_kwh_for_period(start_date, end_date)
            total_energy.append(energy)

            if device.type == 'GENERATOR' or 'LOAD' or 'Water Area' or 'Swimming Pool':
                diesel = device.get_total_kwh_for_period(start_date, end_date)
                total_diesel.append(diesel)

            if device.type == 'IPP' or 'UTILITY':
                utility = device.get_total_kwh_for_period(start_date, end_date)
                total_utility.append(utility)

        total_energy = round(sum(total_energy), 2)
        total_diesel = round(sum(total_diesel), 2)
        total_utility = round(sum(total_utility), 2)

        data = {
            'total_energy' : total_energy,
            'total_diesel' : total_diesel,
            'total_utility' : total_utility,
        }

        return data

    # def header_endpoint_total_energy(self, start_date, end_date):

    #     branches = main.models.Branch.objects.filter(client=self, is_active=True)

    #     total_energy = 0

    #     for branch in branches:

    #         energy = branch.client_admin_header_endpoint_total_energy(start_date, end_date).get('total_energy')
    #         total_energy += energy

    #     return total_energy

    # def header_endpoint_total_co2(self, start_date, end_date):

    #     branches = main.models.Branch.objects.filter(client=self, is_active=True)

    #     total_co2 = 0

    #     for branch in branches:

    #         co2 = branch.get_carbon_emmisions(start_date, end_date)
    #         total_co2 += co2

    #     return total_co2

    def header_endpoint_total_energy(self, start_date, end_date):
        """
        Optimized: Bulk fetch kWh for all devices in all active branches for this client.
        """
        from main.models import Branch, Device, Datalog
        from django.db.models import Min, Max

        branches = Branch.objects.filter(client=self, is_active=True)
        devices = Device.objects.filter(branch__in=branches)
        device_ids = [d.id for d in devices]

        dql = (
            Datalog.objects
            .filter(device_id__in=device_ids, post_datetime__gte=start_date, post_datetime__lte=end_date)
            .values("device_id")
            .annotate(min_val=Min("summary_energy_register_1"), max_val=Max("summary_energy_register_1"))
        )
        total_energy = sum((row["max_val"] or 0) - (row["min_val"] or 0) for row in dql)
        return round(total_energy, 2)

    def header_endpoint_total_co2(self, start_date, end_date):
        """
        Optimized: Bulk fetch kWh for all devices in all active branches, then sum CO2 by device type.
        """
        from main.models import Branch, Device, Datalog
        # from django.db.models import Min, Max

        branches = Branch.objects.filter(client=self, is_active=True)
        devices = list(Device.objects.filter(branch__in=branches).select_related('type'))
        device_ids = [d.id for d in devices]
        type_map = {d.id: d.type.choice_name.lower() for d in devices}

        dql = (
            Datalog.objects
            .filter(device_id__in=device_ids, post_datetime__gte=start_date, post_datetime__lte=end_date)
            .values("device_id")
            .annotate(min_val=Min("summary_energy_register_1"), max_val=Max("summary_energy_register_1"))
        )
        kwh_map = {row["device_id"]: (row["min_val"] or 0, row["max_val"] or 0) for row in dql}

        total_co2 = 0.0
        for device in devices:
            mn, mx = kwh_map.get(device.id, (0, 0))
            kwh = mx - mn
            typ = type_map[device.id]
            if typ == "ipp":
                co2 = (main.CO2_KG_EMMISSIONS_PER_KWH_LNG * kwh) / 1000
            elif typ == "generator":
                co2 = (main.CO2_KG_EMMISSIONS_PER_KWH_DIESEL * kwh) / 1000
            elif typ == "utility":
                co2 = (main.CO2_KG_EMMISSIONS_PER_KWH_UTILITY * kwh) / 1000
            else:
                co2 = 0
            total_co2 += co2

        return round(total_co2, 2)


    def header_endpoint_total_utility_cost(self, start_date, end_date):
        """
        Calculates the total actual utility cost (from Cost table)
        and total calculated cost (from readings * dynamic tariff) for all active branches
        between start_date and end_date.
        Optimized to reduce DB hits by bulk fetching readings.
        """
        import main.models
        # Prefetch devices for all branches in one query
        branches = main.models.Branch.objects.filter(client=self, is_active=True).prefetch_related(
            models.Prefetch('device_set', queryset=main.models.Device.objects.select_related('type'))
        )

        # Prefetch all utility costs for all branches in one query
        cost_qs = main.models.Cost.objects.filter(
            branch__in=branches,
            date__gte=start_date,
            date__lte=end_date,
            cost_type__iexact="utility"
        )
        cost_map = {}
        for cost in cost_qs:
            cost_map.setdefault(cost.branch_id, 0)
            cost_map[cost.branch_id] += cost.amount or 0

        # --- Bulk fetch all utility devices ---
        utility_devices = []
        branch_utility_map = {}
        for branch in branches:
            for d in branch.device_set.all():
                if getattr(d.type, "choice_name", "").upper() == "UTILITY":
                    utility_devices.append(d.id)
                    branch_utility_map[branch.id] = d

        # --- Bulk fetch all readings for all utility devices ---
        readings_qs = main.models.Reading.objects.filter(
            device_id__in=utility_devices,
            post_datetime__gte=start_date,
            post_datetime__lte=end_date
        ).values('device_id').annotate(
            min_reading=Min('kwh_import'),
            max_reading=Max('kwh_import')
        )
        readings_map = {r['device_id']: (r['min_reading'], r['max_reading']) for r in readings_qs}

        total_actual_cost = 0.0
        total_calculated_cost = 0.0

        for branch in branches:
            # --- Actual cost from Cost table ---
            actual_cost = cost_map.get(branch.id, 0)

            # --- Calculated cost using dynamic tariff ---
            utility_device = branch_utility_map.get(branch.id)
            if utility_device:
                min_r, max_r = readings_map.get(utility_device.id, (None, None))
                if min_r is not None and max_r is not None:
                    total_energy = max_r - min_r
                else:
                    total_energy = 0

                # Get hours of use for the device
                hours_of_use = utility_device.get_hourly_time_of_use(start_date, end_date)
                # Get dynamic tariff for the branch/device
                band, tariff = branch.get_branch_band_from_utility_time_of_use(hours_of_use)
                calculated_cost = total_energy * tariff
            else:
                calculated_cost = 0

            total_actual_cost += actual_cost
            total_calculated_cost += calculated_cost

        return {
            "total_actual_cost": round(total_actual_cost, 2),
            "total_calculated_cost": round(total_calculated_cost, 2)
        }

    def header_endpoint_total_diesel_cost(self, start_date, end_date):
        """
        Calculates the total actual diesel cost (from FuelConsumption and Cost tables)
        and total calculated (optimal) diesel cost (from device readings × DIESEL_LTR_PER_KWH)
        for all active branches between start_date and end_date.
        Optimized: bulk fetch generator readings for all devices.
        """
        import main.models
        DIESEL_LTR_PER_KWH = 0.4

        # Prefetch devices for all branches in one query
        branches = main.models.Branch.objects.filter(client=self, is_active=True).prefetch_related(
            models.Prefetch('device_set', queryset=main.models.Device.objects.select_related('type'))
        )

        # Prefetch all fuel consumptions and diesel costs for all branches in one query
        fuel_entries = main.models.FuelConsumption.objects.filter(
            branch__in=branches,
            start_date__gte=start_date,
            end_date__lte=end_date
        )
        fuel_map = {}
        for entry in fuel_entries:
            fuel_map.setdefault(entry.branch_id, 0)
            fuel_map[entry.branch_id] += entry.quantity or 0

        cost_qs = main.models.Cost.objects.filter(
            branch__in=branches,
            cost_type__iexact="diesel",
            date__gte=start_date,
            date__lte=end_date
        )
        price_map = {}
        for cost in cost_qs:
            price_map.setdefault(cost.branch_id, []).append(cost.price_per_litre or 0)

        # --- Bulk fetch all generator devices ---
        generator_devices = []
        branch_generators_map = {}
        for branch in branches:
            gens = [d for d in branch.device_set.all() if getattr(d.type, "choice_name", "").lower() == "generator"]
            generator_devices.extend([g.id for g in gens])
            branch_generators_map[branch.id] = gens

        # --- Bulk fetch all readings for all generator devices ---
        from django.db.models import Min, Max
        readings_qs = main.models.Datalog.objects.filter(
            device_id__in=generator_devices,
            post_datetime__gte=start_date,
            post_datetime__lte=end_date
        ).values('device_id').annotate(
            min_val=Min('summary_energy_register_1'),
            max_val=Max('summary_energy_register_1')
        )
        readings_map = {r['device_id']: (r['min_val'], r['max_val']) for r in readings_qs}

        total_actual_cost = 0.0
        total_calculated_cost = 0.0

        for branch in branches:
            # --- Actual cost from FuelConsumption and Cost ---
            actual_usage = fuel_map.get(branch.id, 0.0)
            prices = price_map.get(branch.id, [])
            avg_price = sum(prices) / len(prices) if prices else 1100  # fallback price per litre
            actual_cost = actual_usage * avg_price

            # --- Calculated (optimal) cost ---
            total_optimal_usage = 0.0
            for device in branch_generators_map.get(branch.id, []):
                min_r, max_r = readings_map.get(device.id, (None, None))
                if min_r is not None and max_r is not None:
                    device_energy = max_r - min_r
                else:
                    device_energy = 0
                optimal = device_energy * DIESEL_LTR_PER_KWH
                total_optimal_usage += optimal

            calculated_cost = total_optimal_usage * avg_price

            total_actual_cost += actual_cost
            total_calculated_cost += calculated_cost

        return {
            "total_actual_cost": round(total_actual_cost, 2),
            "total_calculated_cost": round(total_calculated_cost, 2)
        }

    def client_branches_energy(self, start_date, end_date, current_page, items_per_page):
        branches = main.models.Branch.objects.filter(client=self, is_active=True).order_by('name')

        # Create a paginator for the branches
        paginator = Paginator(branches, items_per_page)

        try:
            # Get the specified page of branches
            paged_branches = paginator.page(current_page)
        except EmptyPage:
            paged_branches = paginator.page(1)

        values = []

        for branch in paged_branches:
            devices = main.models.Device.objects.filter(branch=branch)
            generators = [device for device in devices if device.is_gen]
            utility = [device for device in devices if device.is_utility]

            generators_energy = [device.get_total_kwh_for_period(start_date, end_date) for device in generators]
            generators_energy_sum = round(sum(generators_energy), 2)

            utility_energy = [device.get_total_kwh_for_period(start_date, end_date) for device in utility]
            utility_energy_sum = round(sum(utility_energy), 2)

            value = dict(name = branch.name,
                         generators_energy = generators_energy_sum,
                         utility_energy = utility_energy_sum)

            values.append(value)

        return {
            "chart": values,
            "pagination": {
                "total_items": branches.count(),
                "items_per_page": items_per_page,
                "total_pages": paginator.num_pages,
                "current_page": current_page,
            }
        }


    def monthly_utility_cost(self, month, year):
        """
        Returns a dict:
        {
            "branches": [
                {
                    "branch_id": ...,
                    "branch_name": ...,
                    "client_cost": ...,
                    "wyre_cost": ...,
                    "historic_average": ...,
                    "tariff": ...,
                    "band": ...,
                },
                ...
            ],
            "average_cost": ...
        }
        For the given month/year.
        """
        import datetime

        branches = main.models.Branch.objects.filter(client=self, is_active=True)
        devices = main.models.Device.objects.filter(client=self, type__choice_name__iexact="UTILITY")
        start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(datetime.datetime(year, month, 1))

        # Pre-aggregate costs for all branches
        costs = (
            main.models.Cost.objects
            .filter(branch__in=branches, date__year=year, date__month=month, cost_type="utility")
            .values('branch_id')
            .annotate(total=Sum('amount'))
        )
        cost_map = {c['branch_id']: c['total'] or 0 for c in costs}

        # Pre-aggregate readings for all utility devices by branch
        device_branch_map = {d.id: d.branch_id for d in devices}
        device_readings = (
            main.models.Reading.objects
            .filter(device__in=devices, post_datetime__gte=start_date, post_datetime__lte=end_date)
            .values('device_id')
            .annotate(
                min_val=Min('kwh_import'),
                max_val=Max('kwh_import')
            )
        )
        branch_energy_map = {}
        for r in device_readings:
            branch_id = device_branch_map.get(r['device_id'])
            if branch_id is not None:
                diff = (r['max_val'] or 0) - (r['min_val'] or 0)
                branch_energy_map[branch_id] = branch_energy_map.get(branch_id, 0) + diff

        # Build result per branch
        result = []
        for branch in branches:
            client_cost = Decimal(cost_map.get(branch.id, 0))
            utility_energy_sum = Decimal(branch_energy_map.get(branch.id, 0))

            # Get time of use from the DataFrame
            tou_df = branch.get_time_of_use_raw_dataframe(start_date, end_date)
            utility_device = branch.device_set.filter(type__choice_name__iexact="utility").first()
            if utility_device and isinstance(tou_df, pd.DataFrame):
                hours_col = f"{utility_device.name}_hours"
                time_of_use = tou_df[hours_col].sum() if hours_col in tou_df else 0
            else:
                time_of_use = 0

            # Get dynamic tariff and band for this branch and period
            band, tariff = branch.get_branch_band_from_utility_time_of_use(time_of_use)
            tariff = Decimal(tariff)

            wyre_cost = utility_energy_sum * tariff
            average_cost = (client_cost + wyre_cost) / 2
            result.append({
                'branch_id': branch.id,
                'branch_name': branch.name,
                'phcn_cost': round(client_cost, 2),
                'wyre_cost': round(wyre_cost, 2),
                'average_cost': round(average_cost, 2),
                # 'tariff': float(tariff),
                # 'band': band,
            })

        # Calculate average cost (average of all branch historic averages)
        historic_average = round(sum(b['average_cost'] for b in result) / len(result), 2) if result else 0
               
        return {
            'branches': result,
            'historic_average': historic_average
        }


    def utility_cost(self, year=None):
        """
        Calculates utility costs for either the past 12 months or a specific year.
        Uses dynamic avg_tariff per month.
        """
        date_now = datetime.datetime.now()

        if year:
            year = int(year)
            months_to_calculate = [(month, year) for month in range(1, 13)]
        else:
            months_to_calculate = [
                ((date_now - relativedelta(months=i)).month, (date_now - relativedelta(months=i)).year)
                for i in range(11, -1, -1)
            ]

        branches = main.models.Branch.objects.filter(client=self, is_active=True)
        devices = main.models.Device.objects.filter(client=self, type__choice_name__iexact="UTILITY")
        cost_overview = []

        for month, year in months_to_calculate:
            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(
                datetime.datetime(year, month, 1)
            )
            month_name = start_date.strftime("%b, %Y")

            # Calculate client costs
            branch_usage = branches.filter(
                cost__date__month=month,
                cost__date__year=year
            ).aggregate(
                total_amount=Sum('cost__amount'),
                total_units=Sum('cost__value'),
                average_tariff=Avg('cost__tarrif')
            )
            client_cost = branch_usage.get('total_amount') or 0

            # Calculate utility readings
            monthly_readings = main.models.Reading.objects.filter(
                device__in=devices,
                post_datetime__year=year,
                post_datetime__month=month
            ).aggregate(
                first_reading=Min('kwh_import'),
                last_reading=Max('kwh_import')
            )

            if monthly_readings['first_reading'] is not None and monthly_readings['last_reading'] is not None:
                utility_energy_sum = monthly_readings['last_reading'] - monthly_readings['first_reading']
            else:
                utility_energy_sum = 0

            # --- Dynamic avg_tariff calculation ---
            tariffs = []
            for branch in branches:
                utility_device = branch.device_set.filter(type__choice_name__iexact="utility").first()
                if utility_device:
                    tou_df = branch.get_time_of_use_raw_dataframe(start_date, end_date)
                    if isinstance(tou_df, pd.DataFrame):
                        hours_col = f"{utility_device.name}_hours"
                        time_of_use = tou_df[hours_col].sum() if hours_col in tou_df else 0
                    else:
                        time_of_use = 0
                    _, tariff = branch.get_branch_band_from_utility_time_of_use(time_of_use)
                    tariffs.append(float(tariff))
            avg_tariff = sum(tariffs) / len(tariffs) if tariffs else 209.05  # fallback Band A if no data

            wyre_cost = utility_energy_sum * avg_tariff
            average_cost = (client_cost + wyre_cost) / 2

            cost_overview.append({
                'month': month_name,
                'phcn_cost': round(client_cost, 2),
                'wyre_cost': round(wyre_cost, 2),
                'average_cost': round(average_cost, 2),
                # 'avg_tariff': round(avg_tariff, 2),
            })

        return {
            'cost_overview': cost_overview,
            'historic_average': round(sum(entry['average_cost'] for entry in cost_overview) / len(cost_overview), 2)
        }



    def monthly_diesel_cost(self, month, year):
        """
        Returns a list of dicts: [{branch_id, branch_name, diesel_cost, calculated_cost, avg_price, actual_litres, optimal_litres}, ...]
        for the given month/year, using both actual and calculated (optimal) diesel usage.
        """
        import main.models
        from django.db.models import Min, Max
        DIESEL_LTR_PER_KWH = 0.4

        # Get all active branches and generator devices
        branches = main.models.Branch.objects.filter(client=self, is_active=True).prefetch_related(
            models.Prefetch('device_set', queryset=main.models.Device.objects.select_related('type'))
        )

        # Prepare generator device lists per branch
        generator_devices = []
        branch_generators_map = {}
        for branch in branches:
            gens = [d for d in branch.device_set.all() if getattr(d.type, "choice_name", "").lower() == "generator"]
            generator_devices.extend([g.id for g in gens])
            branch_generators_map[branch.id] = gens

        # Bulk fetch diesel costs for all branches for the month
        cost_qs = main.models.Cost.objects.filter(
            branch__in=branches,
            cost_type__iexact="diesel",
            date__year=year,
            date__month=month
        )
        price_map = {}
        for cost in cost_qs:
            price_map.setdefault(cost.branch_id, []).append(cost.price_per_litre or 0)

        # Bulk fetch fuel consumptions for all branches for the month
        fuel_entries = main.models.FuelConsumption.objects.filter(
            branch__in=branches,
            start_date__year=year,
            start_date__month=month
        )
        fuel_map = {}
        for entry in fuel_entries:
            fuel_map.setdefault(entry.branch_id, 0)
            fuel_map[entry.branch_id] += entry.quantity or 0

        # Bulk fetch generator readings for all devices for the month
        readings_qs = main.models.Datalog.objects.filter(
            device_id__in=generator_devices,
            post_datetime__year=year,
            post_datetime__month=month
        ).values('device_id').annotate(
            min_val=Min('summary_energy_register_1'),
            max_val=Max('summary_energy_register_1')
        )
        readings_map = {r['device_id']: (r['min_val'], r['max_val']) for r in readings_qs}

        result = []
        for branch in branches:
            # Actual usage and price for this branch/month
            actual_litres = fuel_map.get(branch.id, 0.0)
            prices = price_map.get(branch.id, [])
            avg_price = sum(prices) / len(prices) if prices else 1100  # fallback price per litre
            diesel_cost = actual_litres * avg_price

            # Calculated (optimal) usage and cost for this branch/month
            total_optimal_usage = 0.0
            for device in branch_generators_map.get(branch.id, []):
                min_r, max_r = readings_map.get(device.id, (None, None))
                if min_r is not None and max_r is not None:
                    device_energy = max_r - min_r
                else:
                    device_energy = 0
                optimal = device_energy * DIESEL_LTR_PER_KWH
                total_optimal_usage += optimal

            calculated_cost = total_optimal_usage * avg_price

            result.append({
                'branch_id': branch.id,
                'branch_name': branch.name,
                'client_cost': round(diesel_cost, 2),
                'wyre_cost': round(calculated_cost, 2),
                # 'avg_price': round(avg_price, 2),
                # 'actual_litres': round(actual_litres, 2),
                # 'optimal_litres': round(total_optimal_usage, 2),
            })
        return result
    

    def diesel_cost(self, year=None):
        """
        Calculates the diesel costs for either the past 12 months including the current month
        or for a specific year if provided, using actual fuel consumption and generator readings.
        Returns a monthly breakdown and the average.
        """
        import main.models
        from django.db.models import Min, Max
        DIESEL_LTR_PER_KWH = 0.4

        date_now = datetime.datetime.now()
        if year:
            year = int(year)
            months_to_calculate = [(month, year) for month in range(1, 13)]
        else:
            months_to_calculate = [
                ((date_now - relativedelta(months=i)).month, (date_now - relativedelta(months=i)).year)
                for i in range(11, -1, -1)
            ]

        branches = main.models.Branch.objects.filter(client=self, is_active=True).prefetch_related(
            models.Prefetch('device_set', queryset=main.models.Device.objects.select_related('type'))
        )

        # Prefetch all generator devices for all branches
        generator_devices = []
        branch_generators_map = {}
        for branch in branches:
            gens = [d for d in branch.device_set.all() if getattr(d.type, "choice_name", "").lower() == "generator"]
            generator_devices.extend([g.id for g in gens])
            branch_generators_map[branch.id] = gens

        # Prefetch all diesel costs for all branches (for all months in range)
        cost_qs = main.models.Cost.objects.filter(
            branch__in=branches,
            cost_type__iexact="diesel"
        )
        price_map = {}
        for cost in cost_qs:
            price_map.setdefault((cost.branch_id, cost.date.month, cost.date.year), []).append(cost.price_per_litre or 0)

        # Prefetch all fuel consumptions for all branches (for all months in range)
        fuel_entries = main.models.FuelConsumption.objects.filter(
            branch__in=branches
        )
        fuel_map = {}
        for entry in fuel_entries:
            key = (entry.branch_id, entry.start_date.month, entry.start_date.year)
            fuel_map.setdefault(key, 0)
            fuel_map[key] += entry.quantity or 0

        # Prefetch all generator readings for all devices (for all months in range)
        readings_qs = main.models.Datalog.objects.filter(
            device_id__in=generator_devices
        ).values('device_id', 'post_datetime__month', 'post_datetime__year').annotate(
            min_val=Min('summary_energy_register_1'),
            max_val=Max('summary_energy_register_1')
        )
        readings_map = {}
        for r in readings_qs:
            key = (r['device_id'], r['post_datetime__month'], r['post_datetime__year'])
            readings_map[key] = (r['min_val'], r['max_val'])

        cost_overview = []
        monthly_means = []

        for month, year in months_to_calculate:
            month_name = datetime.datetime(year, month, 1).strftime('%b, %Y')
            monthly_cost = 0
            calculated_cost = 0

            for branch in branches:
                # Actual usage and price for this branch/month
                actual_usage = fuel_map.get((branch.id, month, year), 0.0)
                prices = price_map.get((branch.id, month, year), [])
                avg_price = sum(prices) / len(prices) if prices else 1100  # fallback price per litre
                actual_cost = actual_usage * avg_price

                # Calculated (optimal) cost for this branch/month
                total_optimal_usage = 0.0
                for device in branch_generators_map.get(branch.id, []):
                    min_r, max_r = readings_map.get((device.id, month, year), (None, None))
                    if min_r is not None and max_r is not None:
                        device_energy = max_r - min_r
                    else:
                        device_energy = 0
                    optimal = device_energy * DIESEL_LTR_PER_KWH
                    total_optimal_usage += optimal

                calculated_cost += total_optimal_usage * avg_price
                monthly_cost += actual_cost

            # Compute mean for this month
            monthly_mean = (monthly_cost + calculated_cost) / 2
            monthly_means.append(monthly_mean)

            cost_overview.append({
                'month': month_name,
                'client_cost': round(monthly_cost, 2),
                'wyre_cost': round(calculated_cost, 2)
            })

        average_diesel_purchase = sum(monthly_means) / len(monthly_means) if monthly_means else 0

        return {
            'historical_avg': round(average_diesel_purchase, 2),
            'cost_overview': cost_overview
        }
    

    def monthly_diesel_litres(self, month, year):
        """
        Returns a list of dicts: [{branch_id, branch_name, diesel_litres}, ...] for the given month/year.
        """
        import main.models

        branches = main.models.Branch.objects.filter(client=self, is_active=True)

        # Aggregate diesel litres for all branches in one query (from FuelConsumption)
        fuel_entries = (
            main.models.FuelConsumption.objects
            .filter(branch__in=branches, start_date__year=year, start_date__month=month)
            .values('branch_id')
            .annotate(total=Sum('quantity'))
        )
        litres_map = {f['branch_id']: f['total'] or 0 for f in fuel_entries}

        result = []
        for branch in branches:
            result.append({
                'branch_id': branch.id,
                'branch_name': branch.name,
                'diesel_litres': round(litres_map.get(branch.id, 0), 2)
            })
        return result

    def diesel_litres(self, year=None):
        """
        Calculates the diesel litres used for either the past 12 months including the current month
        or for a specific year if provided.
        """

        # Determine the current date
        date_now = datetime.datetime.now()

        if year:
            # If a specific year is provided, set the year
            year = int(year)
            months_to_calculate = [(month, year) for month in range(1, 13)]
        else:
            # If no year is provided, calculate the last 12 months including the current month
            months_to_calculate = [
                ((date_now - relativedelta(months=i)).month, (date_now - relativedelta(months=i)).year)
                for i in range(11, -1, -1)
            ]

        branches = main.models.Branch.objects.filter(client=self, is_active=True)

        diesel_overview = []
        total_sum = 0

        # Loop through the months and calculate diesel litres
        for month, year in months_to_calculate:
            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(
                datetime.datetime(year, month, 1)
            )
            month_name = start_date.strftime("%b, %Y")

            branch_usage = branches.annotate(
                total_quantity=Sum('cost__quantity'),
            ).filter(cost__date__month=month, cost__date__year=year)

            # Sum up diesel litres for the month
            diesel_litres = branch_usage.aggregate(Sum('total_quantity'))['total_quantity__sum'] or 0
            total_sum += diesel_litres

            diesel_overview.append({
                'month': month_name,
                'diesel_litres': round(diesel_litres, 2),
            })

        # Calculate the average diesel litres
        average_diesel_litres = total_sum / len(diesel_overview) if diesel_overview else 0

        return {
            'average_diesel_litres': round(average_diesel_litres, 2),
            'diesel_overview': diesel_overview
        }
    
        
    def monthly_utility_energy(self, month, year):
        """
        Returns a list of dicts: [{branch_id, branch_name, utility_energy}, ...] for the given month/year.
        """
        import main.models

        branches = main.models.Branch.objects.filter(client=self, is_active=True)
        devices = main.models.Device.objects.filter(client=self, branch__in=branches, type__choice_name__iexact="UTILITY")

        # Bulk fetch readings for all utility devices for the month
        from django.db.models import Min, Max

        readings = (
            main.models.Reading.objects
            .filter(device__in=devices, post_datetime__year=year, post_datetime__month=month)
            .values('device_id')
            .annotate(
                min_val=Min('kwh_import'),
                max_val=Max('kwh_import')
            )
        )
        # Map device_id to branch_id
        device_branch_map = {d.id: d.branch_id for d in devices}
        branch_energy_map = {}
        for r in readings:
            branch_id = device_branch_map.get(r['device_id'])
            if branch_id is not None:
                diff = (r['max_val'] or 0) - (r['min_val'] or 0)
                branch_energy_map[branch_id] = branch_energy_map.get(branch_id, 0) + diff

        result = []
        for branch in branches:
            result.append({
                'branch_id': branch.id,
                'branch_name': branch.name,
                'utility_energy': round(branch_energy_map.get(branch.id, 0), 2)
            })
        return result
    

class UserManager(BaseUserManager):

    use_in_migrations = True
    
    def get_queryset(self):
        return super().get_queryset().defer('cache_data')


    def create_user(self, username, email=None, password=None, **extra_fields):
        if not username:
            raise ValueError("The username must be set")
        email = self.normalize_email(email)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get('is_superuser') is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self.create_user(username, email, password, **extra_fields)

class User(AbstractUser):

    SUPERADMIN = 1
    ADMIN      = 2
    CLIENT_ADMIN    = 3
    OPERATOR    = 4
    VIEWER    = 5

    ROLES_DICT = {SUPERADMIN:"SUPERADMIN", ADMIN:"ADMIN", CLIENT_ADMIN:"CLIENT_ADMIN", OPERATOR:"OPERATOR", VIEWER:"VIEWER"}

    ROLE_CHOICES = (
        (ADMIN, 'ADMIN'),
        (CLIENT_ADMIN, 'CLIENT_ADMIN'),
        (SUPERADMIN, 'SUPERADMIN'),
        (OPERATOR, 'OPERATOR'),
        (VIEWER, 'VIEWER'),
    )

    client       = models.ForeignKey(Client, on_delete=models.CASCADE, null=True, related_name='client')
    email = models.EmailField(null=True, max_length = 100)
    phone_number = models.CharField(max_length=20, default="0")
    is_ceo                       = models.BooleanField(default=False)
    is_ppl_staff                 = models.BooleanField(default=False)
    can_set_gen_maintenance_date = models.BooleanField(default=False)
    can_alter_alert_parameters   = models.BooleanField(default=False)
    cache_data = models.JSONField(null=True, blank=True)
    roles = models.PositiveSmallIntegerField(choices=ROLE_CHOICES, default=4)
    has_no_password = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)

    objects = UserManager()

    def __str__(self):
        return self.username

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if self.has_no_password:
            self.set_password(self.password)
            self.has_no_password = False

        super(User, self).save(*args, **kwargs)

    @property
    def role_text(self):
        return self.ROLES_DICT[self.roles]

    @property
    def client_detail(self):
        client = Client.objects.get(id = self.client.id)

        return  {"id":client.id,"name":client.name}

    def set_is_active(self, is_active):
        self.is_active = is_active
        self.save()

    def get_permitted_devices(self):

        view_permissions = self.view_permissions.all()
        permitted_devices = []

        for view_permission in view_permissions:

            branch = view_permission.branch
            devices = branch.device_set.all()

            for device in devices:
                permitted_devices.append(dict(device_name = device.name,
                                                device_id = device.id,
                                                device_branch = device.branch.name))

        return permitted_devices

    @property
    def get_permitted_branches(self):

        view_permissions = self.view_permissions.all()
        permitted_branches = []

        for view_permission in view_permissions:
            branch = view_permission.branch
            permitted_branches.append(branch)

        return permitted_branches


    def load_cache_data(self):

        # if not self.id == 4 : return

        try:
            frequency = 'hourly'
            start_date = timezone.now()
            end_date = timezone.now()
            frequency = frequency.lower()

            user = self

            permitted_views = user.view_permissions.all()

            reprocessed_dates = time_helpers.get_start_and_end_of_month_from_date(end_date)
            start_of_month = reprocessed_dates[0]
            start_date = start_of_month
            end_date = reprocessed_dates[1]


            data = {
                    'status'  : True,
                    'message' : "Successful",
                    'authenticatedData' : {
                        'id' : user.client.id,
                        "user_id":user.id,
                        'name'  :  user.client.name,
                        'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                        "score_cards_date": time_helpers.score_cards_date(end_date),
                        'branches': [
                            {
                                "name": permitted_view.branch.name,
                                "branch_id": permitted_view.branch.id,
                                "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       'dashboard': {
                                            "total_kwh": {
                                            "unit": "kWh",
                                            "value": round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
                                            },
                                            "min_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("min"), settings.DECIMAL_PLACES)
                                            },
                                            "max_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("max"), settings.DECIMAL_PLACES)
                                            },
                                            "avg_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("avg"), settings.DECIMAL_PLACES)
                                            },
                                            "dashboard_carbon_emissions": {
                                                "unit": "Tons",
                                                "value": round(device.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value"), 2)
                                            },
                                            "cost_of_energy": {
                                                "unit": "Naira/KWh",
                                                "value": device.get_cost_of_energy(start_date, end_date)
                                            },
                                            "today": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("today_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "yesterday": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("yesterday_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "solar_hours":{
                                                "value":device.get_solar_hours_consumption(start_date, end_date),
                                                "unit":"kwh"
                                            }
                                       },
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                    "daily_kwh": permitted_view.branch.get_periodic_device_usage(start_date, end_date, "daily"),
                                    "usage_hours": permitted_view.branch.get_hours_of_use(start_date, end_date),
                                    "time_of_use_table": permitted_view.branch.get_time_of_use(start_date, end_date),
                                    "billing_totals": {
                                        "previous_total": {
                                            "usage_kwh": sum([device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["usage_kwh"] for device in permitted_view.branch.device_set.all()]),
                                            "value_naira": sum([device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["value_naira"] for device in permitted_view.branch.device_set.all()]),
                                        },
                                        "present_total": {
                                            "usage_kwh": sum([device.get_billing_data(start_date, end_date)["totals"]["present_total"]["usage_kwh"] for device in permitted_view.branch.device_set.all()]),
                                            "value_naira": sum([device.get_billing_data(start_date, end_date)["totals"]["present_total"]["value_naira"] for device in permitted_view.branch.device_set.all()]),
                                        },
                                        "metrics": permitted_view.branch.get_cost_of_per_kwh(start_date, end_date),
                                        "usage": {
                                            "previous_kwh": 0,
                                            "present_kwh": 0,
                                            "total_usage_kwh": 0
                                        }
                                    },
                                    "id": branch_index+1,
                            }
                            for branch_index, permitted_view in enumerate(permitted_views)
                        ]
                    }
                }

            # print(data)
            # file = open("file.txt", "w")
            # file.write(str(data))
            user.cache_data = json.dumps(data, indent=4, sort_keys=True, default=str)
            user.save()

        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            print(exc_type, fname, exc_tb.tb_lineno)

class TimeAndUUIDStampedBaseModel(models.Model):
    """
    Base model class that contains special fields other model classes
    will subclass from
    Fields:
        created_at (DateTime): Time at which the object was created
        updated_at (Datetime): Time at which the object was updated
        uuid (String): UUID representing ID of each model
    """

    created_at = models.DateTimeField(default=timezone.now, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    uuid = models.UUIDField(default=uuid.uuid4, editable=False)

    class Meta:
        abstract = True

class SupportTicket(TimeAndUUIDStampedBaseModel):
    """
    Database schema for Support Ticket model.

    Fields:
        - id (UUID): Unique identifier for the support ticket.
        - subject (CharField): The subject of the support ticket.
        - priority (TextChoices): The suport ticket priority types.
        - description (TextField): The description of the support ticket.
        - status (TextChoices): The resolution status types of the support ticket.
        - user (User): The creator of the support ticket.
        - created_at (Datetime): Time at which the invite was created.
        - updated_at (Datetime): Time at which the invite was last updated.
    """

    class SupportTicketPriority(models.TextChoices):
        URGENT = "Urgent", _("Urgent")
        NORMAL = "Normal", _("Normal")

    class SupportTicketStatus(models.TextChoices):
        PENDING = "Pending", _("Pending")
        RESOLVED = "Resolved", _("Resolved")

    subject = models.CharField(
        max_length=512, help_text="The subject of the support ticket"
    )
    priority = models.CharField(
        max_length=20,
        choices=SupportTicketPriority.choices,
        default=SupportTicketPriority.URGENT,
        help_text="The suport ticket priority types",
    )
    description = models.TextField(help_text="The description of the support ticket")
    status = models.CharField(
        max_length=20,
        choices=SupportTicketStatus.choices,
        default=SupportTicketStatus.PENDING,
        help_text="The resolution status types of the support ticket",
    )
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="The client",
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        help_text="The creator of the support ticket",
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self) -> str:
        return f"{self.id} {self.user}"
