from rest_framework import serializers
from account.models import Client, User, SupportTicket
from main.models import Branch, Device, DeviceType, ViewPermission, Region
from django.contrib.auth.hashers import make_password
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer


class ClientSerializer(serializers.ModelSerializer):

    class Meta:
        model = Client
        read_only_fields = ["total_energy"]
        fields = '__all__'

class UserSerializer(serializers.ModelSerializer):
    roles = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id", "last_login", "username", "first_name", "last_name", "email", "is_active", "phone_number", "date_joined", "roles", "client"
        ]

    def get_roles(self, obj):
        return {
            "id": obj.roles,
            "name": obj.ROLES_DICT.get(obj.roles, "UNKNOWN")
        }


class RegionCreateSerializer(serializers.Serializer):
    region = serializers.CharField(max_length=200)

class DeviceTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceType
        fields = ['id', 'choice_name']

class DeviceCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        fields = ['name', 'type', 'is_load', 'provider', 'gen_size', 'device_id', 'fuel_type', 'is_source', 'operating_hours_start', 'operating_hours_end']

class BranchCreateSerializer(serializers.ModelSerializer):
    devices = DeviceCreateSerializer(many=True, required=False)
    region = serializers.PrimaryKeyRelatedField(queryset=Region.objects.all(), required=False, allow_null=True)

    class Meta:
        model = Branch
        fields = ['name', 'address', 'email', 'devices', 'copy_email', 'region']

    def update(self, instance, validated_data):
        # Update branch fields (not devices)
        devices_data = validated_data.pop('devices', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        # Only add new devices if provided
        if devices_data:
            for device_data in devices_data:
                # Accept both type id and DeviceType instance
                device_type = device_data.get('type')
                if isinstance(device_type, int):
                    device_type_obj = DeviceType.objects.filter(id=device_type).first()
                elif isinstance(device_type, DeviceType):
                    device_type_obj = device_type
                else:
                    device_type_obj = None
                if device_type_obj:
                    device_data['type'] = device_type_obj
                else:
                    device_data.pop('type', None)
                Device.objects.create(branch=instance, client=instance.client, **device_data)
        return instance

class UserCreateSerializer(serializers.ModelSerializer):
    client = serializers.PrimaryKeyRelatedField(queryset=Client.objects.all(), required=False, allow_null=True)

    class Meta:
        model = User
        fields = ['username', 'first_name', 'last_name', 'email', 'phone_number', 'password', 'roles', 'is_ceo', 'client']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        client = validated_data.pop('client', None)
        if client is not None and not isinstance(client, Client):
            client = Client.objects.get(pk=client)
        validated_data['password'] = make_password(validated_data['password'])
        user = User.objects.create(client=client, **validated_data)
        return user

class ClientCreateWithDetailsSerializer(serializers.ModelSerializer):
    branches = BranchCreateSerializer(many=True, required=False)
    main_user = UserCreateSerializer(required=True)
    additional_users = UserCreateSerializer(many=True, required=False)

    class Meta:
        model = Client
        fields = ['name', 'logo', 'client_type', 'phone_number', 'email', 'address', 'additional_emails', 'branches', 'main_user', 'additional_users']

    def create(self, validated_data):
        branches_data = validated_data.pop('branches', [])
        main_user_data = validated_data.pop('main_user')
        additional_users_data = validated_data.pop('additional_users', [])

        # Create the client
        client = Client.objects.create(**validated_data)

        # Create the main user
        main_user_data['client'] = client
        main_user_data['is_ceo'] = True
        main_user = User.objects.create(**main_user_data)

        # Create branches and devices
        for branch_data in branches_data:
            devices_data = branch_data.pop('devices', [])
            branch = Branch.objects.create(client=client, **branch_data)

            # Create devices for this branch
            for device_data in devices_data:
                device_type_id = device_data.pop('type')
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print(device_type_id, devices_data)
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                device_type = DeviceType.objects.filter(choice_name=device_type_id).last()
                Device.objects.create(
                    client=client,
                    branch=branch,
                    type=device_type,
                    **device_data
                )

            # Create view permission for main user
            ViewPermission.objects.create(user=main_user, branch=branch)

        # Create additional users
        for user_data in additional_users_data:
            user_data['client'] = client
            user = User.objects.create(**user_data)

            # Create view permissions for all branches
            for branch in Branch.objects.filter(client=client):
                ViewPermission.objects.create(user=user, branch=branch)

        return client
    
class ClientCreateSerializer(serializers.ModelSerializer):
    regions = serializers.ListField(
        child=serializers.CharField(max_length=200),
        write_only=True,
        required=False
    )
    email = serializers.EmailField(required=False, allow_null=True, allow_blank=True)
    address = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    additional_emails = serializers.ListField(
        child=serializers.EmailField(),
        required=False,
        allow_null=True
    )
    logo_url = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = Client
        fields = [
            'name', 'logo', 'logo_url', 'client_type', 'phone_number',
            'email', 'address', 'additional_emails', 'is_active', 'regions'
        ]

    def create(self, validated_data):
        regions = validated_data.pop('regions', [])
        client = Client.objects.create(**validated_data)
        for region_name in regions:
            Region.objects.create(client=client, region=region_name)
        return client

    def update(self, instance, validated_data):
        # Only update client fields, not regions, but allow adding new regions if provided
        regions = validated_data.pop('regions', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if regions is not None:
            # Get existing region names for this client
            existing_regions = set(instance.region_set.values_list('region', flat=True))
            # Add only new regions
            for region_name in regions:
                if region_name not in existing_regions:
                    Region.objects.create(client=instance, region=region_name)
        return instance

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):

    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token['id']   = user.id
        token['first_name'] = user.first_name
        token['last_name'] = user.last_name
        token['email'] = user.email
        token['username'] = user.username
        token['client'] = user.client.name if user.client else 'None'
        token['client_type'] = user.client.client_type if user.client else 'None'
        token['client_id'] = user.client.id if user.client else 'None'
        token['client_image'] = user.client.logo.url if user.client else 'None'
        token['role'] = user.roles
        token['role_text'] =User.ROLES_DICT.get(user.roles)

        return token