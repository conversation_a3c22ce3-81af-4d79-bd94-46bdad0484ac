from rest_framework import status
from account.serializers import (Client<PERSON>erializer, CustomTokenObtainPairSerializer, UserSerializer,
                                ClientCreateWithDetailsSerializer, DeviceTypeSerializer, ClientCreateSerializer, UserCreateSerializer, BranchCreateSerializer)
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser
from rest_framework.views import APIView
from account.models import Client, User
from main.models import DeviceType, ViewPermission, Branch, Device, Region
from rest_framework.response import Response
from django.contrib.auth.hashers import make_password
from django.contrib.auth import authenticate
from rest_framework_jwt.utils import jwt_payload_handler, jwt
from rest_framework_simplejwt.tokens import RefreshToken
from django.conf import settings
from django.contrib.auth.signals import user_logged_in
import random
import cloudinary
import cloudinary.uploader
from django.db import transaction

import warnings
from calendar import timegm
from datetime import datetime

from rest_framework_jwt.compat import get_username, get_username_field
from rest_framework_jwt.settings import api_settings

from rest_framework_simplejwt.views import TokenObtainPairView


# def jwt_payload_handler(user):
#     username_field = get_username_field()
#     username = get_username(user)

#     warnings.warn(
#         'The following fields will be removed in the future: '
#         '`email` and `user_id`. ',
#         DeprecationWarning
#     )

#     payload = {
#         'user_id': user.pk,
#         'email': user.email,
#         'username': username,
#         'client': user.client.name if user.client else 'None',
#         'client_type': user.client.client_type if user.client else 'None',
#         'client_id': user.client.id if user.client else 'None',
#         'client_image': user.client.logo.url if user.client else 'None',
#         'role': user.roles,
#         'role_text':User.ROLES_DICT.get(user.roles),
#         'exp': datetime.utcnow() + api_settings.JWT_EXPIRATION_DELTA
#     }

#     payload[username_field] = username

#     # Include original issued at time for a brand new token,
#     # to allow token refresh
#     if api_settings.JWT_ALLOW_REFRESH:
#         payload['orig_iat'] = timegm(
#             datetime.utcnow().utctimetuple()
#         )

#     if api_settings.JWT_AUDIENCE is not None:
#         payload['aud'] = api_settings.JWT_AUDIENCE

#     if api_settings.JWT_ISSUER is not None:
#         payload['iss'] = api_settings.JWT_ISSUER

#     return payload


@api_view(['POST'])
def add_client(request):
    if request.method == 'POST':
        serializer = ClientSerializer(data=request.data)
        if serializer.is_valid():

            if serializer.validated_data["logo"] is not None: #check if an image was uploaded
                file = serializer.validated_data['logo'] #get the image file from the request
                img = cloudinary.uploader.upload(file, folder = 'logo/') #upload the image to cloudinary
                serializer.validated_data['logo'] = "" #delete the image file
                serializer.validated_data['logo_url'] = img['secure_url'] #save the image url

            client = Client.objects.create(**serializer.validated_data)

            #automatically create a CEO account for any client created
            pass_ = ''.join([str(random.randint(0, 999)).zfill(3) for _ in range(2)])
            # print(password)


            password = make_password(pass_)
            User.objects.create(username=serializer.validated_data['name'], is_ceo=True, password= password, client=client)

            serializer = ClientSerializer(client)

            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
            }

            response = Response(data, status = status.HTTP_200_OK)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        else:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : serializer.errors,
            }

            response = Response(data, status = status.HTTP_400_BAD_REQUEST)
            response["Access-Control-Allow-Origin"] = "*"
            return response


@api_view(['GET'])
def get_users(request):
    if request.method == 'GET':
        users = User.objects.all()
        serializer = UserSerializer(users, many=True)

        data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
            }

        return Response(data, status = status.HTTP_200_OK)



@api_view(['POST'])
def create_user(request):
    if request.method == 'POST':
        serializer = UserSerializer(data=request.data)
        if serializer.is_valid():
            serializer.validated_data['password'] = make_password(serializer.validated_data['password'])

            if serializer.validated_data["profile_pics"] is not None: #check if an image was uploaded

                #upload the image to cloud
                file = serializer.validated_data['profile_pics'] #get the image file from the request
                img = cloudinary.uploader.upload(file, folder = 'profile_pics/') #upload the image to cloudinary
                serializer.validated_data['profile_pics'] = "" #delete the image file
                serializer.validated_data['profile_pics_url'] = img['secure_url'] #save the image url

            serializer.save()

            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
            }

            return Response(data, status = status.HTTP_201_CREATED)

        else:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : serializer.errors,
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)


@api_view(['PUT'])
def add_user(request, user_id):
    try:
        user = User.objects.get(id = user_id)
    except User.DoesNotExist:
        data = {
                'status'  : False,
                'error' : "Does not exist",
            }

        return Response(data, status=status.HTTP_404_NOT_FOUND)


    if request.method == 'PUT':

        serializer = UserSerializer(user, data = request.data, partial=True) #allows you to be able to update the client field of the model by passing the client id

        if serializer.is_valid():

            serializer.save()

            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
            }

            return Response(data, status = status.HTTP_201_CREATED)

        else:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : serializer.errors,
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


# @api_view([ 'POST'])
# def user_login(request):

#     if request.method == "POST":

#         user = authenticate(request, username = request.data['username'], password = request.data['password'])
#         if user is not None and user.is_active==True:
#             try:
#                 payload = jwt_payload_handler(user)
#                 token = jwt.encode(payload, settings.SECRET_KEY)
#                 user_detail = {}
#                 user_detail['id']   = user.id
#                 user_detail['first_name'] = user.first_name
#                 user_detail['last_name'] = user.last_name
#                 user_detail['email'] = user.email
#                 user_detail['username'] = user.username
#                 user_detail['token'] = token
#                 user_logged_in.send(sender=user.__class__,
#                                     request=request, user=user)

#                 data = {
#                 'status'  : True,
#                 'message' : "Successful",
#                 'data' : user_detail,
#                 }

#                 response = Response(data, status = status.HTTP_200_OK)
#                 response["Access-Control-Allow-Origin"] = "*"
#                 return response


#             except SyntaxError:
#                 pass

#         else:
#             data = {
#                 'status'  : False,
#                 'error': 'Invalid email or password'
#                 }

#             response = Response(data, status=status.HTTP_401_UNAUTHORIZED)
#             response["Access-Control-Allow-Origin"] = "*"
#             return response
#     else:
#         data = {
#             'status'  : False,
#             'error': 'Invalid request method'
#             }

#         response = Response(data, status=status.HTTP_405_METHOD_NOT_ALLOWED)
#         response["Access-Control-Allow-Origin"] = "*"
#         return response

@api_view(['POST'])
def user_login(request):
    if request.method == "POST":
        username = request.data.get('username')
        password = request.data.get('password')

        user = authenticate(request, username=username, password=password)

        if user is not None and user.is_active:
            # Generate tokens
            refresh = RefreshToken.for_user(user)

            # Add custom claims to access token
            access = refresh.access_token
            access['id'] = user.id
            access['user_id'] = user.id
            access['first_name'] = user.first_name
            access['last_name'] = user.last_name
            access['email'] = user.email
            access['username'] = user.username
            access['client'] = user.client.name if user.client else 'None'
            access['client_type'] = user.client.client_type if user.client else 'None'
            access['client_id'] = user.client.id if user.client else 'None'
            access['client_image'] = user.client.logo.url if user.client and user.client.logo else ''
            access['role'] = user.roles
            access['role_text'] = User.ROLES_DICT.get(user.roles)

            # Check if this is the first login
            is_first = user.last_login is None

            user_detail = {
                'id': user.id,
                'user_id': user.id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email,
                'username': user.username,
                'is_first': is_first,
                'token': {
                    'refresh': str(refresh),
                    'access': str(access),
                }
            }

            data = {
                'status': True,
                'message': "Successful",
                'data': user_detail
            }

            response = Response(data, status=status.HTTP_200_OK)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        else:
            return Response({
                'status': False,
                'error': 'Invalid email or password'
            }, status=status.HTTP_401_UNAUTHORIZED)

    return Response({
        'status': False,
        'error': 'Invalid request method'
    }, status=status.HTTP_405_METHOD_NOT_ALLOWED)


@api_view([ 'POST'])
def reset_password(request):

    if request.method == "POST":

        try:

            user = authenticate(request, username = request.data['username'], password = request.data['password'])

        except:

            response = Response({
                                    'status'  : False,
                                    'message' : "Ecpected 'username' 'password' & 'new_password",
                                    'data' : [],
                                    }, status = status.HTTP_409_CONFLICT)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        if user is not None and user.is_active==True:
            try:

                new_password = request.data['new_password']
                user.set_password(new_password)
                user.save()

                data = {
                'status'  : True,
                'message' : "Successful",
                'data' : [],
                }

                response = Response(data, status = status.HTTP_200_OK)
                response["Access-Control-Allow-Origin"] = "*"
                return response

            except:

                response = Response({
                                        'status'  : False,
                                        'message' : "Something went wrong",
                                        'data' : [],
                                        }, status = status.HTTP_409_CONFLICT)
                response["Access-Control-Allow-Origin"] = "*"
                return response


        else:

            response = Response({
                                'status'  : False,
                                'message' : "Invalid Credentials",
                                'data' : [],
                                }, status = status.HTTP_401_UNAUTHORIZED)
            response["Access-Control-Allow-Origin"] = "*"
            return response
    else:

        response = Response({
                                'status'  : False,
                                'message' : "Request Not POST",
                                'data' : [],
                                }, status = status.HTTP_400_BAD_REQUEST)
        response["Access-Control-Allow-Origin"] = "*"
        return response


class ClientWithDetailsView(APIView):
    """View for creating a client with branches, devices, and users in one request"""
    permission_classes = [IsAuthenticated, IsAdminUser]

    @transaction.atomic
    def post(self, request):
        serializer = ClientCreateWithDetailsSerializer(data=request.data)

        if serializer.is_valid():
            try:
                client = serializer.save()

                # Return success response with client details
                return Response({
                    'status': True,
                    'message': 'Client created successfully with all related data',
                    'data': {
                        'client_id': client.id,
                        'client_name': client.name,
                        'branches_count': client.branches.count(),
                        'users_count': User.objects.filter(client=client).count()
                    }
                }, status=status.HTTP_201_CREATED)

            except SyntaxError: # Exception as e:
                # If any error occurs, the transaction will be rolled back
                return Response({
                    'status': False,
                    'message': 'Error creating client',
                    # 'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data provided',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_device_types(request):
    """Get all available device types"""
    device_types = DeviceType.objects.all()
    serializer = DeviceTypeSerializer(device_types, many=True)

    return Response({
        'status': True,
        'message': 'Device types retrieved successfully',
        'data': serializer.data
    }, status=status.HTTP_200_OK)


class ClientCreateWithRegionsView(APIView):
    """
    Endpoint: /api/account/create-clients-with-regions/
    Description: Create a client and its regions in a single request.
    Example request body:
    {
        "name": "Acme Corp",
        "regions": ["North", "South", "East"]
    }
    Required fields: name, regions (list)
    Optional fields: email, address, logo_url
    Response: {"status": true/false, "message": str, "client_id": id, "regions": [region names]}
    Frontend: Provide regions as a list of strings.
    """
    permission_classes = ([IsAuthenticated])

    def post(self, request):
        serializer = ClientCreateSerializer(data=request.data)
        if serializer.is_valid():
            client = serializer.save()
            return Response({
                'status': True,
                'message': 'Client and regions created successfully',
                'client_id': client.id,
                'regions': [r.region for r in client.region_set.all()]
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, client_id):
        """
        Update client details and add new regions (existing regions are not removed or edited).
        Example request body:
        {
            "name": "New Name",
            "regions": ["West", "Central"]
        }
        """
        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)
        serializer = ClientCreateSerializer(client, data=request.data, partial=True)
        if serializer.is_valid():
            client = serializer.save()
            return Response({
                'status': True,
                'message': 'Client updated successfully',
                'client_id': client.id,
                'regions': [r.region for r in client.region_set.all()]
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
    

    def get(self, request, client_id):
        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

        client_details = {
            'id': client.id,
            'name': client.name,
            'logo': client.logo.url if client.logo else None,
            'client_type': client.client_type,
            'phone_number': client.phone_number,
            'email': client.email,
            'address': client.address,
            'additional_emails': client.additional_emails,
            'is_active': client.is_active,
        }

        return Response({'client': client_details}, status=status.HTTP_200_OK)
        

class MainClientUserCreateView(APIView):
    """
    Endpoint: /api/account/main_client_user/<client_id>/
    Description: Create the main client user (role CLIENT_ADMIN, is_ceo=True) for a client.
    Example request body:
    {
        "username": "ceo_user",
        "password": "...",
        "first_name": "CEO",
        "last_name": "User",
        ...other user fields...
    }
    Required fields: username, password
    Optional fields: first_name, last_name, email, etc.
    Response: {"status": true/false, "message": str, "user_id": id, "username": str, "client_id": id}
    Frontend: Provide client_id in URL and user details in body.
    """
    permission_classes = [IsAuthenticated]
    def post(self, request, client_id):
        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)

        data = request.data.copy()
        data['client'] = client.id
        data['roles'] = User.CLIENT_ADMIN
        data['is_ceo'] = True

        serializer = UserCreateSerializer(data=data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'status': True,
                'message': 'Main client user created successfully',
                'user_id': user.id,
                'username': user.username,
                'client_id': client.id
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, client_id):
        try:
            client = Client.objects.get(id=client_id)
            user = User.objects.get(client=client, roles=User.CLIENT_ADMIN, is_ceo=True)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)
        except User.DoesNotExist:
            return Response({'status': False, 'message': 'Main client user not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = UserSerializer(user)
        return Response({'status': True, 'user': serializer.data}, status=status.HTTP_200_OK)

    def patch(self, request, client_id):
        try:
            client = Client.objects.get(id=client_id)
            user = User.objects.get(client=client, roles=User.CLIENT_ADMIN, is_ceo=True)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)
        except User.DoesNotExist:
            return Response({'status': False, 'message': 'Main client user not found'}, status=status.HTTP_404_NOT_FOUND)

        data = request.data.copy()
        password = data.pop('password', None)
        serializer = UserCreateSerializer(user, data=data, partial=True)
        if serializer.is_valid():
            updated_user = serializer.save()
            if password:
                updated_user.password = make_password(password)
                updated_user.save()
            return Response({
                'status': True,
                'message': 'Main client user updated successfully',
                'user_id': updated_user.id,
                'username': updated_user.username,
                'client_id': client.id
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        

class AdditionalUserCreateView(APIView):
    """
    Endpoint: /api/account/additional_users/<client_id>/
    Description: Create, get, and update additional users for a client. Supports single or bulk creation.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, client_id):
        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)

        # Expecting a list of users in request.data
        users_data = request.data if isinstance(request.data, list) else [request.data]
        for user in users_data:
            user['client'] = client.id

        serializer = UserCreateSerializer(data=users_data, many=True)
        if serializer.is_valid():
            users = serializer.save()
            return Response({
                'status': True,
                'message': 'Additional users created successfully',
                'users': [
                    {
                        'user_id': user.id,
                        'username': user.username,
                        'client_id': client.id,
                        'roles': user.roles
                    } for user in users
                ]
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, client_id):
        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)

        users = User.objects.filter(client=client).exclude(is_ceo=True)
        from account.serializers import UserSerializer
        serializer = UserSerializer(users, many=True)
        return Response({
            'status': True,
            'users': serializer.data
        }, status=status.HTTP_200_OK)

    def patch(self, request, client_id):
        """
        PATCH body should include 'user_id' and fields to update.
        Optionally include 'password' to change password.
        """
        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)

        user_id = request.data.get('user_id')
        if not user_id:
            return Response({'status': False, 'message': 'user_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id, client=client)
        except User.DoesNotExist:
            return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        data = request.data.copy()
        password = data.pop('password', None)
        from account.serializers import UserCreateSerializer
        serializer = UserCreateSerializer(user, data=data, partial=True)
        if serializer.is_valid():
            updated_user = serializer.save()
            if password:
                updated_user.password = make_password(password)
                updated_user.save()
            return Response({
                'status': True,
                'message': 'User updated successfully',
                'user_id': updated_user.id,
                'username': updated_user.username,
                'client_id': client.id,
                'roles': updated_user.roles
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'status': False,
                'message': 'Invalid data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        


class BranchBulkCreateView(APIView):
    """
    Endpoint: /api/account/branches/<client_id>/
    Description: Create one or more branches (with optional devices) for a client. Assigns view permissions for all client users to each new branch.
    Example request body (single):
    {
        "name": "Branch 1",
        "address": "...",
        "devices": [
            {"serial": "123", "type": "POS", ...},
            ...
        ]
    }
    Example request body (bulk):
    [
        {
            "name": "Branch 1",
            "address": "...",
            "devices": [ ... ]
        },
        {
            "name": "Branch 2",
            "address": "...",
            "devices": [ ... ]
        }
    ]
    Required fields: name
    Optional fields: address, devices
    Response: {"status": true/false, "message": str, "branch_ids": [id, ...]}
    Frontend: Provide client_id in URL and branch(es) in body (list for bulk).
    """
    permission_classes = [IsAuthenticated]
    @transaction.atomic
    def post(self, request, client_id):
        # Accepts either a single dict or a list of dicts
        data = request.data
        is_bulk = isinstance(data, list)
        serializer = BranchCreateSerializer(data=data, many=is_bulk)

        if serializer.is_valid():
            client = Client.objects.get(id=client_id)
            branches_data = serializer.validated_data if is_bulk else [serializer.validated_data]
            created_branches = []

            for branch_data in branches_data:
                devices_data = branch_data.pop('devices', [])
                branch = Branch.objects.create(client=client, **branch_data)
                # Create devices if any
                for device_data in devices_data:
                    device_type_id = device_data.pop('type')
                    device_type = DeviceType.objects.filter(choice_name=device_type_id).last()
                    Device.objects.create(
                        client=client,
                        branch=branch,
                        type=device_type,
                        **device_data
                    )
                created_branches.append(branch)

            # Assign view permissions for CLIENT_ADMIN for each branch
            users = User.objects.filter(client=client, roles=User.CLIENT_ADMIN)
            for branch in created_branches:
                for user in users:
                    ViewPermission.objects.get_or_create(user=user, branch=branch)

            return Response({
                "status": True,
                "message": f"{len(created_branches)} branch(es) created successfully.",
                "branch_ids": [b.id for b in created_branches]
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "status": False,
                "message": "Invalid data provided",
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
    @transaction.atomic
    def put(self, request, branch_id):
        """
        Update branch details and add new devices (does not update or delete existing devices).
        Example request body:
        {
            "name": "New Branch Name",
            "address": "New Address",
            "devices": [
                {"name": "New Device", "type": 1, ...}
            ]
        }
        """
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            return Response({"status": False, "message": "Branch not found"}, status=status.HTTP_404_NOT_FOUND)
        serializer = BranchCreateSerializer(branch, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "status": True,
                "message": "Branch updated successfully",
                "branch_id": branch.id
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "status": False,
                "message": "Invalid data provided",
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
    @transaction.atomic
    def get(self, request, client_id):
        """
        Fetch all branches (branch_id, name, email, address) for a client, with their devices (id, device_id, device_name, device_type).
        If client_id is provided, filter by client; else return error.
        """
        if not client_id:
            return Response({
                "status": False,
                "message": "client_id is required to fetch branches."
            }, status=status.HTTP_400_BAD_REQUEST)
        branches = Branch.objects.filter(client_id=client_id)
        data = []
        for branch in branches:
            devices = branch.device_set.all()
            devices_data = [
                {
                    "id": device.id,
                    "device_id": device.device_id,
                    "device_name": device.name,
                    "device_type": device.type.choice_name if device.type else None
                }
                for device in devices
            ]
            data.append({
                "branch_id": branch.id,
                "name": branch.name,
                "email": branch.email,
                "address": branch.address,
                "Is_active": branch.is_active,
                "devices": devices_data
            })
        return Response({
            "status": True,
            "branches": data
        }, status=status.HTTP_200_OK)
    
        
@api_view(['GET'])
def client_regions(request, client_id):
    """
    Returns all regions (id, region) for a given client_id.
    """
    regions = Region.objects.filter(client_id=client_id)
    data = [
        {
            'id': region.id,
            'region': region.region,
        }
        for region in regions
    ]
    return Response({'regions': data}, status=status.HTTP_200_OK)

@api_view(['GET', 'POST'])
def client_regions_branches(request, client_id):
    """
    GET: Returns all regions (id, region) for a given client_id,
         including branches under each region (branch_id, branch_name).
    POST: Creates a new region for the client. Expects {"region": "Region Name"} in the body.
    """
    if request.method == 'GET':
        regions = Region.objects.filter(client_id=client_id)
        data = []
        for region in regions:
            branches = region.branches.all().values('id', 'name')
            data.append({
                'id': region.id,
                'region': region.region,
                'branches': [
                    {'branch_id': b['id'], 'branch_name': b['name']}
                    for b in branches
                ]
            })
        return Response({'regions': data}, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        region_name = request.data.get('region')
        if not region_name:
            return Response({'status': False, 'message': "'region' field is required."}, status=status.HTTP_400_BAD_REQUEST)
        # Check for duplicate region for this client
        if Region.objects.filter(client_id=client_id, region=region_name).exists():
            return Response({'status': False, 'message': "Region with this name already exists for this client."}, status=status.HTTP_400_BAD_REQUEST)
        region = Region.objects.create(client_id=client_id, region=region_name)
        return Response({
            'status': True,
            'message': 'Region created successfully.',
            'region': {'id': region.id, 'region': region.region}
        }, status=status.HTTP_201_CREATED)


class RegionUpdateDeleteView(APIView):
    """
    Endpoint: /api/account/region/<int:region_id>/
    Methods: PATCH (update), DELETE (delete)
    Description: Update or delete a region by its ID.
    PATCH request body example:
    {
        "region": "New Region Name"
    }
    Response: {"status": true/false, "message": str, ...}
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def patch(self, request, region_id):
        try:
            region = Region.objects.get(id=region_id)
        except Region.DoesNotExist:
            return Response({"status": False, "message": "Region not found"}, status=status.HTTP_404_NOT_FOUND)
        new_name = request.data.get("region")
        if not new_name:
            return Response({"status": False, "message": "'region' field is required"}, status=status.HTTP_400_BAD_REQUEST)
        region.region = new_name
        region.save()
        return Response({"status": True, "message": "Region updated successfully", "region": region.region}, status=status.HTTP_200_OK)

    def delete(self, request, region_id):
        try:
            region = Region.objects.get(id=region_id)
        except Region.DoesNotExist:
            return Response({"status": False, "message": "Region not found"}, status=status.HTTP_404_NOT_FOUND)
        region.delete()
        return Response({"status": True, "message": "Region deleted successfully"}, status=status.HTTP_204_NO_CONTENT)