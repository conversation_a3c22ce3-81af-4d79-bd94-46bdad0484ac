from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import Article, Contact, Quote
from .serializers import ArticleSerializer, ChatSerializer, ContactSerializer, QuoteSerializer


class ContactViewSet(viewsets.ModelViewSet):
   queryset = Contact.objects.all()
   serializer_class = ContactSerializer


class ArticleViewSet(viewsets.ModelViewSet):
   queryset = Article.objects.all()
   serializer_class = ArticleSerializer


class ArticleApiView(APIView):
   #  permission_classes=[IsAuthenticated, IsAdmin]

    detail_serializer = ArticleSerializer

    queryset = Article.objects.all()

    def get(self, request, id=False):

        if id:
            article = self.queryset.filter(id=id)

            print(article)

            if article.exists():

                serializer = self.detail_serializer(article.first())

                print(serializer.data)

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:
                return Response({"Error": "Page not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.detail_serializer(self.queryset.all(), many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

class ChatApiView(APIView):

    serializer_Class = ChatSerializer

    def post(self, request):

        serializer = self.serializer_Class(data=request.data)

        if serializer.is_valid():

            serializer.save()

            return Response(serializer.data, status=status.HTTP_200_OK)

        else:

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
class QuoteApiView(APIView):

    serializer_class = QuoteSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():

            serializer.save()

            return Response(serializer.data, status=status.HTTP_200_OK)

        else:

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
    def get(self, request):

        quotes = Quote.objects.all()

        serializer = self.serializer_class(quotes.all(), many=True)

        if quotes:

            return Response(serializer.data, status=status.HTTP_200_OK)

        else:
            return Response({"Error": "No requested Quotes"}, status=status.HTTP_404_NOT_FOUND)
