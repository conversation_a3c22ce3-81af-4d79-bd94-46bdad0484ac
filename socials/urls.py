from django.urls import include, path

from rest_framework import routers

from .views import ContactViewSet, ArticleViewSet, ArticleApiView, ChatApiView, QuoteApiView

router = routers.DefaultRouter()
router.register(r'contact', ContactViewSet)
router.register(r'article', ArticleViewSet)

urlpatterns = [
   path('', include(router.urls)),
   path('articles/', ArticleApiView.as_view()),
   path('articles/<int:id>', ArticleApiView.as_view()),
   path('lets_talk/', ChatApiView.as_view()),
   path('get_quote/', QuoteApiView.as_view()),
]