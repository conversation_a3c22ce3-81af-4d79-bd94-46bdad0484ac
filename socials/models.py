from django.db import models
from tinymce.models import HTM<PERSON>ield
from main.scripts import mailgun

# Create your models here.

FACILITY_CHOICES = [
    ('Residential', 'Residential'),
    ('Commercial', 'Commercial'),
    ('Industrial', 'Industrial'),
]

ENERGY_CHOICES = [
    ('Check Utility', 'Check Utility'),
    ('Generator 1-5', 'Generator 1-5'),
    ('Gas Generator', 'Gas Generator'),
    ('IPP', 'IPP'),
    ('Solar', 'Solar'),
]

class Contact(models.Model):
    name = models.CharField(max_length=150, blank=False, null=False)
    email = models.EmailField(max_length=100, blank=False, null=False)
    phone_number = models.CharField(max_length=15, blank=False, default= 'XXXX-XXX-XXXX', null=False)
    location = models.CharField(max_length=150)
    use_case = models.TextField(max_length=150)
    facility_type = models.CharField(
        max_length = 20,
        choices = FACILITY_CHOICES,
        default = 'Select one...',
        null=False
        )
    no_of_sources = models.IntegerField()
    sources_of_energy = models.CharField(
        max_length = 20,
        choices = ENERGY_CHOICES,
        default = 'Select one...',
        null=False
        )
    avg_energy_cost = models.IntegerField()

    def __str__(self):
        return f"{self.name}--{self.email}"

    def save(self, *args, **kwargs):
        mailgun.Mailer.send_new_registrant_notification({
                                        "recipients":[
                                                "<EMAIL>",
                                                "<EMAIL>",
                                                "<EMAIL>",
                                                "<EMAIL>",
                                                "<EMAIL>"
                                                ],
                                        "bcc":[
                                                self.email
                                            ],
                                        "location": self.location,
                                        "facility_type":self.facility_type,
                                        "no_of_sources":str(self.no_of_sources),
                                        "sources_of_energy":str(self.sources_of_energy),
                                        "facility_type":self.facility_type,
                                        "email":self.email,
                                        "phone":self.phone_number,
                                        "use_case":self.use_case,
                                        "avg_energy_cost":str(self.avg_energy_cost),
                                        "name":self.name
                                        })
        super(Contact, self).save(*args, **kwargs)


class Article(models.Model):
    title =  models.CharField(max_length=150)
    thumbnail = models.ImageField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    body = HTMLField()

    def __str__ (self):
        return self.title


class Chat(models.Model):
    name = models.CharField(max_length=150, blank=False, null=False)
    email = models.EmailField(max_length=100, blank=False, null=False)
    phone_number = models.CharField(max_length=15, blank=False, default= 'XXXX-XXX-XXXX', null=False)
    created_at = models.DateTimeField(auto_now=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name}--{self.email}"

    def save(self, *args, **kwargs):
        mailgun.Mailer.send_new_chat_notification({
                                        "recipients":[
                                                "<EMAIL>",
                                                "<EMAIL>",
                                                "<EMAIL>",
                                                "<EMAIL>",
                                                "<EMAIL>"
                                                ],
                                        "bcc":[
                                                self.email
                                            ],
                                        "email":self.email,
                                        "phone":self.phone_number,
                                        "name":self.name
                                        })
        super(Chat, self).save(*args, **kwargs)


class Quote(models.Model):
    name = models.CharField(max_length=150, blank=False, null=False)
    email = models.EmailField(max_length=100, blank=False, null=False)
    phone_number = models.CharField(max_length=15, blank=False, default= 'XXXX-XXX-XXXX', null=False)
    location = models.CharField(max_length=150)
    description = models.TextField()

    def __str__(self):
        return f"{self.name}--{self.email}"

    def save(self, *args, **kwargs):
        title = "Wyre-Quote-Request"
        receievers = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        #receievers = ['<EMAIL>']

        message = f"Quotation request details are as follows:\n \nName: {self.name}\nEmail: {self.email}\nPhone Number: {self.phone_number}\nLocation: {self.location}\nDescription: {self.description}\n \nNote:\nResponse should be within 24 hours."
        mailgun.Mailer.send_quote_request(2, title, message, receievers)
        super(Quote, self).save(*args, **kwargs)
