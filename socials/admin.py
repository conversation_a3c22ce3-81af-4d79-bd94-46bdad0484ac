from django.contrib import admin
from .models import Contact, Article, Chat, Quote

@admin.register(Chat)
class ChatAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone_number', 'created_at', 'updated_at')
    search_fields = ('name', 'email', 'phone_number')
    list_filter = ('created_at', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('name', 'email', 'phone_number')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

# Register your models here.
admin.site.register(Contact)
admin.site.register(Article)
# admin.site.register(Chat)
admin.site.register(Quote)
