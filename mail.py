import requests

def send_simple_message_html():

    """
        SEND SIMPLE MAIL REQUIRES :
        - SENDER - NUMERIC CODE
        - TITLE -> STRING
        - MESSAGE -> STRING
        - RECEIVERS -> LIST

        POSSIBLE SENDERS:
            1. WYRE-MONITOR
            2. WYRE-ALERTS
    """

    receievers = ["<EMAIL>"]

    message = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Example POST Form</title>
                </head>
                <body>
                    <h1>Example POST Form</h1>
                    <form action="https://example.com/api/endpoint" method="POST">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name"><br><br>
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email"><br><br>
                    <input type="submit" value="Submit">
                    </form>
                </body>
                </html>

    """

    api_key = "**************************************************"
    mailgun_url = "https://api.mailgun.net/v3/mg.wyreng.com/messages"
    response = requests.post(
        mailgun_url,
        auth=("api", api_key),
        data={"from": f"MAILER <<EMAIL>>",
            "to": receievers,
            "subject": "WYRE FORM",
            "html": message})

    if response.ok:
        print("Sending successful ")
        open("maillogs.txt", "a").write("Sending Successful")
        return {
            "status": True,
            "message": "E-mail successfully sent"
        }

    else:
        print("Sending failed ")
        open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

        return {
            "status": True,
            "message": response.content
        }


send_simple_message_html()