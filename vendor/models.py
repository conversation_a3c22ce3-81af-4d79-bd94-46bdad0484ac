from django.db import models
from statistics import mean as AVERAGE
from wyre import settings
from account.models import Client
from main.models import Device
from datetime import date, timedelta, timezone
from django.utils import timezone
import uuid

class TimeAndUUIDStampedBaseModel(models.Model):
    """
    Base model class that contains special fields other model classes
    will subclass from
    Fields:
        created_at (DateTime): Time at which the object was created
        updated_at (Datetime): Time at which the object was updated
        uuid (String): UUID representing ID of each model
    """

    created_at = models.DateTimeField(default=timezone.now, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    uuid = models.UUIDField(default=uuid.uuid4, editable=False)

    class Meta:
        abstract = True

class Wallet(TimeAndUUIDStampedBaseModel):
    """
    Wallet model that tracks client balances
    Fields:
        balance (DecimalField): The wallet balance
        client (ForeignKey): A reference to the client that owns the wallet
    """
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='wallets')

    def __str__(self):
        return f"{self.client} wallet, Balance: {self.balance}"
    
class Account(TimeAndUUIDStampedBaseModel):
    """
    Account model representing a client's bank or payment account.
    Fields:
        account_number (CharField): The account number for the account.
        account_provider (CharField): The provider of the account, either 'Wema Bank' or 'Paystack'.
        client (ForeignKey): A reference to the client that owns the account.
    """

    WEMA_BANK = 'Wema Bank'
    PAYSTACK = 'Paystack'
    
    ACCOUNT_PROVIDER_CHOICES = [
        (WEMA_BANK, 'Wema Bank'),
        (PAYSTACK, 'Paystack'),
    ]

    account_number = models.CharField(max_length=20)
    account_provider = models.CharField(max_length=20, choices=ACCOUNT_PROVIDER_CHOICES, default=WEMA_BANK)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='payment_account')

    def __str__(self):
        return f"{self.client} Account {self.account_number} with {self.account_provider}"

class Transaction(TimeAndUUIDStampedBaseModel):
    """
    Transaction model representing the details of a transaction.
    Fields:
        amount (DecimalField): The amount involved in the transaction.
        device (ForeignKey): The device related to the transaction.
        client (ForeignKey): The client who initiated the transaction.
        transaction_id (UUIDField): A unique identifier for the transaction.
        transaction_reference (CharField): A reference string for the transaction (e.g., Paystack, Stripe).
        transaction_date (DateTimeField): The date and time when the transaction occurred.
    """

    amount = models.DecimalField(max_digits=10, decimal_places=2)
    device = models.ForeignKey(Device, on_delete=models.CASCADE, related_name='transaction_device')
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='transaction_client')
    transaction_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    transaction_reference = models.CharField(max_length=100, unique=True)
    transaction_date = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Transaction {self.transaction_reference} - {self.amount} for {self.client}"
    
class Token(TimeAndUUIDStampedBaseModel):
    """
    Token model representing tokens associated with a device.
    Fields:
        device (ForeignKey): The device related to the token.
        token_value (DecimalField): The value of the token in monetary terms.
        token_code (CharField): A unique code representing the token.
        amount (DecimalField): The amount associated with the token.
    """

    device = models.ForeignKey(Device, on_delete=models.CASCADE, related_name='token_device')
    token_value = models.DecimalField(max_digits=10, decimal_places=2)
    token_code = models.CharField(max_length=100, unique=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"Token {self.token_code} - {self.amount} for {self.device}"
    
class DebitCreditRecord(TimeAndUUIDStampedBaseModel):
    """
    DebitCreditRecord model to track debits and credits for wallets.
    Fields:
        wallet (ForeignKey): The wallet associated with the transaction.
        transaction (ForeignKey): The transaction that caused the debit or credit.
        account (ForeignKey): The account associated with the debit/credit transaction.
        balance_before (DecimalField): The balance of the wallet before the transaction.
        balance_after (DecimalField): The balance of the wallet after the transaction.
        amount (DecimalField): The transaction amount.
    """

    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='debit_credit_records')
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='debit_credit_records')
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='debit_credit_records')
    balance_before = models.DecimalField(max_digits=10, decimal_places=2)
    balance_after = models.DecimalField(max_digits=10, decimal_places=2)
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"Transaction {self.transaction.id}: {self.amount} - Balance Before: {self.balance_before}, After: {self.balance_after}"