from django.contrib import admin
from .models import Wallet, Account, Transaction, Token, DebitCreditRecord

@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    list_display = ('uuid', 'client', 'balance', 'created_at', 'updated_at')
    search_fields = ('uuid', 'client__name')
    list_filter = ('created_at', 'updated_at')

@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ('uuid', 'client', 'account_number', 'account_provider')
    search_fields = ('uuid', 'client__name', 'account_number')
    list_filter = ('account_provider',)

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction_id', 'client', 'device', 'amount', 'transaction_reference', 'transaction_date')
    search_fields = ('transaction_id', 'transaction_reference', 'client__name', 'device__name')
    list_filter = ('transaction_date',)

@admin.register(Token)
class TokenAdmin(admin.ModelAdmin):
    list_display = ('device', 'token_code', 'token_value', 'amount')
    search_fields = ('token_code', 'device__name')
    list_filter = ('device',)

@admin.register(DebitCreditRecord)
class DebitCreditRecordAdmin(admin.ModelAdmin):
    list_display = ('wallet', 'transaction', 'account', 'balance_before', 'balance_after', 'amount', 'created_at')
    search_fields = ('wallet__uuid', 'transaction__transaction_id', 'account__account_number')
    list_filter = ('created_at',)