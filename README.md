#### This is a django application for energy management

##### Setting up djangoon digi ocean

- https://www.digitalocean.com/community/tutorials/****************************************************************20-04

##### Cron-jobs

*/5 * * * * /home/<USER>/bin/python /home/<USER>/manage.py update_readings
*/5 * * * * /home/<USER>/bin/python /home/<USER>/manage.py update_datalogs
*/5 * * * * /home/<USER>/bin/python /home/<USER>/manage.py update_last_readings
0 0 * * * /home/<USER>/bin/python /home/<USER>/manage.py send_scheduled_bills # CHECK IF TO SEND MAILS EVERY MORNING.