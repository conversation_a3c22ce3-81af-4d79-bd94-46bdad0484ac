#!/usr/bin/env python
import os
import sys
import json
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wyre.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from main.models import Branch, ViewPermission
from adminapp.views import user_pemit
from rest_framework.test import force_authenticate

# Get the User model
User = get_user_model()

def test_user_pemit_update():
    # Get a user that exists in the database
    user = User.objects.filter(is_active=True).first()
    
    if not user:
        print("No active users found in the database.")
        return
    
    print(f"Testing with user: {user.username} (ID: {user.id})")
    
    # Get branches that the user doesn't have permissions for (to add)
    user_branch_ids = ViewPermission.objects.filter(user_id=user.id).values_list('branch_id', flat=True)
    branches_to_add = list(Branch.objects.exclude(id__in=user_branch_ids)[:2].values_list('id', flat=True))
    
    # Get branches that the user has permissions for (to remove)
    branches_to_remove = list(ViewPermission.objects.filter(user_id=user.id)[:1].values_list('branch_id', flat=True))
    
    print(f"Branches to add: {branches_to_add}")
    print(f"Branches to remove: {branches_to_remove}")
    
    # If we don't have branches to add or remove, we can't test properly
    if not branches_to_add and not branches_to_remove:
        print("No suitable branches found for testing.")
        return
    
    # Create a request factory
    factory = RequestFactory()
    
    # Create a request
    payload = {
        'user': user.id,
        'add': branches_to_add,
        'remove': branches_to_remove
    }
    
    request = factory.generic('UPDATE', f'/adminapp/user_pemit/{user.id}/', 
                             data=json.dumps(payload), 
                             content_type='application/json')
    
    # Authenticate the request
    force_authenticate(request, user=user)
    
    # Call the view function directly
    response = user_pemit(request, user_id=user.id)
    
    # Print the response
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.data}")

if __name__ == "__main__":
    test_user_pemit_update()
