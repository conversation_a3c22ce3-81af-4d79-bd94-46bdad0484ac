#!/usr/bin/env python
import os
import sys
import json
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wyre.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from main.models import Branch, ViewPermission

# Get the User model
User = get_user_model()

# Create a test client
client = Client()

# Function to get a valid user ID and token
def get_user_and_token():
    # Get a user that exists in the database
    user = User.objects.filter(is_active=True).first()
    
    if not user:
        print("No active users found in the database.")
        return None, None
    
    # For testing purposes, we'll use a simple approach to get a token
    # In a real application, you would use proper authentication
    from rest_framework_simplejwt.tokens import RefreshToken
    refresh = RefreshToken.for_user(user)
    token = str(refresh.access_token)
    
    return user, token

# Function to get branch IDs for testing
def get_branch_ids(user_id):
    # Get branches that the user doesn't have permissions for (to add)
    user_branch_ids = ViewPermission.objects.filter(user_id=user_id).values_list('branch_id', flat=True)
    branches_to_add = Branch.objects.exclude(id__in=user_branch_ids)[:2].values_list('id', flat=True)
    
    # Get branches that the user has permissions for (to remove)
    branches_to_remove = ViewPermission.objects.filter(user_id=user_id)[:1].values_list('branch_id', flat=True)
    
    return list(branches_to_add), list(branches_to_remove)

# Main test function
def test_user_pemit_update():
    # Get a user and token
    user, token = get_user_and_token()
    if not user:
        return
    
    print(f"Testing with user: {user.username} (ID: {user.id})")
    
    # Get branch IDs for testing
    add_branch_ids, remove_branch_ids = get_branch_ids(user.id)
    
    print(f"Branches to add: {add_branch_ids}")
    print(f"Branches to remove: {remove_branch_ids}")
    
    # If we don't have branches to add or remove, we can't test properly
    if not add_branch_ids and not remove_branch_ids:
        print("No suitable branches found for testing.")
        return
    
    # Set up headers with authentication
    headers = {
        'HTTP_AUTHORIZATION': f'Bearer {token}',
        'content_type': 'application/json'
    }
    
    # Payload for the UPDATE request
    payload = {
        'user': user.id,
        'add': add_branch_ids,
        'remove': remove_branch_ids
    }
    
    # Make the UPDATE request
    response = client.generic(
        'UPDATE',  # Using UPDATE method
        f'/adminapp/user_pemit/{user.id}/',
        json.dumps(payload),
        **headers
    )
    
    # Print the response
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.content.decode()}")

if __name__ == "__main__":
    test_user_pemit_update()
