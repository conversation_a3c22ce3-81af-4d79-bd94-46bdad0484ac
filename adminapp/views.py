from os import device_encoding
from django.shortcuts import render, get_object_or_404
from requests import Request
from main.models import <PERSON><PERSON>, <PERSON><PERSON>, Branch, User, DeviceType, ViewPermission, AdminTariff, Target, DieselOverviewHistory, Reading, Region, MonthlyBranchMetrics, Cost, Datalog
from account.models import SupportTicket
from rest_framework.views import APIView
from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.db.models import Prefetch, Sum, Min, Max, Avg, F, Q
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from rest_framework.decorators import api_view, authentication_classes, permission_classes
# from rest_framework_jwt.authentication import JSONWebTokenAuthentication
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.paginator import Paginator, EmptyPage
from rest_framework.exceptions import NotFound
from django.db.models import Case, When, Value
from functools import lru_cache
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.exceptions import ObjectDoesNotExist
import random
import cloudinary
import cloudinary.uploader
from django.http import JsonResponse
import datetime, logging, json
from django.utils import timezone
from datetime import date, datetime
import calendar
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from adminapp.scripts import time_helpers, pagination
from adminapp.serializers import ClientDetailSerializer
from adminapp.permissions import IsAdmin, IsSuperAdmin, IsCreator, IsWyreAdmin
from adminapp.serializers import (
    ClientCreateSerializer, DeviceCreateSerializer,
    BranchCreateSerializer, BranchDetailSerializer,
    UserCreateSerializer, BranchAdminSerializer,
    UserUpdateSerializer, UserSerializer,
    DeviceSerializer, DeviceTypeSerializer,
    ViewSerializer, ClientUpdateSerializer,
    UserGetSerializer, AdminTariffSerializer,
    UpdateAdminTariffSerializer, ListAdminTariffSerializer, AdminUserSerializer,
    ClientEnergyCostParamsSerializer, ClientBranchesEnergySerializer,
    TargetSerializer, ClientUtilityEnergySerializer,
    DieselOverviewSerializer, DieselCostSerializer,
    ClientBranchesSerializer, SupportTicketSerializer,
    BranchSerializer, MonthlyBranchMetricsSerializer
    )
from main.serializers import BranchLeanSerializer
from wyre import settings

from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.generics import (
    CreateAPIView,
    DestroyAPIView,
    GenericAPIView,
    ListAPIView,
    RetrieveAPIView,
    UpdateAPIView,
)

@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def admin_overview(request, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)
    frequency = frequency.lower()

    try:

        user = User.objects.get(id = request.user.id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    'branches' : [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "total_energy": {
                                            "unit": "kWh",
                                            "value": permitted_view.branch.get_total_energy(start_date, end_date),
                                        },
                            'utility' :
                                    {
                                        "unit": "kWh",
                                        "value": sum(device.get_total_kwh_for_period(start_date, end_date) for device in permitted_view.branch.device_set.filter(type__choice_name = "UTILITY"))
                                    },
                            'diesel' :
                                    {
                                        "unit": "kWh",
                                        "value": sum(device.get_total_kwh_for_period(start_date, end_date) for device in permitted_view.branch.device_set.filter(type__choice_name = "GENERATOR"))
                                    },
                            "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ],
                }
            }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_branches_overview_1(request, start_date, end_date, client_id):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)

    if end.month == datetime.now().month:
        end = datetime.now()

    try:
        client = Client.objects.get(id = client_id)
        queryset = Branch.objects.filter(client=client)
        #user = User.objects.get(client = client)
    except Exception as e:
        print(e)
        data = {
            'status'  : False,
            'message' : "This Client has no Branches",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    branches_detail = [branch.get_devices_overview_2(start, end) for branch in queryset]

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branches_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_branches_overview_2(request, start_date, end_date, client_id):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    print("                                    ")
    print("                                    ")
    print("                                    ")
    print("START ::", start, "END", end)
    print("                                    ")
    print("                                    ")
    print("                                    ")

    try:

        client = Client.objects.get(id = client_id)
        queryset = Branch.objects.filter(client=client)

    except Exception as e:
        print(e)

        data = {
            'status'  : False,
            'message' : "This Client has no Branches",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    branches_detail = []

    for branch in queryset:

        branches_detail.append(branch.get_devices_overview_2(start, end))

    total_kwh = []
    total_cost = []
    baseline_avg =[]
    co2_total = []

    for branch in branches_detail:
        kwh = branch['total_energy']
        cost = branch['energy_cost']
        baseline = branch['baseline']
        co2 = branch['co2']

        total_kwh.append(kwh)
        total_cost.append(cost)
        baseline_avg.append(baseline)
        co2_total.append(co2)

    branches_detail = dict(total_kwh = sum(total_kwh),
                                total_cost = sum(total_cost),
                                baseline_avg = sum(baseline_avg)/len(baseline_avg),
                                co2_total = sum(co2_total),
                                        )
    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branches_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAdmin | IsSuperAdmin])
def all_branches_overview(request, start_date, end_date):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)

    try:

        branches = Branch.objects.all()

        branches_detail = []

        for branch in branches:

            device_detail = branch.get_devices_overview(start, end)

            branches_detail.append(dict(branch_id = branch.id,
                                        name = branch.name,
                                        organisation = branch.client.name,
                                        total_energy = device_detail.get('total_energy'),
                                        baseline = device_detail.get('baseline'),
                                        energy_cost = device_detail.get('energy_cost'),
                                        papr = device_detail.get('papr'),
                                        generator_efficiency = device_detail.get('generator_efficiency'),
                                        diesel_usage = device_detail.get('diesel_usage'),
                                        fuel_efficiency = device_detail.get('fuel_efficiency')
                                        ))

    except User.DoesNotExist:
        # print(e)

        data = {
            'status'  : False,
            'message' : "An error has occured",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    print('@@@')
    print(branches_detail)
    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branches_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAdmin | IsSuperAdmin])
def branch_detail(request, branch_id, start_date, end_date):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    try:
        branch_detail = []

        branch = Branch.objects.get(id=branch_id)

        branch_detail.append(branch.get_devices_overview_2(start, end))

    except Branch.DoesNotExist or User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "Branch does not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branch_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAdmin | IsSuperAdmin])
def branch_users(request, branch_id):

    try:

        serializer = UserSerializer
        branch = Branch.objects.get(id=branch_id)

        view_permissions = ViewPermission.objects.filter(branch=branch)
        users = [permission.user for permission in view_permissions]

        serializer = serializer(users, many=True)
        branch_users = list(serializer.data)

    except Branch.DoesNotExist  or User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "Branch does not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branch_users
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAdmin | IsSuperAdmin])
def branch_devices(request, branch_id):

    try:

        serializer = DeviceSerializer

        branch = Branch.objects.get(id=branch_id)

        devices = Device.objects.filter(branch=branch)

        serializer = serializer(devices, many=True)

        branch_devices = list(serializer.data)

    except User.DoesNotExist  or Branch.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "An error has occured",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branch_devices
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def branch_energy_stats(request, branch_id, start_date, end_date):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    try:
        branch = Branch.objects.get(id = branch_id)

        branch_stats = branch.energy_stats(start, end)

        data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branch_stats
                }
        response = Response(data, status = status.HTTP_200_OK)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    except Exception as e:
        print(e)
        data = {
            'status'  : False,
            'message' : "An error occured",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

@api_view(['GET'])
@permission_classes([IsSuperAdmin | IsWyreAdmin])
def client_overview(request, start_date, end_date):

    try:

        clients = Client.objects.all()

        client_detail = []

        for client in clients:
            start = time_helpers.convert_date(start_date)
            end = time_helpers.convert_date(end_date)

            client_detail.append(dict(client_id = client.id,
                                    name = client.name,
                                    logo_url = client.logo_url,
                                    client_type = client.client_type,
                                    total_energy = client.total_energy(start_date=start, end_date=end).get('total_energy'),
                                    utility = client.total_energy(start_date=start, end_date=end).get('total_utility'),
                                    diesel = client.total_energy(start_date=start, end_date=end).get('total_diesel'),
                                    bench_mark = 0,
                                    subscription_expiry = 0))

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : client_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

class ClientApiView(APIView):
    permission_classes=[IsAuthenticated, IsSuperAdmin]

    detail_serializer = ClientDetailSerializer
    create_serializer = ClientCreateSerializer

    queryset = Client.objects.all()

    def get(self, request, id=False):

        if id:
            client = self.queryset.filter(id=id)

            if client.exists():

                serializer = self.detail_serializer(client.first())

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:
                return Response({"Error": "Page not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.detail_serializer(self.queryset.all(), many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.create_serializer(data = request.data)

        if serializer.is_valid():

            client = Client.objects.create(**serializer.validated_data)

            serializer = ClientCreateSerializer(client)

            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
            }

            response = Response(data, status = status.HTTP_200_OK)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        else:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : serializer.errors,
            }

            response = Response(data, status = status.HTTP_400_BAD_REQUEST)
            response["Access-Control-Allow-Origin"] = "*"
            return response

    def update(self, request):
        client = self.queryset.filter(pk=request.data['id'])

        if client.exists():
            serializer = self.create_serializer(client.first(), data=request.data)

            if serializer.is_valid():
                serializer.save()
                data = {
                    'status': True,
                    'message': 'Successful',
                    'data': serializer.data,
                }
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = {
                    'status': False,
                    'message': 'Unsuccessful',
                    'error': serializer.errors,
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'Error': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsSuperAdmin])
def update_client(request, client_id):
    if request.method == 'PATCH':
        # retrieve the client to update
        try:
            client = Client.objects.get(pk=client_id)
        except Client.DoesNotExist:
            data = {
                'status': False,
                'message': "Unsuccessful",
                'error': "Invalid client ID",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # update the client
        serializer = ClientUpdateSerializer(client, data=request.data)
        if serializer.is_valid():

            Client.objects.filter(pk=client_id).update(**serializer.validated_data)

            # retrieve the updated client
            try:
                updated_client = Client.objects.get(pk=client_id)
                serializer = ClientCreateSerializer(updated_client)
            except Client.DoesNotExist:
                data = {
                    'status': False,
                    'message': "Unsuccessful",
                    'error': "Error updating client",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            data = {
                'status': True,
                'message': "Successful",
                'data': serializer.data,
            }

            return Response(data, status=status.HTTP_200_OK)
        else:
            data = {
                'status': False,
                'message': "Unsuccessful",
                'error': serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
    else:
        data = {
            'status': False,
            'message': "Unsuccessful",
            'error': "Invalid request method",
        }
        return Response(data, status=status.HTTP_405_METHOD_NOT_ALLOWED)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated, IsSuperAdmin])
def delete_client(request, client_id):
    if request.method == 'DELETE':
        # retrieve the client to delete
        try:
            client = Client.objects.get(pk=client_id)
        except Client.DoesNotExist:
            data = {
            'status': False,
            'message': "Unsuccessful",
            'error': "Invalid client ID",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # delete the client
        client.delete()

        data = {
            'status': True,
            'message': "Successful",
        }
        return Response(data, status=status.HTTP_200_OK)
    else:
        data = {
            'status': False,
            'message': "Unsuccessful",
            'error': "Invalid request method",
        }
        return Response(data, status=status.HTTP_405_METHOD_NOT_ALLOWED)

class DeviceApiView(APIView):
    permission_classes=[IsAuthenticated, IsAdmin]

    get_serializer = DeviceSerializer
    create_serializer = DeviceCreateSerializer

    queryset = Device.objects.all()

    def get(self, request, client=False):

        if client:

            devices = self.queryset.filter(client=client)

            if devices.exists():

                serializer = self.get_serializer(devices.all(), many=True)

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:
                return Response({"Error": "Page not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.get_serializer(self.queryset.all(), many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.create_serializer(data = request.data)

        if serializer.is_valid():

            serializer.save()

            return Response(serializer.data, status=status.HTTP_200_OK)
        else:

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def update_device(request, id):

    device = Device.objects.get(id=id)

    serializer = DeviceSerializer(instance=device, data=request.data)

    if serializer.is_valid():

        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def delete_device(request, id):

    device = get_object_or_404(Device, id=id)

    device.delete()

    return Response({"Device deleted successfully"}, status=status.HTTP_202_ACCEPTED)


class BranchApiView(APIView):
    # authentication_classes=[JSONWebTokenAuthentication]
    # permission_classes=[IsAdmin]
    permission_classes=[IsAuthenticated, IsAdmin]

    detail_serializer = BranchDetailSerializer
    create_serializer = BranchCreateSerializer

    queryset = Branch.objects.all()

    def get(self, request, client=False):

        if client:

            branches = self.queryset.filter(client=client)

            if branches.exists():

                serializer = self.detail_serializer(branches.all(), many=True)

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:
                return Response({"Error": "No branch Found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.detail_serializer(self.queryset.all(), many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.create_serializer(data = request.data)

        if serializer.is_valid():

            serializer.save()

            return Response(serializer.data, status=status.HTTP_200_OK)
        else:

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class BranchDetailView(generics.RetrieveAPIView):
    queryset = Branch.objects.all()
    serializer_class = BranchSerializer
    lookup_field = 'id'

    def get_serializer_context(self):
        # Parse start_date and end_date from the request query parameters
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")

        # Add dates to the context
        context = super().get_serializer_context()
        context.update({
            "start_date": start_date,
            "end_date": end_date
        })
        return context

@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def update_branch(request, id):

    branch = Branch.objects.get(id=id)

    serializer = BranchCreateSerializer(instance=branch, data=request.data)

    if serializer.is_valid():

        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def delete_branch(request, id):

    branch = get_object_or_404(Branch, id=id)

    branch.delete()

    return Response(status=status.HTTP_202_ACCEPTED)


class UserApiView(APIView):
    permission_classes=[IsAuthenticated, IsAdmin]

    create_serializer = UserCreateSerializer
    get_serializer = UserGetSerializer

    queryset = User.objects.all()

    def get(self, request, client=False):

        if client:

            user = self.queryset.filter(client=client)

            if user.exists():

                serializer = self.get_serializer(user.all(), many=True)

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:
                return Response({"Error": "No Users"}, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.get_serializer(self.queryset.all(), many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):

        serializer = self.create_serializer(data = request.data)

        if serializer.is_valid():

            user_object = serializer.save()

            user_object.has_no_password = True
            user_object.save()

            return Response(serializer.data, status=status.HTTP_200_OK)
        else:

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def update_user(request, id):

    user = User.objects.get(id=id)

    serializer = UserUpdateSerializer(instance=user, data=request.data)

    if serializer.is_valid():

        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def delete_user(request, id):

    user = get_object_or_404(User, id=id)

    user.delete()

    data = {
                'status'  : True,
                'message' : f"{user} with id {id} deleted",
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def device_types(request):

    serializer = DeviceTypeSerializer

    types = DeviceType.objects.all()
    device_types = []

    serializer = serializer(types, many=True)
    device_types = list(serializer.data)

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : device_types
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def roles(request):

    role_choices = dict(
        SUPERADMIN = 1,
        ADMIN = 2,
        CLIENT_ADMIN = 3,
        OPERATOR = 4,
        VIEWER = 5
    )
    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : role_choices
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET', 'POST', 'DELETE', 'PATCH'])
@permission_classes([IsAuthenticated, IsAdmin, IsCreator])
def user_pemit(request, user_id):

    """Getting, adding, deleting, and updating user permissions."""

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        data = {
                'status'  : False,
                'error' : "Does not exist",
            }

        return Response(data, status=status.HTTP_404_NOT_FOUND)


    # if request.method == 'GET':
    #     perm = user.view_permissions.all()
    #     serializer = ViewSerializer(perm, many=True)

    #     for data in serializer.data:
    #         branch = Branch.objects.get(id=data['branch'])
    #         data["branch_name"] = branch.name

    #     data = {
    #             'status'  : True,
    #             'message' : "Successful",
    #             'data' : serializer.data,
    #             }

    #     return Response(data, status=status.HTTP_200_OK)

    if request.method == 'GET':

        client_id = user.client.id
        permissions = ViewPermission.objects.filter(user=user.id, branch__client=client_id)
        data = [{"id":permission.branch.id, "name":permission.branch.name} for permission in permissions]

        data = {
            "user" : user.get_full_name(),
            'message' : 'Successful',
            'data' : data,
        }

        return Response(data, status=status.HTTP_200_OK)


    elif request.method == 'POST':
        try:
            branch_ids = request.data['branches']
            if not isinstance(branch_ids, list):
                raise KeyError
        except KeyError:
            data = {
                'error': {"branches": ["This field is required and should be a list of branch IDs"]},
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.get(id=user.id)  # Get the user object once

        added_branches = []
        already_existing_branches = []

        for branch_id in branch_ids:
            existing_permission:ViewPermission = ViewPermission.objects.filter(user=user.id, branch=branch_id).first()
            if existing_permission:
                already_existing_branches.append(existing_permission.branch.name)
            else:
                branch = Branch.objects.get(id=branch_id)
                view_permission = ViewPermission.objects.create(branch=branch, user=user)
                view_permission.save()
                added_branches.append(branch.name)

        data = {
            'status': True,
            'message': "Processed branch permissions",
            'added_branches': added_branches,
            'already_existing_branches': already_existing_branches,
        }

        return Response(data, status=status.HTTP_201_CREATED)

    elif request.method == 'PATCH':
        try:
            # Validate payload format
            user_id_from_payload = request.data.get('user')
            add_branch_ids = request.data.get('add', [])
            remove_branch_ids = request.data.get('remove', [])

            # Verify user ID matches
            if user_id_from_payload and int(user_id_from_payload) != int(user_id):
                raise ValueError("User ID in payload does not match URL parameter")

            # Validate branch IDs are lists
            if not isinstance(add_branch_ids, list) or not isinstance(remove_branch_ids, list):
                raise ValueError("'add' and 'remove' must be lists of branch IDs")

        except (KeyError, ValueError) as e:
            data = {
                'status': False,
                'error': str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # Process branches to add
        added_branches = []
        already_existing_branches = []

        for branch_id in add_branch_ids:
            existing_permission = ViewPermission.objects.filter(user=user.id, branch=branch_id).first()
            if existing_permission:
                branch = Branch.objects.get(id=branch_id)
                already_existing_branches.append(branch.name)
            else:
                try:
                    branch = Branch.objects.get(id=branch_id)
                    view_permission = ViewPermission.objects.create(branch=branch, user=user)
                    view_permission.save()
                    added_branches.append(branch.name)
                except Branch.DoesNotExist:
                    # Skip non-existent branches
                    continue

        # Process branches to remove
        deleted_branches = []
        not_found_branches = []

        for branch_id in remove_branch_ids:
            try:
                branch = Branch.objects.get(id=branch_id)
                perm = user.view_permissions.filter(branch=branch).first()
                if perm:
                    perm.delete()
                    deleted_branches.append(branch.name)
                else:
                    not_found_branches.append(branch.name)
            except Branch.DoesNotExist:
                # Skip non-existent branches
                continue

        # Get all current branches for the user after the update
        current_permissions = ViewPermission.objects.filter(user=user.id)
        current_branches = [{
            "id": permission.branch.id,
            "name": permission.branch.name
        } for permission in current_permissions]

        data = {
            'status': True,
            'message': "Updated branch permissions",
            'added_branches': added_branches,
            'already_existing_branches': already_existing_branches,
            'deleted_branches': deleted_branches,
            'not_found_branches': not_found_branches,
            'branches': current_branches  # Add the list of all current branches
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'DELETE':
        try:
            branch_ids = request.data['branches']
            if not isinstance(branch_ids, list):
                raise KeyError
        except KeyError:
            data = {
                'error': {"branches": ["This field is required and should be a list of branch IDs"]},
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.get(id=user.id)  # Get the user object once

        deleted_branches = []
        not_found_branches = []

        for branch_id in branch_ids:
            branch = Branch.objects.get(id=branch_id)
            perm = user.view_permissions.filter(branch=branch).first()
            if perm:
                perm.delete()
                deleted_branches.append(branch.name)
            else:
                not_found_branches.append(branch.name)

        data = {
            'status': True,
            'message': "Processed branch permissions deletion",
            'deleted_branches': deleted_branches,
            'not_found_branches': not_found_branches,
        }

        return Response(data, status=status.HTTP_200_OK)

@api_view(['GET', 'POST'])
def device_state(request, device_id):

    device = Device.objects.get(id=device_id)

    if request.method == 'GET':
        return Response({'is_active': device.is_active})
    if request.method == 'POST':
        data = request.data
        is_active = data.get('is_active')
        if is_active is not None:
            device.set_is_active(is_active)
            return Response({'is_active': device.is_active})
        else:
            return Response({'error': 'Invalid data'}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'POST'])
def user_state(request, user_id):
    user = User.objects.get(id=user_id)

    if request.method == 'GET':
        return Response({'is_active': user.is_active})
    if request.method == 'POST':
        data = request.data
        is_active = data.get('is_active')
        if is_active is not None:
            user.set_is_active(is_active)
            return Response({'is_active': user.is_active})
        else:
            return Response({'error': 'Invalid data'}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def branch_baseline(request, branch_id, end_date):

    if request.method == 'GET':
        end_date = time_helpers.convert_date(end_date)
        branch = Branch.objects.get(id=branch_id)
        branch_baseline = branch.get_branch_baseline(end_date)
        data = {
                'status'  : True,
                'branch' : branch.name,
                'baseline' : branch_baseline
            }
        response = Response(data, status = status.HTTP_200_OK)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    else:
        data = {
                'status'  : False,
                'branch_baseline' : "Not Authorized"
            }
        response = Response(data, status=status.HTTP_404_NOT_FOUND)
        response["Access-Control-Allow-Origin"] = "*"
        return response

def change_user_role(user_id, new_role):

    if new_role not in [User.CLIENT_ADMIN, User.OPERATOR, User.VIEWER]:
        raise ValueError("Invalid role specified. Allowed roles are CLIENT_ADMIN, OPERATOR, and VIEWER.")

    try:
        user = User.objects.get(id=user_id)
        if user.roles in [User.SUPERADMIN, User.ADMIN]:
            raise ValueError("Cannot change roles for SUPERADMIN or ADMIN users.")

        user.roles = new_role
        user.save()

        print(f"User with id {user_id} updated successfully. New role: {User.ROLES_DICT[new_role]}")
        return "updated successfully"

    except User.DoesNotExist:
        print(f"User with id {user_id} does not exist.")
        return "User does not exist."

    except Exception as e:
        print(f"Failed to update user with id {user_id}: {str(e)}")
        return "failed to update"

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_user_role_view(request, user_id, new_role):
    if request.method == 'POST':
        try:
            i = change_user_role(user_id, int(new_role))

            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : i,
            }

            return Response(data, status = status.HTTP_200_OK)

        except Exception as e:
            data = {
                'status'  : False,
                'message' : "Not allowed",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)
    else:
            data = {
                'status'  : False,
                'message' : "GET only allowed",
                'error' : "",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)

"<<<<<<<<<<<<<<<<<<<<<<<< ADMIN TARIFF START >>>>>>>>>>>>>>>>>>>>>>>>"

@api_view(['GET'])
def list_tariffs(request):
    tariffs = AdminTariff.objects.all()
    serializer = ListAdminTariffSerializer(tariffs, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['GET'])
def get_tariff(request, id):
    tariff = AdminTariff.objects.get(id=id)
    serializer = AdminTariffSerializer(tariff)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['POST'])
def create_tariff(request):
    serializer = AdminTariffSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    else:
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['PATCH'])
def update_tariff(request, id):
    tariff = AdminTariff.objects.get(id=id)
    serializer = UpdateAdminTariffSerializer(instance=tariff, data=request.data, partial=True)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
def delete_tariff(request, id):
    tariff = AdminTariff.objects.get(id=id)
    tariff.delete()
    return Response(status=status.HTTP_204_NO_CONTENT)

"<<<<<<<<<<<<<<<<<<<<<<<< ADMIN TARIFF END >>>>>>>>>>>>>>>>>>>>>>>>"

"<<<<<<<<<<<<<<<<<<<<<<<< RESELLER ADMIN START >>>>>>>>>>>>>>>>>>>>>>>>"

@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def reseller_branches_overview_1(request, start_date, end_date, client_id):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    try:
        client = Client.objects.get(id = client_id)
        queryset = Branch.objects.filter(client=client)
    except Exception as e:
        print(e)
        data = {
            'status'  : False,
            'message' : "This Client has no Branches",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    branches_detail = [branch.reseller_branches_overview(start, end) for branch in queryset]

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branches_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def reseller_branches_overview_2(request, start_date, end_date, client_id):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    try:

        client = Client.objects.get(id = client_id)
        queryset = Branch.objects.filter(client=client)

    except Exception as e:
        print(e)

        data = {
            'status'  : False,
            'message' : "This Client has no Branches",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    branches_detail = []

    for branch in queryset:

        branches_detail.append(branch.reseller_branches_overview(start, end))

    total_kwh = []
    total_bill = []
    co2_total = []

    for branch in branches_detail:

        total_kwh.append(branch['total_energy'])
        total_bill.append(branch['bill'])
        co2_total.append(branch['co2'])

    branches_detail = dict(total_kwh = sum(total_kwh),
                                total_bill = sum(total_bill),
                                co2_total = sum(co2_total)/len(co2_total),
                                        )
    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branches_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAdmin | IsSuperAdmin])
def reseller_branch_detail(request, branch_id, start_date, end_date):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    try:
        branch_detail = []

        branch = Branch.objects.get(id=branch_id)

        branch_detail.append(branch.reseller_branches_overview(start, end))

    except Branch.DoesNotExist or User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "Branch does not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branch_detail
            }
    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def reseller_energy_stats(request, branch_id, start_date, end_date):

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    if end.month == datetime.now().month:
        end = datetime.now()

    try:
        branch = Branch.objects.get(id = branch_id)

        branch_stats = branch.reseller_energy_stats(start, end)

        data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : branch_stats
                }
        response = Response(data, status = status.HTTP_200_OK)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    except Exception as e:
        print(e)
        data = {
            'status'  : False,
            'message' : "An error occured",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

"<<<<<<<<<<<<<<<<<<<<<<<< RESELLER ADMIN END >>>>>>>>>>>>>>>>>>>>>>>>"

"<<<<<<<<<<<<<<<<<<<<<<<< RESELLER ADMIN v2 >>>>>>>>>>>>>>>>>>>>>>>>"
@cache_page(60 * 60)
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def admin_header_endpoint(request, start_date, end_date, client_id):
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    if end_date.month == datetime.now().month:
        end_date = datetime.now()

    try:
        client = Client.objects.get(id=client_id)

        total_energy = 0
        total_co2 = 0

        # To get total energy
        try:
            total_energy = client.header_endpoint_total_energy(start_date, end_date)
        except Exception as energy_error:
            print(f"Error calculating total energy: {energy_error}")
            total_energy = 0  # Return 0 if the calculation fails

        # To get total CO2
        try:
            total_co2 = client.header_endpoint_total_co2(start_date, end_date)
        except Exception as co2_error:
            print(f"Error calculating total CO2: {co2_error}")
            total_co2 = 0  # Return 0 if the calculation fails

        # Construct response data
        branches_detail = {
            'total_energy': total_energy,
            'co2_emmission': total_co2
        }

        data = {
            'data': branches_detail
        }

        response = Response(data, status=status.HTTP_200_OK)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    except Exception as e:
        print(f"Error in admin_header_endpoint: {e}")

        data = {
            'status': False,
            'message': "An error occurred",
            'authenticatedData': None
        }
        response = Response(data, status=status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response
    
@cache_page(60 * 60)
@api_view(['GET'])
# @permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def admin_header_cost_endpoint(request, start_date, end_date, client_id):
    """
    Returns the total actual and calculated (optimal) cost for both utility and diesel
    for a client within the given date range.
    """
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    if end_date.month == datetime.now().month:
        end_date = datetime.now()

    try:
        client = Client.objects.get(id=client_id)

        def get_utility():
            try:
                return client.header_endpoint_total_utility_cost(start_date, end_date)
            except Exception as e:
                return {"total_actual_cost": 0, "total_calculated_cost": 0}

        def get_diesel():
            try:
                return client.header_endpoint_total_diesel_cost(start_date, end_date)
            except Exception as e:
                return {"total_actual_cost": 0, "total_calculated_cost": 0}

        with ThreadPoolExecutor(max_workers=2) as executor:
            future_utility = executor.submit(get_utility)
            future_diesel = executor.submit(get_diesel)
            utility_costs = future_utility.result()
            diesel_costs = future_diesel.result()

        total_actual_cost = utility_costs["total_actual_cost"] + diesel_costs["total_actual_cost"]
        total_calculated_cost = utility_costs["total_calculated_cost"] + diesel_costs["total_calculated_cost"]

        cost_detail = {
            "total_actual_cost": total_actual_cost,
            "total_calculated_cost": total_calculated_cost,
        }

        data = {
            "data": cost_detail
        }

        response = Response(data, status=status.HTTP_200_OK)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    except Exception as e:
        data = {
            "status": False,
            "message": "An error occurred",
            "authenticatedData": None
        }
        response = Response(data, status=status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

# class ClientBranchesEnergy(ListAPIView):
#     serializer_class = ClientBranchesEnergySerializer
#     permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]
#     filter_backends = [SearchFilter]
#     pagination_class = pagination.TenPerPagePagination
#     search_fields = ["name"]
#     queryset = Branch.objects.only(
#         "id",
#         "name",
#     ).order_by("name")

#     def list(self, request: Request, client_id: int, start_date: str, end_date: str) -> Response:
#         # filter by client_id first
#         filtered_queryset = self.queryset.filter(client_id=client_id, is_active=True)

#         # Apply search filter
#         search_queryset = self.filter_queryset(filtered_queryset)

#         # Paginate the results
#         page = self.paginate_queryset(search_queryset)

#         # Pass dates to serializer context
#         context = {
#             'request': request,
#             'start_date': start_date,
#             'end_date': end_date
#         }

#         if page is not None:

#             serializer = self.get_serializer(page, many=True, context=context)

#             return self.get_paginated_response(serializer.data)

#         else:
#             # This only happens if there's no pagination needed
#             serializer = self.get_serializer(search_queryset, many=True, context=context)
#             return Response(serializer.data, status=status.HTTP_200_OK)


# from django.db.models import Prefetch
# from main.models import DeviceType, Datalog


@method_decorator(cache_page(60 * 60), name='dispatch')
class ClientBranchesEnergy(ListAPIView):
    serializer_class = ClientBranchesEnergySerializer
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]
    filter_backends = [SearchFilter]
    search_fields = ["name"]

    def get_queryset(self):
        # Prefetch all devices for each branch, with their type
        return Branch.objects.only("id", "name").prefetch_related(
            Prefetch(
                "device_set",
                queryset=Device.objects.select_related("type").only("id", "name", "branch_id", "type", "gen_size"),
                to_attr="prefetched_devices"
            )
        ).order_by("name")

    def get_date_range(self, month: int, year: int):
        today = date.today()
        if year == today.year and month == today.month:
            start_date = date(year, month, 1)
            end_date = today
        else:
            last_day = calendar.monthrange(year, month)[1]
            start_date = date(year, month, 1)
            end_date = date(year, month, last_day)
        return start_date, end_date

    def list(self, request: Request, client_id: int) -> Response:
        try:
            month = int(request.query_params.get('month'))
            year = int(request.query_params.get('year'))
        except (TypeError, ValueError):
            return Response({'error': 'month and year query parameters are required and must be integers.'}, status=status.HTTP_400_BAD_REQUEST)

        start_date, end_date = self.get_date_range(month, year)
        queryset = self.get_queryset().filter(client_id=client_id, is_active=True)
        search_queryset = self.filter_queryset(queryset)

        # Gather all device IDs for all branches in the result
        branches = search_queryset
        all_device_ids = []
        branch_device_map = {}
        for branch in branches:
            devices = getattr(branch, "prefetched_devices", [])
            branch_device_map[branch.id] = devices
            all_device_ids.extend([d.id for d in devices])

        # Bulk aggregate Datalog for all devices in one query
        datalog_agg = (
            Datalog.objects
            .filter(device_id__in=all_device_ids, post_datetime__gte=start_date, post_datetime__lte=end_date)
            .values('device_id')
            .annotate(
                min_val=Min('summary_energy_register_1'),
                max_val=Max('summary_energy_register_1')
            )
        )
        datalog_map = {row['device_id']: (row['min_val'] or 0, row['max_val'] or 0) for row in datalog_agg}

        context = {
            'request': request,
            'start_date': start_date,
            'end_date': end_date,
            'branch_device_map': branch_device_map,
            'datalog_map': datalog_map,
        }

        serializer = self.get_serializer(branches, many=True, context=context)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
@cache_page(60 * 60)
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_monthly_utility_cost(request, client_id):
    """
    Returns monthly utility cost for each branch of a client.
    Query params: month (1-12), year (YYYY)
    """
    month = request.query_params.get('month')
    year = request.query_params.get('year')

    if not month or not year:
        return Response({'error': 'month and year are required as query parameters.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        month = int(month)
        year = int(year)
    except ValueError:
        return Response({'error': 'month and year must be integers.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

    data = client.monthly_utility_cost(month, year)
    return Response(data, status=status.HTTP_200_OK)


@method_decorator(cache_page(60 * 60), name='dispatch')
class ClientUtilityCostView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]

    def get(self, request):
        serializer = ClientEnergyCostParamsSerializer(data=request.query_params)
        if serializer.is_valid():
            client_id = serializer.validated_data['client_id']
            year = serializer.validated_data.get('year')
            try:
                client = Client.objects.get(id=client_id)
                result = client.utility_cost(year)
                return Response(result, status=status.HTTP_200_OK)
            except Client.DoesNotExist:
                return Response({'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                print(e)
                return Response({'message': 'An error occurred'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(cache_page(60 * 60), name='dispatch')
class ClientUtilityEnergy(ListAPIView):
    serializer_class = ClientUtilityEnergySerializer
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]

    def list(self, request, *args, **kwargs):
        client_id = self.request.query_params.get('client_id')
        year = self.request.query_params.get('year')

        try:
            client = Client.objects.get(id=client_id)
        except Client.DoesNotExist:
            return Response({'detail': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

        context = {
            'request': request,
            'client_id': client_id,
            'year': year,
        }

        serializer = self.get_serializer(client, context=context)
        return Response(serializer.data, status=status.HTTP_200_OK)

@cache_page(60 * 60)
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_monthly_utility_energy(request, client_id):
    """
    Returns monthly utility energy for each branch of a client.
    Query params: month (1-12), year (YYYY)
    """
    month = request.query_params.get('month')
    year = request.query_params.get('year')

    if not month or not year:
        return Response({'error': 'month and year are required as query parameters.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        month = int(month)
        year = int(year)
    except ValueError:
        return Response({'error': 'month and year must be integers.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

    data = client.monthly_utility_energy(month, year)
    return Response({'monthly_utility_energy': data}, status=status.HTTP_200_OK)


@method_decorator(cache_page(60 * 60), name='dispatch')
class ClientDieselCostView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]

    def get(self, request):
        serializer = ClientEnergyCostParamsSerializer(data=request.query_params)
        if serializer.is_valid():
            client_id = serializer.validated_data['client_id']
            year = serializer.validated_data.get('year')
            try:
                client = Client.objects.get(id=client_id)
                result = client.diesel_cost(year)
                return Response(result, status=status.HTTP_200_OK)
            except Client.DoesNotExist:
                return Response({'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)
            # except Exception as e:
            #     print(e)
            #     return Response({'message': 'An error occurred'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@cache_page(60 * 60)        
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_monthly_diesel_cost(request, client_id):
    """
    Returns monthly diesel cost for each branch of a client.
    Query params: month (1-12), year (YYYY)
    """
    month = request.query_params.get('month')
    year = request.query_params.get('year')

    if not month or not year:
        return Response({'error': 'month and year are required as query parameters.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        month = int(month)
        year = int(year)
    except ValueError:
        return Response({'error': 'month and year must be integers.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

    data = client.monthly_diesel_cost(month, year)
    return Response({'monthly_diesel_costs': data}, status=status.HTTP_200_OK)


@method_decorator(cache_page(60 * 60), name='dispatch')
class ClientDieselLitresView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]

    def get(self, request):
        serializer = ClientEnergyCostParamsSerializer(data=request.query_params)
        if serializer.is_valid():
            client_id = serializer.validated_data['client_id']
            year = serializer.validated_data.get('year')
            try:
                client = Client.objects.get(id=client_id)
                result = client.diesel_litres(year)
                return Response(result, status=status.HTTP_200_OK)
            except Client.DoesNotExist:
                return Response({'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)
            # except Exception as e:
            #     print(e)
            #     return Response({'message': 'An error occurred'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@cache_page(60 * 60)
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_monthly_diesel_litres(request, client_id):
    """
    Returns monthly diesel litres for each branch of a client.
    Query params: month (1-12), year (YYYY)
    """
    month = request.query_params.get('month')
    year = request.query_params.get('year')

    if not month or not year:
        return Response({'error': 'month and year are required as query parameters.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        month = int(month)
        year = int(year)
    except ValueError:
        return Response({'error': 'month and year must be integers.'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

    data = client.monthly_diesel_litres(month, year)
    return Response({'monthly_diesel_litres': data}, status=status.HTTP_200_OK)


class ClientBranches(ListAPIView):
    serializer_class = ClientBranchesSerializer
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]
    filter_backends = [SearchFilter]
    pagination_class = pagination.TenPerPagePagination
    search_fields = ["name"]
    queryset = Branch.objects.only(
        "id",
        "name",
    ).order_by("name")

    def list(self, request: Request, client_id: int) -> Response:
        # filter by client_id first
        filtered_queryset = self.queryset.filter(client_id=client_id, is_active=True)

        # Apply search filter
        search_queryset = self.filter_queryset(filtered_queryset)

        # Paginate the results
        page = self.paginate_queryset(search_queryset)

        if page is not None:

            serializer = self.get_serializer(page, many=True)

            return self.get_paginated_response(serializer.data)

        else:
            # This only happens if there's no pagination needed
            serializer = self.get_serializer(search_queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

@lru_cache(maxsize=128)
def get_set_target(client_id):

    try:
        # Try to get the Target object
        target = Target.objects.get(client_id=client_id)

        # If found, construct set_target dictionary with target data
        set_target = {
            "id": 0,
            "name": "Set Target",
            "baseline_energy": '-',
            "blended_cost_of_energy": target.blended_cost_of_energy,
            "diesel_usage_accuracy": target.diesel_usage_accuracy,
            "utility_usage_accuracy": target.utility_usage_accuracy,
            "deviation_hours": target.maximum_monthly_deviation_hours,
            "papr": target.papr,
            "fuel_efficiency": target.fuel_efficiency,
            "generator_size_efficiency_1": "G1",
            "generator_size_efficiency_2": "G2",
            "generator_size_efficiency_3": "G3",
        }

    except ObjectDoesNotExist:
        # Handle the case where Target does not exist
        set_target = {
            "id": 0,
            "name": "Target not Set",
            "baseline_energy": '-',
            "blended_cost_of_energy": '-',
            "diesel_usage_accuracy": '-',
            "utility_usage_accuracy": '-',
            "deviation_hours": '-',
            "papr": '-',
            "fuel_efficiency": '-',
            "generator_size_efficiency_1": "G1",
            "generator_size_efficiency_2": "G2",
            "generator_size_efficiency_3": "G3",
        }

    except Exception as e:
        # Log unexpected errors for debugging with a fallback response
        print(f"Unexpected error: {e}")
        set_target = {
            "id": 0,
            "name": "Set Target",
            "error": "An unexpected error occurred."
        }
    return set_target


# class AdminKeyMetrics(ListAPIView):
#     serializer_class = ClientBranchListTableSerializer
#     permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]
#     filter_backends = [SearchFilter]
#     pagination_class = pagination.TenPerPagePagination
#     search_fields = ["name"]

#     # Cache entire view response with dynamic cache key for page-level caching
#     @method_decorator(cache_page(60 * 15, key_prefix="admin_key_metrics"))
#     def list(self, request: Request, client_id: int, start_date: str, end_date: str) -> Response:
#         # Only retrieve necessary fields and relationships
#         queryset = (
#             Branch.objects.filter(client_id=client_id, is_active=True)
#             .only("id", "name")
#             .order_by("name")
#             .prefetch_related(
#                 Prefetch(
#                     "device_set",
#                     queryset=Device.objects
#                         .select_related("type")
#                         .prefetch_related(
#                             Prefetch(
#                                 "reading_set",
#                                 queryset=Reading.objects.only(
#                                     "device_id", "post_datetime", "kwh_import", "total_kw"
#                                 )
#                             )
#                         )
#                         .only("id", "branch_id", "type_id", "name", "is_load", "is_source", "gen_size")
#                 )
#             )
#         )

#         # Apply search filter
#         filtered_queryset = self.filter_queryset(queryset)

#         # Cache pagination results per page to speed up common pagination accesses
#         page = self.paginate_queryset(filtered_queryset)

#         if page is not None:
#             # Add context parameters for serializer
#             context = {"request": request, "start_date": start_date, "end_date": end_date}
#             serializer = self.get_serializer(page, many=True, context=context)
#             response = self.get_paginated_response(serializer.data)

#             # Prepend set_target data only once after pagination
#             response.data['results'].insert(0, get_set_target(client_id))  # New function for set_target

#             return response

#         # No pagination
#         results.insert(0, get_set_target(client_id))
#         return Response({
#             "page": 1,
#             "total_pages": 1,
#             "count": len(results),
#             "results": results
#         }, status=status.HTTP_200_OK)



from datetime import timedelta
class AdminKeyMetrics(APIView):
    permission_classes = [IsAuthenticated]
    filter_backends = [SearchFilter]
    search_fields = ["name"]

    def format_metrics_for_original(self, metrics_obj):
        m = metrics_obj.metrics or {}
        return {
            "id": metrics_obj.branch.id,
            "name": metrics_obj.branch.name,
            "baseline_energy_used": float(m.get("baseline_energy_used", 0)),
            "blended_cost_of_energy": float(m.get("blended_cost_of_energy", 0)),
            "diesel_usage_accuracy": float(m.get("diesel_usage_accuracy", 0)),
            "utility_usage_accuracy": float(m.get("utility_usage_accuracy", 0)),
            "deviation_hours": m.get("deviation_hours", "0 Hrs : 0 Mins"),
            "papr": float(m.get("papr", 0)),
            "fuel_efficiency": float(m.get("fuel_efficiency", 0)),
            "generator_size_efficiency_1": f'{float(m.get("generator_size_efficiency_1", 0)):.2f}',
            "generator_size_efficiency_2": f'{float(m.get("generator_size_efficiency_2", 0)):.2f}',
            "generator_size_efficiency_3": f'{float(m.get("generator_size_efficiency_3", 0)):.2f}',
        }

    def get(self, request, client_id):
        """
        Query params: month, year
        Returns: metrics for all branches under the client for the given month/year,
        with search (no pagination).
        """
        month = request.query_params.get("month")
        year = request.query_params.get("year")

        if not (month and year):
            return Response({"detail": "month and year are required."}, status=400)

        try:
            client = Client.objects.get(id=client_id)
            month = int(month)
            year = int(year)
        except (Client.DoesNotExist, ValueError):
            return Response({"detail": "Invalid client_id, month, or year."}, status=400)

        now = timezone.now()
        current_month = now.month
        current_year = now.year

        # 1. Build base queryset
        branches_qs = Branch.objects.filter(client=client, is_active=True).order_by("name")

        # 2. Apply search
        for backend in list(self.filter_backends):
            branches_qs = backend().filter_queryset(request, branches_qs, self)

        # 3. Calculate metrics for all filtered branches
        results = []
        for branch in branches_qs:
            metrics_obj = MonthlyBranchMetrics.objects.filter(branch=branch, month=month, year=year).first()
            if metrics_obj:
                if (month == current_month and year == current_year):
                    is_fresh = (now - metrics_obj.updated_at) < timedelta(hours=2)
                    if is_fresh:
                        results.append(self.format_metrics_for_original(metrics_obj))
                        continue
                else:
                    results.append(self.format_metrics_for_original(metrics_obj))
                    continue
            metrics = MonthlyBranchMetrics.calculate_monthly_metrics(branch, month, year)
            if metrics_obj:
                metrics_obj.metrics = metrics
                metrics_obj.save()
            else:
                metrics_obj = MonthlyBranchMetrics.objects.create(
                    client=client,
                    branch=branch,
                    month=month,
                    year=year,
                    metrics=metrics,
                )
            results.append(self.format_metrics_for_original(metrics_obj))

        # 4. Insert the target at the top
        results.insert(0, get_set_target(client_id))

        # 5. Return all results (no pagination)
        return Response({
            "count": len(results),
            "results": results
        }, status=200)
    

class ClientUser(ListCreateAPIView, RetrieveUpdateDestroyAPIView):
    serializer_class = AdminUserSerializer
    permission_classes = [IsAuthenticated, IsAdmin]
    filter_backends = [SearchFilter]
    pagination_class = pagination.TenPerPagePagination
    search_fields = ["username", "email", "phone_number", "roles"]
    queryset = User.objects.only(
        "id",
        "username",
        "email",
        "phone_number",
        "client",
        "roles"
    ).order_by('username')

    def list(self, request: Request, client_id: int) -> Response:
        users = ViewPermission.objects.filter(branch__client_id=client_id).values("user__id").distinct()
        users = User.objects.filter(id__in=[user['user__id'] for user in users])

        # If no_pagination is true or pagination is not needed
        serializer = self.get_serializer(users, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


    def create(self, request: Request, client_id: int) -> Response:
        data = request.data.copy()
        data['client'] = client_id

        # Extract the branch_id from the request data
        branch_id = data.pop('branch_id', None)

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)

        # Save the user instance
        user_instance = serializer.save()

        if branch_id:
            # Create a ViewPermission instance to associate the user with the branch
            ViewPermission.objects.create(user=user_instance, branch_id=branch_id)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request: Request, client_id: int, pk: int) -> Response:
        user = self.get_object()
        data = request.data.copy()
        data['client'] = client_id  # Ensure the client_id is set correctly
        serializer = self.get_serializer(user, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)

    def destroy(self, request: Request, client_id: int, pk: int) -> Response:
        try:
            user = self.queryset.get(id=pk, client_id=client_id)
        except User.DoesNotExist:
            return Response({"detail": "User not found"}, status=status.HTTP_404_NOT_FOUND)

        user.delete()
        return Response({"detail": "User deleted successfully"}, status=status.HTTP_204_NO_CONTENT)

class AdminTarget(APIView):
    def get(self, request, client_id):
        client = Client.objects.get(id=client_id)
        target = get_object_or_404(Target, client=client)
        serializer = TargetSerializer(target)
        return Response(serializer.data)

    def post(self, request, client_id):
        client = Client.objects.get(id=client_id)
        # Check if target already exists for the client
        if Target.objects.filter(client=client).exists():
            return Response({'error': 'Target already exists for this client.'}, status=status.HTTP_400_BAD_REQUEST)
        serializer = TargetSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(client_id=client_id)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, client_id):
        client = Client.objects.get(id=client_id)
        target = get_object_or_404(Target, client=client)
        serializer = TargetSerializer(target, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, client_id):
        client = Client.objects.get(id=client_id)
        target = get_object_or_404(Target, client=client)
        target.reset_values()  # Call the reset_values method to reset the target's fields
        return Response({'message': 'Target values have been reset.'})

    def delete(self, request, client_id, format=None):
        target = get_object_or_404(Target, client_id=client_id)
        target.delete()
        return Response({'message': 'Target has been deleted.'}, status=status.HTTP_204_NO_CONTENT)

class DieselOverview(ListAPIView):
    serializer_class = DieselOverviewSerializer
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]
    filter_backends = [SearchFilter]
    pagination_class = pagination.TenPerPagePagination
    search_fields = ["name"]
    queryset = Branch.objects.only(
        "id",
        "name",
    ).order_by("name")

    def list(self, request: Request, client_id: int) -> Response:
        # filter by client_id first
        filtered_queryset = self.queryset.filter(client_id=client_id, is_active=True)

        # Apply search filter
        search_queryset = self.filter_queryset(filtered_queryset)

        # Paginate the results
        page = self.paginate_queryset(search_queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True, context={'request': request})
            data = serializer.data

            # Get the paginated response
            response = self.get_paginated_response(data)

            return response

        else:
            serializer = self.get_serializer(search_queryset, many=True, context={'request': request})
            data = serializer.data

            return Response(data, status=status.HTTP_200_OK)

class Procurements(APIView):

    def get(self, request, branch_id):
        try:
            obj = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            raise NotFound('Branch not found')

        cost_queryset = obj.cost_set.filter(cost_type="diesel")
        paginated_costs = self.paginate_queryset(cost_queryset, request)
        cost_serializer = DieselCostSerializer(paginated_costs, many=True)
        data = cost_serializer.data
        for i in data:
            i["amount"] = i["quantity"] * i["price_per_litre"]

        return Response(data)

    def paginate_queryset(self, queryset, request, view=None):
        """A simple paginator to be reused in the method fields"""
        # page_size = int(request.query_params.get('page_size', 10))  # Default to 10 items per page
        page_size = 10
        page = int(request.query_params.get('page', 1))  # Default to page 1
        start = (page - 1) * page_size
        end = start + page_size
        return queryset[start:end]

class Consumptions(APIView):

    def get(self, request, branch_id):
        try:
            obj = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            raise NotFound('Branch not found')

        overview_history_queryset = DieselOverviewHistory.objects.filter(branch=obj.id)
        paginated_overview_history = self.paginate_queryset(overview_history_queryset, request)

        data = []
        for i in paginated_overview_history:
            data.append({
                'date': i.data["date"],
                'consumption': i.data["quantity"],
                'fuel_efficiency_ratio': 0 #TODO
            })
        return Response(data)  # Wrap the list in a Response object


    def paginate_queryset(self, queryset, request, view=None):
        """A simple paginator to be reused in the method fields"""
        # page_size = int(request.query_params.get('page_size', 10))  # Default to 10 items per page
        page_size = 2
        page = int(request.query_params.get('page', 1))  # Default to page 1
        start = (page - 1) * page_size
        end = start + page_size
        return queryset[start:end]

class CreateSupportTicketAPIView(CreateAPIView):
    queryset = SupportTicket.objects.all().select_related("user", "client")
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class ListSupportTicketAPIView(ListAPIView):
    queryset = SupportTicket.objects.only(
        "subject", "priority", "description", "status", "user", "client"
    ).select_related("user")
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = pagination.TenPerPagePagination

    def list(self, request, client_id):
        queryset = self.get_queryset().filter(client__id=client_id)

        # Apply custom ordering to bring RESOLVED tickets to the top
        queryset = queryset.order_by(
            Case(
                When(status=SupportTicket.SupportTicketStatus.RESOLVED, then=Value(0)),
                default=Value(1),
            ),
            "-created_at",
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class SupportTicketAPIView(UpdateAPIView, DestroyAPIView):
    queryset = SupportTicket.objects.all().select_related("user")
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = "id"

class ClientDashboardConfig(APIView):
    """
    API to fetch the dashboard configuration for a client.
    Returns the client's type and other relevant details for determining the dashboard layout.
    """
    permission_classes = [IsAuthenticated, IsAdmin | IsSuperAdmin]

    def get(self, request, client_id):
        try:
            # Fetch the client
            client = get_object_or_404(Client, id=client_id)

            # Prepare the response data
            response_data = {
                "client_type": client.client_type,
                "client_name": client.name,
                "client_logo": client.logo_url or request.build_absolute_uri(client.logo.url) if client.logo else None,
                "contact": {
                    "phone_number": client.phone_number,
                    "email": client.email,
                    "address": client.address,
                }
            }

            return JsonResponse(response_data, status=200)

        except Exception as e:
            # Log the exception if necessary
            print(f"Error in UserDashboardConfig API: {e}")
            return JsonResponse({"error": "An unexpected error occurred."}, status=500)

class AllTimeConsumptionView(APIView):
    def get(self, request, client_id):
        data = {
            "client_name": "ATC",
            "total_consumption": {
                "unit": "kWh",
                "value": 45678.34
            }
        }
        return Response(data, status=status.HTTP_200_OK)

class ThisMonthConsumptionView(APIView):
    def get(self, request, client_id):
        data = {
            "client_name": "ATC",
            "this_month_consumption": {
                "unit": "kWh",
                "value": 1234.56
            }
        }
        return Response(data, status=status.HTTP_200_OK)

class LastMonthConsumptionView(APIView):
    def get(self, request, client_id):
        data = {
            "client_name": "ATC",
            "last_month_consumption": {
                "unit": "kWh",
                "value": 1234.56
            }
        }
        return Response(data, status=status.HTTP_200_OK)

class AmountDueView(APIView):
    def get(self, request, client_id):
        data = {
            "client_name": "ATC",
            "amount_due": {
                "unit": "Naira",
                "value": 30000
            }
        }
        return Response(data, status=status.HTTP_200_OK)

class DevicesListView(APIView):
    def get(self, request, client_id):

        data = {
                "page": 1,
                "total_pages": 2,
                "count": 10,
                "results": [
                    {
                        "id": 1,
                        "device_name": "ATC Ilupeju UTILITY 1",
                        "consumption": {
                            "all_time": {
                                "unit": "kWh",
                                "value": 5678.90
                            },
                            "this_month": {
                                "unit": "kWh",
                                "value": 345.67
                            },
                            "last_month": {
                                "unit": "kWh",
                                "value": 567.89
                            },
                            "amount": {
                                "unit": "Naira",
                                "value": 5000
                            }
                        }
                    },
                    {
                        "id": 2,
                        "device_name": "ATC Ilupeju Generator 1",
                        "consumption": {
                            "all_time": {
                                "unit": "kWh",
                                "value": 1234.56
                            },
                            "this_month": {
                                "unit": "kWh",
                                "value": 67.89
                            },
                            "last_month": {
                                "unit": "kWh",
                                "value": 89.01
                            },
                            "amount": {
                                "unit": "Naira",
                                "value": 3000
                            }
                        }
                    },
                    {
                        "id": 3,
                        "device_name": "ATC Ilupeju Solar Panel 1",
                        "consumption": {
                            "all_time": {
                                "unit": "kWh",
                                "value": 2345.67
                            },
                            "this_month": {
                                "unit": "kWh",
                                "value": 123.45
                            },
                            "last_month": {
                                "unit": "kWh",
                                "value": 145.67
                            },
                            "amount": {
                                "unit": "Naira",
                                "value": 4000
                            }
                        }
                    }
                ]
            }

        return Response(data, status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def client_branches_devices(request, client_id):
    """
    Returns all client details, branches (id, name) and their devices (device_id, name) for a given client_id.
    """
    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

    client_details = {
        'id': client.id,
        'name': client.name,
        'logo': client.logo.url if client.logo else None,
        'logo_url': client.logo_url,
        'client_type': client.client_type,
        'phone_number': client.phone_number,
        'email': client.email,
        'address': client.address,
        'additional_emails': client.additional_emails,
        'is_active': client.is_active,
    }

    branches = Branch.objects.filter(client=client)
    data = []
    for branch in branches:
        devices = Device.objects.filter(branch=branch)
        devices_list = [
            {
                'id': device.id,
                'device_id': device.device_id,
                'name': device.name
            }
            for device in devices
        ]
        data.append({
            'branch_id': branch.id,
            'branch_name': branch.name,
            'devices': devices_list
        })
    return Response({'client': client_details, 'branches': data}, status=status.HTTP_200_OK)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def edit_device_name_id(request, id):
    """
    Edit the name and device_id of a device by its id (primary key).
    PATCH body: {"name": "new name", "device_id": "new_device_id"}
    """
    try:
        device = Device.objects.get(pk=id)
    except Device.DoesNotExist:
        return Response({'error': 'Device not found.'}, status=status.HTTP_404_NOT_FOUND)

    name = request.data.get('name')
    device_id = request.data.get('device_id')
    updated = False

    if name is not None:
        device.name = name
        updated = True
    if device_id is not None:
        device.device_id = device_id
        updated = True

    if updated:
        device.save()
        return Response({'success': True, 'device_id': device.device_id, 'name': device.name}, status=status.HTTP_200_OK)
    else:
        return Response({'error': 'No valid fields provided.'}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def suspend_client(request, client_id):
    """
    Suspend or unsuspend a client by toggling is_active.
    PATCH body: {"is_active": false} or {"is_active": true}
    """
    from account.models import Client
    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'error': 'Client not found.'}, status=status.HTTP_404_NOT_FOUND)

    is_active = request.data.get('is_active')
    if is_active is None:
        return Response({'error': 'is_active field required.'}, status=status.HTTP_400_BAD_REQUEST)

    client.is_active = bool(is_active)
    client.save()
    return Response({'client_id': client.id, 'is_active': client.is_active}, status=status.HTTP_200_OK)

@api_view(['PATCH'])
@permission_classes([IsAuthenticated, IsAdmin | IsSuperAdmin])
def suspend_branch(request, branch_id):
    """
    Suspend or unsuspend a branch by toggling is_active.
    PATCH body: {"is_active": false} or {"is_active": true}
    """
    from main.models import Branch
    try:
        branch = Branch.objects.get(id=branch_id)
    except Branch.DoesNotExist:
        return Response({'error': 'Branch not found.'}, status=status.HTTP_404_NOT_FOUND)

    is_active = request.data.get('is_active')
    if is_active is None:
        return Response({'error': 'is_active field required.'}, status=status.HTTP_400_BAD_REQUEST)

    branch.is_active = bool(is_active)
    branch.save()
    return Response({'branch_id': branch.id, 'is_active': branch.is_active}, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def force_login_client_admin(request, client_id):
    """
    Endpoint: /api/account/force-login-client-admin/<client_id>/
    Description: Allows a superuser to generate a login token for the main client admin of a client.
    Returns a JWT and user details in the same format as standard login.
    """
    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        return Response({'status': False, 'message': 'Client not found'}, status=status.HTTP_404_NOT_FOUND)

    # Find the main client admin (is_ceo=True or role=CLIENT_ADMIN)
    user = User.objects.filter(client=client, is_ceo=True).first()
    if not user:
        user = User.objects.filter(client=client, roles=User.CLIENT_ADMIN).first()
    if not user:
        return Response({'status': False, 'message': 'No client admin found for this client.'}, status=status.HTTP_404_NOT_FOUND)

    # Generate JWT tokens for this user
    refresh = RefreshToken.for_user(user)
    access = refresh.access_token
    # Add custom claims to access token (optional, for parity with standard login)
    access['id'] = user.id
    access['user_id'] = user.id
    access['first_name'] = user.first_name
    access['last_name'] = user.last_name
    access['email'] = user.email
    access['username'] = user.username
    access['client'] = user.client.name if user.client else 'None'
    access['client_type'] = user.client.client_type if user.client else 'None'
    access['client_id'] = user.client.id if user.client else 'None'
    access['client_image'] = user.client.logo.url if user.client and user.client.logo else ''
    access['role'] = user.roles
    access['role_text'] = User.ROLES_DICT.get(user.roles)

    data = {
        'id': user.id,
        'user_id': user.id,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'email': user.email,
        'username': user.username,
        'token': {
            'refresh': str(refresh),
            'access': str(access),
        }
    }

    return Response({
        'status': True,
        'message': 'Force login token generated.',
        'data': data
    }, status=status.HTTP_200_OK)