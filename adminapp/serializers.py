import datetime
from decimal import Decimal
from rest_framework import serializers
from main.models import Al<PERSON>_Setting, <PERSON>, Cost, Device, Equipment, Month_End_Diesel_Balance, ViewPermission, Bill, Bill_Mails, DeviceType, AdminTariff, Target, Reading, FuelConsumption, Datalog, MonthlyBranchMetrics
from account.models import Client, User, SupportTicket
import main
from adminapp.scripts import time_helpers, pagination
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.db.models import Q, Sum, Avg, Max, Min, F
from django.utils.functional import cached_property
from django.core.cache import cache
import hashlib
import hashlib
import concurrent.futures
from main.scripts import aggregation_helpers

class ClientDetailSerializer(serializers.ModelSerializer):
    number_of_branches = serializers.ReadOnlyField()

    class Meta:
        model = Client
        fields = '__all__'

class ClientUpdateSerializer(serializers.ModelSerializer):

    class Meta:
        model = Client
        exclude = ['logo', 'logo_url']

class ClientCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = Client
        read_only_fields = ["total_energy"]
        fields = '__all__'
        # exclude = ['logo_url']

class BranchDetailSerializer(serializers.ModelSerializer):

    class Meta:
        model = Branch
        fields = ['id', 'name' , 'client', 'address']

class BranchCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = Branch
        fields = ['id', 'name', 'address', 'client']

class DeviceSerializer(serializers.ModelSerializer):

    class Meta:
        model = Device
        fields = ['id', 'name', 'type', 'device_id', 'branch', 'fuel_type', 'gen_size', 'operating_hours_start', 'operating_hours_end', 'is_active', 'is_source', 'next_maintenance_date']

class DeviceCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = Device
        fields = ['name', 'type', 'client', 'branch', 'device_id', 'is_active', 'is_source', 'fuel_type', 'operating_hours_start', 'operating_hours_end']

class DeviceMaintenanceSerializer(serializers.ModelSerializer):

    class Meta:
        model = Device
        fields = ['id', 'name', 'next_maintenance_date', 'gen_size']

class ViewSerializer(serializers.ModelSerializer):

    class Meta:
        model = ViewPermission
        fields = '__all__'

class BillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bill
        fields = '__all__'

class Alert_SettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Alert_Setting
        fields = '__all__'

class BillMailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bill_Mails
        fields = '__all__'

class CostTrackerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = '__all__'

class EquipmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Equipment
        fields = '__all__'

class MonthEndDieselSerializer(serializers.ModelSerializer):
    class Meta:
        model = Month_End_Diesel_Balance
        fields = '__all__'

class DieselCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = ["date", "quantity", "price_per_litre"]

class UtilityCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = ["date", "amount", "value", "tarrif", "vat_inclusive_amount", "vat"]

class UserSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "phone_number", "email", "roles", "date_joined", "last_login"]

class UserUpdateSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "phone_number","email", "roles", "is_active"]

class UserCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = ["id", "username", "password", "phone_number", "roles", "client"]
        extra_kwargs = {
            'password': {'write_only': True}
        }

class UserGetSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = ["id", "username", "phone_number", "email", "is_active", "roles", "client", "date_created"]

class ClientMinimalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ['id', 'name']

class DeviceSerializer(serializers.ModelSerializer):
    dashboard = serializers.SerializerMethodField()

    class Meta:
        model = Device
        fields = ["name", "id", "is_load", "is_source", "type", "dashboard"]

    def get_dashboard(self, obj):
        # Get start_date and end_date from context
        start_date = self.context.get("start_date")
        end_date = self.context.get("end_date")

        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        # Perform calculations with `start_date` and `end_date`
        return {
            "total_kwh": {
                "unit": "kWh",
                "value": round(obj.get_total_kwh_for_period(start_date, end_date), 2)
            },
            "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": round(obj.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value"), 2)
            },
            "cost_of_energy": {
                "unit": "Naira/kWh",
                "value": obj.get_cost_of_energy(start_date, end_date)
            },
            "solar_hours": {
                "value": obj.get_solar_hours_consumption(start_date, end_date),
            }
        }

class BranchSerializer(serializers.ModelSerializer):
    client = ClientMinimalSerializer()
    devices = serializers.SerializerMethodField()
    usage_hours = serializers.SerializerMethodField()
    demand_values = serializers.SerializerMethodField()
    daily_consumption_data = serializers.SerializerMethodField()

    class Meta:
        model = Branch
        fields = ["id", "name", "client", "address", "email", "is_active", "devices", "usage_hours", "demand_values", "daily_consumption_data"]

    def get_devices(self, obj: Branch):
        """
        Modify the devices to include usage_hours for each device.
        """
        start_date = self.context.get("start_date")
        end_date = self.context.get("end_date")
        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        devices_data = []
        for device in obj.device_set.all():
            try:
                # usage_hours = device.get_hours_of_use(start_date, end_date)
                readings = device.reading_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values()
                usage_hours = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

            except Exception as e:
                print(f"Error in calculating usage hours for device {device.name}: {e}")
                usage_hours = 0

            # Serialize device data with usage hours
            device_data = DeviceSerializer(device, context=self.context).data
            device_data["usage_hours"] = usage_hours
            devices_data.append(device_data)

        return devices_data

    def get_usage_hours(self, obj: Branch):
        """
        Aggregate usage hours across devices and add other details.
        """
        try:
            start_date = self.context.get("start_date")
            end_date = self.context.get("end_date")
            start_date = time_helpers.convert_date(start_date)
            end_date = time_helpers.convert_date(end_date)

            devices = obj.device_set.all()
            device_names = [device.name for device in devices]
            device_hours = [device.get_hours_of_use(start_date, end_date) for device in devices]

            period_total_hours = sum(device_hours)
            # Example logic for calculating blackout (mock logic provided)
            black_out = max(0, 24 * (end_date - start_date).days - period_total_hours)

            return {
                "devices": device_names,
                "hours": device_hours,
                "period_total_hours": period_total_hours,
                "black_out": black_out
            }

        except Exception as e:
            print(f"Error in get_usage_hours: {e}")
            return {
                "devices": [],
                "hours": [],
                "period_total_hours": 0,
                "black_out": 0
            }

    def get_demand_values(self, obj: Branch):

        start_date = self.context.get("start_date")
        end_date = self.context.get("end_date")

        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        total_energy = obj.branch_total_energy(start_date, end_date)
        demand = obj.branch_demand(start_date, end_date)
        demand["total_kwh"] = total_energy

        return demand

    def get_daily_consumption_data(self, obj: Branch):

        start_date = time_helpers.convert_date(self.context.get("start_date"))
        end_date = time_helpers.convert_date(self.context.get("end_date"))

        daily_kwh_data = obj.get_periodic_device_usage_daily(start_date, end_date, "daily")

        devices = []
        for device_index, device in enumerate(obj.device_set.all()):
            device_data = {
                'name': device.name,
                'device_id': device.id,
                'is_load': device.is_load,
                'is_source': device.is_source,
                'device_type': device.type.choice_name,
                'id': device_index + 1,
                'daily_kwh': daily_kwh_data.get(device.name, [])
            }
            devices.append(device_data)

        return {
            "devices": devices,
            "dates": daily_kwh_data["dates"],
        }

class BranchAdminSerializer(serializers.ModelSerializer):
    # get_diesel_overview = serializers.ReadOnlyField()
    # get_utility_overview = serializers.ReadOnlyField()
    get_total_energy = serializers.ReadOnlyField()

    class Meta:
        model = Branch
        fields = '__all__'
        #fields = ['name', 'client', 'get_utility_overview', 'get_total_energy', 'get_diesel_overview']

class DeviceTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceType
        fields = '__all__'

class AdminTariffSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminTariff
        fields = '__all__'

class UpdateAdminTariffSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminTariff
        fields = ['amount']

class ListAdminTariffSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminTariff
        fields = ['id', 'branch', 'device', 'amount']

################################### V2 Admin ##############################################
import time
def time_it(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f" I HAVE STARTED {func.__name__}")
        result = func(*args, **kwargs)
        end_time = time.time()
        print("???????????????????????????????????????????\n\n\n\n")
        print(f"Execution time for {func.__name__}: {end_time - start_time} seconds")
        print("\n\n\n\n))))))))))))))))))))))))))))))))))))))))))))))))))))))))))")
        return result
    return wrapper

class OldClientBranchListTableSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    baseline_energy_used = serializers.SerializerMethodField()
    blended_cost_of_energy = serializers.SerializerMethodField()
    diesel_usage_accuracy = serializers.SerializerMethodField()
    utility_usage_accuracy = serializers.SerializerMethodField()
    deviation_hours = serializers.SerializerMethodField()
    papr = serializers.SerializerMethodField()
    fuel_efficiency = serializers.SerializerMethodField()
    generator_size_efficiency_1 = serializers.SerializerMethodField()
    generator_size_efficiency_2 = serializers.SerializerMethodField()
    generator_size_efficiency_3 = serializers.SerializerMethodField()

    class Meta:
        model = Branch
        fields = [
            "id",
            "name",
            "baseline_energy_used",
            "blended_cost_of_energy",
            "diesel_usage_accuracy",
            "utility_usage_accuracy",
            "deviation_hours",
            "papr",
            "fuel_efficiency",
            "generator_size_efficiency_1",
            "generator_size_efficiency_2",
            "generator_size_efficiency_3"
        ]
        read_only_fields = fields

    def get_cache_key(self, obj, field):
        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        key = f"{obj.id}_{field}_{start_date}_{end_date}"
        sanitized_key = hashlib.md5(key.encode()).hexdigest()
        return sanitized_key

    def get_name(self, obj: Branch) -> str:
        return obj.name

    def get_baseline_energy_used(self, obj: Branch) -> Decimal:
        cache_key = self.get_cache_key(obj, 'baseline_energy_used')
        cached_value = cache.get(cache_key)

        if cached_value is not None:
            return cached_value

        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        # start_date, end_date = time_helpers.reverse_date_order(start_date, end_date)
        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        devices = main.models.Device.objects.filter(branch=obj.id)

        energy = [device.get_total_kwh_for_period(start_date, end_date) for device in devices]
        energy_sum = round(sum(energy), 2)

        return energy_sum

    def get_blended_cost_of_energy(self, obj: Branch) -> Decimal:
        cache_key = self.get_cache_key(obj, 'blended_cost_of_energy')
        cached_value = cache.get(cache_key)

        if cached_value is not None:
            return cached_value

        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        end_date = time_helpers.convert_date(end_date)
        start_date = time_helpers.convert_date(start_date)

        blended_cost = round(obj.get_branch_blended_cost(start_date, end_date), 2)
        cache.set(cache_key, blended_cost, timeout=60*15)  # Cache for 15 minutes

        return blended_cost

    def get_diesel_usage_accuracy(self, obj: Branch) -> Decimal:
        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        return 0

    def get_utility_usage_accuracy(self, obj: Branch) -> Decimal:
        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        return 0

    def get_deviation_hours(self, obj: Branch) -> Decimal:
        cache_key = self.get_cache_key(obj, 'deviation_hours')
        cached_value = cache.get(cache_key)

        if cached_value is not None:
            return cached_value

        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        devices = Device.objects.filter(branch=obj)
        gens = [device for device in devices if device.is_gen]

        deviation_hours = 0
        for device in gens:
            value = device.get_operating_time(start_date, end_date)
            hours = value["estimated_time_wasted"]["value"]
            deviation_hours += hours

        uptime = time_helpers.convert_float_to_hrs_and_mins(deviation_hours)
        cache.set(cache_key, uptime, timeout=60*15)  # Cache for 15 minutes

        return uptime

    def get_papr(self, obj: Branch) -> Decimal:
        cache_key = self.get_cache_key(obj, 'papr')
        cached_value = cache.get(cache_key)

        if cached_value is not None:
            return cached_value

        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')

        papr = obj.branch_demand(start_date, end_date)['papr']
        cache.set(cache_key, papr, timeout=60*15)  # Cache for 15 minutes

        return papr

    def get_fuel_efficiency(self, obj: Branch) -> Decimal:
        start_date = self.context.get('start_date')
        end_date = self.context.get('end_date')
        return 0

    @cached_property
    def generator_efficiencies_cache(self):
        # Cache the efficiencies by branch id.
        return {}

    def get_generator_efficiencies(self, obj: Branch):
        if obj.id not in self.generator_efficiencies_cache:
            end_date = self.context.get('end_date')
            end_date = time_helpers.convert_date(end_date)
            devices = Device.objects.filter(branch=obj)
            efficiencies = [
                device.get_gen_efficiency(end_date)["usage"] for device in devices if device.is_gen
            ]
            self.generator_efficiencies_cache[obj.id] = efficiencies
        return self.generator_efficiencies_cache[obj.id]

    def get_field_efficiency(self, index, obj: Branch):
        efficiencies = self.get_generator_efficiencies(obj)
        return efficiencies[index] if index < len(efficiencies) else "-"

    def get_generator_size_efficiency_1(self, obj: Branch) -> str:
        return self.get_field_efficiency(0, obj)

    def get_generator_size_efficiency_2(self, obj: Branch) -> str:
        return self.get_field_efficiency(1, obj)

    def get_generator_size_efficiency_3(self, obj: Branch) -> str:
        return self.get_field_efficiency(2, obj)

# class ClientBranchListTableSerializer(serializers.ModelSerializer):
#     # We declare fields to simplify access within the serializer
#     baseline_energy_used = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
#     blended_cost_of_energy = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
#     diesel_usage_accuracy = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
#     utility_usage_accuracy = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
#     deviation_hours = serializers.CharField(read_only=True)
#     papr = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
#     fuel_efficiency = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
#     generator_size_efficiency_1 = serializers.CharField(read_only=True)
#     generator_size_efficiency_2 = serializers.CharField(read_only=True)
#     generator_size_efficiency_3 = serializers.CharField(read_only=True)

#     class Meta:
#         model = Branch
#         fields = [
#             "id",
#             "name",
#             "baseline_energy_used",
#             "blended_cost_of_energy",
#             "diesel_usage_accuracy",
#             "utility_usage_accuracy",
#             "deviation_hours",
#             "papr",
#             "fuel_efficiency",
#             "generator_size_efficiency_1",
#             "generator_size_efficiency_2",
#             "generator_size_efficiency_3",
#         ]
#         read_only_fields = fields

#     @staticmethod
#     def generate_cache_key(obj_id, field_name, start_date, end_date):
#         # Create a cache key unique to each object and field, along with date range
#         key = f"{obj_id}_{field_name}_{start_date}_{end_date}"
#         return hashlib.md5(key.encode()).hexdigest()

#     def to_representation(self, instance):
#         # Get context parameters for dates
#         start_date = self.context.get('start_date')
#         end_date = self.context.get('end_date')

#         start_date = time_helpers.convert_date(start_date)
#         end_date = time_helpers.convert_date(end_date)

#         # Generate cache key for the entire serialized data
#         cache_key = self.generate_cache_key(instance.id, 'complete_data', start_date, end_date)
#         cached_data = cache.get(cache_key)

#         # Return cached data if available
#         if cached_data:
#             return cached_data

#         # No cached data, so we compute it now
#         representation = super().to_representation(instance)

#         # Cache each field separately as required and store precomputed complex fields
#         representation['baseline_energy_used'] = self.get_baseline_energy_used(instance, start_date, end_date)
#         representation['blended_cost_of_energy'] = self.get_blended_cost_of_energy(instance, start_date, end_date)
#         representation['diesel_usage_accuracy'] = self.get_diesel_usage_accuracy(instance, start_date, end_date)
#         representation['utility_usage_accuracy'] = self.get_utility_usage_accuracy(instance, start_date, end_date)
#         representation['deviation_hours'] = self.get_deviation_hours(instance, start_date, end_date)
#         representation['papr'] = self.get_papr(instance, start_date, end_date)
#         representation['fuel_efficiency'] = self.get_fuel_efficiency(instance, start_date, end_date)
#         representation['generator_size_efficiency_1'] = self.get_generator_size_efficiency(1, instance, end_date)
#         representation['generator_size_efficiency_2'] = self.get_generator_size_efficiency(2, instance, end_date)
#         representation['generator_size_efficiency_3'] = self.get_generator_size_efficiency(3, instance, end_date)

#         # Cache the complete data for faster retrieval on subsequent requests
#         cache.set(cache_key, representation, timeout=60 * 15)  # Cache for 15 minutes

#         return representation

#     def get_baseline_energy_used(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'baseline_energy_used', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         devices = main.models.Device.objects.filter(branch=obj.id)
#         energy = [device.get_total_kwh_for_period(start_date, end_date) for device in devices]

#         energy_sum = round(sum(energy), 2)
#         cache.set(cache_key, energy_sum, timeout=60 * 15)
#         return energy_sum

#     def get_blended_cost_of_energy(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'blended_cost_of_energy', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         blended_cost = round(obj.get_branch_blended_cost(start_date, end_date), 2)
#         cache.set(cache_key, blended_cost, timeout=60 * 15)
#         return blended_cost

#     def get_diesel_usage_accuracy(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'diesel_usage_accuracy', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         usage = 0
#         cache.set(cache_key, usage, timeout=60 * 15)
#         return 0

#     def get_utility_usage_accuracy(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'utility_usage_accuracy', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         usage = 0
#         cache.set(cache_key, usage, timeout=60 * 15)
#         return usage

#     def get_deviation_hours(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'deviation_hours', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         devices = Device.objects.filter(branch=obj)
#         gens = [device for device in devices if device.is_gen]

#         deviation_hours = 0
#         for device in gens:
#             value = device.get_operating_time(start_date, end_date)
#             hours = value["estimated_time_wasted"]["value"]
#             deviation_hours += hours

#         uptime = time_helpers.convert_float_to_hrs_and_mins(deviation_hours)

#         cache.set(cache_key, uptime, timeout=60 * 15)
#         return uptime

#     def get_papr(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'papr', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         papr = obj.branch_demand(start_date, end_date)['papr']
#         cache.set(cache_key, papr, timeout=60*15)

#         return papr

#     def get_fuel_efficiency(self, obj, start_date, end_date):
#         cache_key = self.generate_cache_key(obj.id, 'fuel_efficiency', start_date, end_date)
#         cached_value = cache.get(cache_key)
#         if cached_value is not None:
#             return cached_value

#         efficiency = 0
#         cache.set(cache_key, efficiency, timeout=60 * 15)
#         return efficiency

#     @staticmethod
#     def get_generator_size_efficiency(index, obj, end_date):
#         # Using generator efficiencies cached in a context property
#         if 'generator_efficiencies_cache' not in obj.__dict__:
#             devices = Device.objects.filter(branch=obj, type_id=1)
#             obj.__dict__['generator_efficiencies_cache'] = [
#                 device.get_gen_efficiency(end_date).get('usage', '-') for device in devices
#             ]

#         efficiencies = obj.__dict__['generator_efficiencies_cache']
#         return efficiencies[index - 1] if index - 1 < len(efficiencies) else "-"



class MonthlyBranchMetricsSerializer(serializers.ModelSerializer):
    branch_name = serializers.CharField(source="branch.name", read_only=True)
    client_id = serializers.IntegerField(source="client.id", read_only=True)
    def to_representation(self, instance):
        data = super().to_representation(instance)
        metrics = data.pop("metrics", {}) or {}
        data.update(metrics)
        return data

    class Meta:
        model = MonthlyBranchMetrics
        fields = [
            "id", "client_id", "branch", "branch_name", "month", "year", "metrics", "created_at", "updated_at"
        ]
        read_only_fields = fields



# class ClientBranchesEnergySerializer(serializers.ModelSerializer):
#     name = serializers.SerializerMethodField()
#     generators_energy = serializers.SerializerMethodField()
#     utility_energy = serializers.SerializerMethodField()

#     class Meta:
#         model = Branch
#         fields = [
#             "id",
#             "name",
#             "generators_energy",
#             "utility_energy",
#         ]
#         read_only_fields = fields

#     def get_name(self, obj: Branch) -> str:
#         print(self)
#         return obj.name

#     def get_generators_energy(self, obj: Branch) -> Decimal:
#         start_date = self.context.get('start_date')
#         end_date = self.context.get('end_date')
#         # start_date, end_date = time_helpers.reverse_date_order(start_date, end_date)

#         devices = main.models.Device.objects.filter(branch=obj.id)
#         generators = [device for device in devices if device.is_gen]

#         generators_energy = [device.get_total_kwh_for_period(start_date, end_date) for device in generators]
#         generators_energy_sum = round(sum(generators_energy), 2)

#         return generators_energy_sum

#     def get_utility_energy(self, obj: Branch) -> Decimal:
#         start_date = self.context.get('start_date')
#         end_date = self.context.get('end_date')
#         # start_date, end_date = time_helpers.reverse_date_order(start_date, end_date)

#         devices = main.models.Device.objects.filter(branch=obj.id)
#         utility = [device for device in devices if device.is_utility]

#         utility_energy = [device.get_total_kwh_for_period(start_date, end_date) for device in utility]
#         utility_energy_sum = round(sum(utility_energy), 2)

#         return utility_energy_sum

class ClientBranchesEnergySerializer(serializers.ModelSerializer):
    name = serializers.CharField()
    generators_energy = serializers.SerializerMethodField()
    utility_energy = serializers.SerializerMethodField()

    class Meta:
        model = Branch
        fields = ["id", "name", "generators_energy", "utility_energy"]
        read_only_fields = fields

    def get_generators_energy(self, obj: Branch) -> float:
        branch_device_map = self.context.get('branch_device_map', {})
        datalog_map = self.context.get('datalog_map', {})
        devices = branch_device_map.get(obj.id, [])
        # Use your is_gen property for generator detection
        generators = [d for d in devices if d.is_gen]
        total = 0.0
        for device in generators:
            min_val, max_val = datalog_map.get(device.id, (0, 0))
            total += round((max_val - min_val), 2)
        return round(total, 2)

    def get_utility_energy(self, obj: Branch) -> float:
        branch_device_map = self.context.get('branch_device_map', {})
        datalog_map = self.context.get('datalog_map', {})
        devices = branch_device_map.get(obj.id, [])
        # Use your is_utility property for utility detection
        utilities = [d for d in devices if d.is_utility]
        total = 0.0
        for device in utilities:
            min_val, max_val = datalog_map.get(device.id, (0, 0))
            total += round((max_val - min_val), 2)
        return round(total, 2)

class ClientBranchesSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Branch
        fields = [
            "id",
            "name",
        ]
        read_only_fields = fields

    def get_name(self, obj: Branch) -> str:
        print(self)
        return obj.name

class ClientUtilityEnergySerializer(serializers.ModelSerializer):
    utility_energy_overview = serializers.SerializerMethodField()

    class Meta:
        model = Client
        fields = ('utility_energy_overview',)

    def get_utility_energy_overview(self, obj):
        client_id = self.context.get('client_id')
        year = self.context.get('year')
        now = datetime.datetime.now()

        if year:
            year = int(year)
            months_to_calculate = [(month, year) for month in range(1, 13)]
        else:
            months_to_calculate = [
                ((now - relativedelta(months=i)).month, (now - relativedelta(months=i)).year)
                for i in range(11, -1, -1)
            ]

        utility_energy_overview = []

        # Get active branches and utility devices for the client
        branches = Branch.objects.filter(client_id=client_id, is_active=True)
        devices = Device.objects.filter(client_id=client_id, branch__in=branches)
        utility_devices = [device.id for device in devices if device.is_utility]

        # Loop through the months to calculate utility energy
        for month, year in months_to_calculate:
            start_date = datetime.datetime(year, month, 1)
            end_date = start_date + relativedelta(months=1) - relativedelta(days=1)
            month_name = start_date.strftime("%b, %Y")

            total_energy_diff = Reading.objects.filter(
                device__in=utility_devices,
                post_datetime__range=(start_date, end_date)
            ).aggregate(
                total_energy=Sum(F('kwh_import') - F('kwh_import'))
            )['total_energy'] or 0

            utility_energy_overview.append({
                'month': month_name,
                'utility_energy': round(total_energy_diff, 2),
            })

        # Ensure the latest month is the last on the list
        return utility_energy_overview

class AdminUserSerializer(serializers.ModelSerializer):
    branches = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ["id", "username", "branches", "phone_number", "email", "is_active", "roles", "client", "created_at"]

    def get_branches(self, obj: User) -> list:

        perm = obj.view_permissions.all()
        serializer = ViewSerializer(perm, many=True)
        list = []
        for data in serializer.data:
            branch = Branch.objects.get(id=data['branch'])
            list.append(branch.name)

        return list

class ClientEnergyCostParamsSerializer(serializers.Serializer):
    client_id = serializers.IntegerField()
    year = serializers.IntegerField(required=False)

class TargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = Target
        fields = '__all__'

class DieselOverviewSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    current_month_consumption_in_litres = serializers.SerializerMethodField()
    previous_day_consumption_in_litres = serializers.SerializerMethodField()
    remaining_diesel_litres = serializers.SerializerMethodField()
    pagination_class = pagination.TenPerPagePagination

    class Meta:
        model = Branch
        fields = [
            "id",
            "name",
            "current_month_consumption_in_litres",
            "previous_day_consumption_in_litres",
            "remaining_diesel_litres",
        ]
        read_only_fields = fields

    def get_name(self, obj: Branch) -> str:
        return obj.name

    def get_current_month_consumption_in_litres(self, obj: Branch) -> Decimal:
        import datetime
        now = datetime.datetime.now()
        start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(now)
        # Get all the entries for the given branch and month
        entries = FuelConsumption.objects.filter(branch=obj, fuel_type="Daily", start_date__gte=start_date, end_date__lte=end_date)

        # Calculate the sum of quantity for all entries
        total_quantity = entries.aggregate(sum=Sum('quantity'))['sum'] or 0.0

        return total_quantity

    def get_previous_day_consumption_in_litres(self, obj: Branch) -> Decimal:
        import datetime
        # Get yesterday's date
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        yesterday_date = yesterday.date()

        # Get all the entries for the given branch and month
        entries = FuelConsumption.objects.filter(
            branch=obj,
            fuel_type="Daily",
            start_date__gte=yesterday_date,  # Filters entries starting on 'yesterday'
            end_date__lte=yesterday_date  # Filters entries ending on 'yesterday'
        )
        # Calculate the sum of quantity for all entries
        total_quantity = entries.aggregate(sum=Sum('quantity'))['sum'] or 0.0

        return total_quantity

    def get_remaining_diesel_litres(self, obj: Branch) -> Decimal:

        import datetime
        now = datetime.datetime.now()
        start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(now)

        entries = FuelConsumption.objects.filter(branch=obj, fuel_type="Monthly", start_date__gte=start_date, end_date__lte=end_date)

        # Calculate the sum of quantity for all entries
        total_quantity = entries.aggregate(sum=Sum('quantity'))['sum'] or 0.0

        current_month_consumption = self.get_current_month_consumption_in_litres(obj)

        remaining_diesel = total_quantity-current_month_consumption

        return remaining_diesel

class SupportTicketSerializer(serializers.ModelSerializer):
    class Meta:
        model = SupportTicket
        exclude = ["uuid", "updated_at"]
        read_only_fields = ["status", "user"]
        # fields = '__all__'
