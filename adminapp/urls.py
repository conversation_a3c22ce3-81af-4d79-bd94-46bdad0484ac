"""wyre URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.urls import path
from adminapp import views

urlpatterns = [
    path('clients', views.ClientApiView.as_view(), name='clients'),
    path('client/<int:id>', views.ClientApiView.as_view(), name='client'),
    path('update_client/<int:client_id>', views.update_client, name='update_client'),
    path('delete_client/<int:client_id>', views.delete_client, name='delete_client'),
    path('suspend_client/<int:client_id>/', views.suspend_client, name='suspend_client'),
    path('client/<int:client_id>/branches-devices/', views.client_branches_devices, name='client_branches_devices'),

    path('admin/<str:start_date>/<str:end_date>/<str:frequency>', views.admin_overview),
    path('clients/<str:start_date>/<str:end_date>', views.client_overview),
    path('branches/<int:client_id>/<str:start_date>/<str:end_date>', views.client_branches_overview_1),
    path('branches-sum/<int:client_id>/<str:start_date>/<str:end_date>', views.client_branches_overview_2),
    path('branch/<int:branch_id>/<str:start_date>/<str:end_date>', views.branch_detail),
    path('branch/users/<int:branch_id>', views.branch_users),
    path('branch/devices/<int:branch_id>', views.branch_devices),
    path('device-types', views.device_types),
    path('roles', views.roles),
    path('add_user/<int:user_id>', views.user_pemit),
    path('client/branches/<str:start_date>/<str:end_date>', views.all_branches_overview),
    path('branch_baseline/<int:branch_id>/<str:end_date>/', views.branch_baseline),
    path('change-user-role/<int:user_id>/<int:new_role>/', views.change_user_role_view, name='change_user_role'),
    path('branch_energy_stats/<int:branch_id>/<str:start_date>/<str:end_date>', views.branch_energy_stats),

    path('devices/', views.DeviceApiView.as_view(), name='devices'),
    path('devices/<int:client>', views.DeviceApiView.as_view(), name='devices'),
    path('device/<int:id>', views.update_device, name='update_device'),
    path('device/<int:id>/delete', views.delete_device, name='delete_device'),
    path('device_state/<int:device_id>/', views.device_state),
    path('edit_device/<int:id>/', views.edit_device_name_id, name='edit_device_name_id'),

    path('branches/', views.BranchApiView.as_view(), name='branches'),
    path('branches/<int:client>', views.BranchApiView.as_view(), name='branches'),
    path('branch/<int:id>', views.update_branch, name='update_branch'),
    path('branch/<int:id>/delete', views.delete_branch, name='delete_branch'),
    path('suspend_branch/<int:branch_id>/', views.suspend_branch, name='suspend_branch'),

    path('users/', views.UserApiView.as_view(), name='users'),
    path('users/<int:client>', views.UserApiView.as_view(), name='users'),
    path('user/<int:id>', views.update_user, name='update_user'),
    path('user/<int:id>/delete', views.delete_user, name='delete_user'),
    path('user_state/<int:user_id>/', views.user_state),

    path('tariffs/', views.list_tariffs, name='list_tariffs'),
    path('tariffs/<int:id>/', views.get_tariff, name='get_tariff'),
    path('tariffs/create/', views.create_tariff, name='create_tariff'),
    path('tariffs/<int:id>/update/', views.update_tariff, name='update_tariff'),
    path('tariffs/<int:id>/delete/', views.delete_tariff, name='delete_tariff'),

    path('reseller_branches/<int:client_id>/<str:start_date>/<str:end_date>', views.reseller_branches_overview_1),
    path('reseller-branches-sum/<int:client_id>/<str:start_date>/<str:end_date>', views.reseller_branches_overview_2),
    path('reseller_branch/<int:branch_id>/<str:start_date>/<str:end_date>', views.reseller_branch_detail),
    path('reseller_energy_stats/<int:branch_id>/<str:start_date>/<str:end_date>', views.reseller_energy_stats),

    ############################ v2 ###########################################

    path('client-header-endpoints/<int:client_id>/<str:start_date>/<str:end_date>', views.admin_header_endpoint),
    path('client-header-cost-endpoints/<int:client_id>/<str:start_date>/<str:end_date>', views.admin_header_cost_endpoint),
    # path('client-branches-energy/<int:client_id>/<str:start_date>/<str:end_date>/', views.ClientBranchesEnergy.as_view(), name='client_branches_energy'),
    path('client-branches-energy/<int:client_id>/', views.ClientBranchesEnergy.as_view(), name='client_branches_energy'),
    path('client-utility-cost/', views.ClientUtilityCostView.as_view(), name='client_utility_cost'),
    path('client/<int:client_id>/monthly-utility-cost/', views.client_monthly_utility_cost, name='client_monthly_utility_cost'),
    path('client-diesel-cost/', views.ClientDieselCostView.as_view(), name='client_diesel_cost'),
    path('client/<int:client_id>/monthly-diesel-cost/', views.client_monthly_diesel_cost, name='client_monthly_diesel_cost'),
    path('client-diesel-litres/', views.ClientDieselLitresView.as_view(), name='client_diesel_litres'),
    path('client/<int:client_id>/monthly-diesel-litres/', views.client_monthly_diesel_litres, name='client_monthly_diesel_litres'),
    path('client-utility-energy/', views.ClientUtilityEnergy.as_view(), name='client_utility_energy'),
    path('client/<int:client_id>/monthly-utility-energy/', views.client_monthly_utility_energy, name='client_monthly_utility_energy'),
    path('client-branches/<int:client_id>/', views.ClientBranches.as_view(), name='client_branches'),
    # path('key-metrics/<int:client_id>/<str:start_date>/<str:end_date>/', views.AdminKeyMetrics.as_view()),
    path('key-metrics/<int:client_id>/', views.AdminKeyMetrics.as_view()),
    path('client-diesel-overview/<int:client_id>/', views.DieselOverview.as_view()),
    path('procurements/<int:branch_id>/', views.Procurements.as_view()),
    path('consumptions/<int:branch_id>/', views.Consumptions.as_view()),
    path('target/<int:client_id>/', views.AdminTarget.as_view(), name='target'),

    ############################ BULK MONITORING CONFIGURATION ###########################################
    path('client-dashboard-config/<int:client_id>/', views.ClientDashboardConfig.as_view()),
    path('all-time-consumption/<int:client_id>/', views.AllTimeConsumptionView.as_view()),
    path('this-month-consumption/<int:client_id>/', views.ThisMonthConsumptionView.as_view()),
    path('last-month-consumption/<int:client_id>/', views.LastMonthConsumptionView.as_view()),
    path('amount/<int:client_id>/', views.AmountDueView.as_view()),
    path('devices-list/<int:client_id>/', views.DevicesListView.as_view()),

    # BRANCH DETAIL
    path('branch-detail/<int:id>/', views.BranchDetailView.as_view(), name='branch-detail'),

    # CLIENT USERS
    path('client_user_branch_permissions/<int:user_id>/', views.user_pemit),
    path("clients/<int:client_id>/users/", views.ClientUser.as_view(), name='client-user-list'),
    path('clients/<int:client_id>/users/<int:pk>/', views.ClientUser.as_view(), name='client-user-detail'),

    # client: support ticket
    path("support-ticket/", views.CreateSupportTicketAPIView.as_view(), name="create_support_ticket"),
    path("support/list-support-ticket/<int:client_id>/", views.ListSupportTicketAPIView.as_view(), name="list_support_ticket"),
    path("support-ticket/<int:id>/", views.SupportTicketAPIView.as_view(), name="support_ticket"),

    path('force-login-client-admin/<int:client_id>/', views.force_login_client_admin, name='force_login_client_admin'),
]
