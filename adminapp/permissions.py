from rest_framework.exceptions import APIException
from rest_framework import status
from django.contrib import auth
from django.contrib.auth import login
from rest_framework import permissions

# ADMIN PERMISSION

class IsAdmin(permissions.BasePermission):
    """
    View based permission check api key.
    """

    def has_permission(self, request, view):

        user_role = request.user.roles

        if user_role <= 4: # MUST BE ADMIN, SUPER ADMIN, CLIENT ADMIN or OPERATOR

            return True

        else:

            raise NoAdminPermission()

class NoAdminPermission(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {'error': True, 'message': 'You Need to have Admin permissions'}
    default_code = 'Not permitted'

# END ADMIN PERMISSION

# SUPER ADMIN PERMISSION
class IsSuperAdmin(permissions.BasePermission):
    """
    View based permission check api key.
    """

    def has_permission(self, request, view):

        user_role = request.user.roles

        if user_role == 1: # MUST BE ADMIN OR SUPER ADMIN

            return True

        else:

            raise NoSuperAdminPermission()

class NoSuperAdminPermission(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {'error': True, 'message': 'You Need to have SuperAdmin permissions'}
    default_code = 'Not permitted'

# END SEND ADMIN PERMISSION

class IsCreator(permissions.BasePermission):

    def has_object_permission(self, request, view, obj):

        user_role = request.user.roles

        if user_role <= 3: # only Admins or the Creator

            return True

        if request.method in permissions.SAFE_METHODS:

            return True
            
        if obj.author == request.user:

            return True

        else:

            raise NoIsCreatorPermission()

class NoIsCreatorPermission(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {'error': True, 'message': 'Editing is restricted to the Creator or Admin only'}
    default_code = 'Not permitted'

# WYRE ADMIN PERMISSION
class IsWyreAdmin(permissions.BasePermission):
    """
    View based permission check api key.
    """

    def has_permission(self, request, view):

        user_role = request.user.roles

        if user_role == 2: # MUST BE WYRE ADMIN

            return True

        else:

            raise NoWyreAdminPermission()

class NoWyreAdminPermission(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {'error': True, 'message': 'You Need to have WyreAdmin permissions'}
    default_code = 'Not permitted'