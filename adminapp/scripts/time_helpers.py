import pytz
import datetime, os
from dateutil.relativedelta import relativedelta

### CONVERT FROM ONE TIME ZONE TO ANOTHER
def convert_timezone(_from,  to):

    # samples 'Africa/Lagos', 'US/Central'
    source_zone = pytz.timezone(_from)
    target_zone = pytz.timezone(to)
    curtime = source_zone.localize(datetime.datetime.now())
    curtime = curtime.astimezone(target_zone)

    return curtime


###  MAKE DATETIME AWARE OF SERVER TIMEZONE
def localize_time(time):

    target_zone = pytz.timezone(os.getenv("time_zone"))
    localized_time = target_zone.localize(time)

    return time


def convert_date(date):
    # RETURN DATE OBJECT FROM SET FORMAT DD-MM-YY HH:MM

    date_object = datetime.datetime.strptime(date, "%d-%m-%Y %H:%M")

    return date_object

def str_to_time(string:str)->datetime.time:
    
    datetime_object = datetime.datetime.strptime(string, '%H:%M').time()
    
    return datetime_object

def get_start_and_end_of_month_from_date(date_input):

    # Check if the input is a string and convert it to datetime if necessary
    if isinstance(date_input, str):
        # Assuming the string is in a recognizable format for strptime to parse
        date = datetime.strptime(date_input, "%Y-%m-%d")
    else:
        date = date_input

    date_string = f"{date.year}-{date.month}-{date.day} 00:00:00"
    date_obj = datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")

    start_of_month = date_obj - datetime.timedelta(days = date.day-1)
    aux_end_date = start_of_month + datetime.timedelta(days = 33)
    end_date = aux_end_date - datetime.timedelta(days = aux_end_date.day - 1)

    return  start_of_month, end_date

def to_standard_date_str(date):

    date_string = f"{date.year}-{date.month}-{date.day} 00:00:00"
    date_obj = datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")

    start_of_month = date_obj - datetime.timedelta(days = date.day-1)
    aux_end_date = start_of_month + datetime.timedelta(days = 33)
    end_date = aux_end_date - datetime.timedelta(days = aux_end_date.day - 1)

    return  start_of_month, end_date

# THIS FUNCTION GETS THE CORRESPONDING DATE FOR WHICH SCORECARDS IS BEING CALCULATED SO THAT THIS VALUE CAN BE REGISTERED IN THE SCORECARDS PAGE
def score_cards_date(end_date):
    months_before = 0
    now = end_date
    from_datetime = now - relativedelta(months=months_before)
    modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    return modified_from_datetime.strftime("%B, %Y")

def previous_month(date):
    
    today = date
    first = today.replace(day=1)
    lastMonth = first #- datetime.timedelta(hours=1)
    print(lastMonth.strftime("%Y%m"))
    return lastMonth

def to_month_and_year(date):
    
    return date.strftime("%d, %B %Y")


def get_start_date_from(end_date, period):

    "PERIOD CAN BE (week, month, half-year, year)"
    
    if period == "week":
        start_date = end_date-datetime.timedelta(days = 7)

    elif period == "month":
        start_date = previous_month(end_date)

    elif period == "half-year":
        start_date = end_date

        for i in range(6):
            start_date = previous_month(start_date)    

    elif period == "year":
        start_date = end_date.replace(day=1, month=1, hour = 0)
    
    else:
        start_date = previous_month(end_date)

    return start_date
    
def reverse_date_order(start_date, end_date):
    from datetime import datetime
    # Convert date order from %Y-%m-%d to %d-%m-%Y

    # Convert string dates to datetime objects
    start_date = datetime.strptime(start_date, "%d-%m-%Y %H:%M")
    end_date = datetime.strptime(end_date, "%d-%m-%Y %H:%M")

    # Format the dates as strings in the desired format
    formatted_start_date = start_date.strftime("%Y-%m-%d %H:%M")
    formatted_end_date = end_date.strftime("%Y-%m-%d %H:%M")

    return formatted_start_date, formatted_end_date

def convert_float_to_hrs_and_mins(float):
    hours = int(float)  # Get the whole number part for hours
    minutes = int((float - hours) * 60)  # Multiply the fractional part by 60

    # Construct the string
    if hours != 1:
        hour_label = "Hrs"
    else:
        hour_label = "Hr"

    if minutes != 1:
        minute_label = "Mins"
    else:
        minute_label = "Min"

    hours_and_minutes = f"{hours} {hour_label} : {minutes} {minute_label}"
    return hours_and_minutes

def parse_and_format_date(date_str):
    """
    Convert a date string from 'YYYY-MM-DD' to 'DD-MM-YYYY HH:MM' format.
    If time is missing, assume '00:00' as default.
    """
    try:
        # Parse the date string in 'YYYY-MM-DD' format
        parsed_date = datetime.datetime.strptime(date_str, "%Y-%m-%d")
        
        # Convert it to the desired format: 'DD-MM-YYYY HH:MM'
        formatted_date = parsed_date.strftime("%d-%m-%Y %H:%M")
        
        return formatted_date
    except ValueError:
        raise ValueError(f"Date {date_str} is not in the correct format YYYY-MM-DD")
