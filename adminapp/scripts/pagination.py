from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class TenPerPagePagination(PageNumberPagination):
    page_size = 10
    page_query_param = "page"

    def get_paginated_response(self, data) -> Response:
        page = self.page
        total_pages = page.paginator.num_pages
        page_count = self.page_size

        return Response(
            {
                "page": page.number,
                "total_pages": total_pages,
                "count": page_count,
                "results": data,
            }
        )
