#!/usr/bin/env python
import os

# Path to the file we need to patch
file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                         'venv/lib/python3.9/site-packages/rest_framework_jwt/authentication.py')

# Read the file content
with open(file_path, 'r') as f:
    content = f.read()

# Replace smart_text with smart_str
content = content.replace('from django.utils.encoding import smart_text',
                         'from django.utils.encoding import smart_str as smart_text')

# Replace ugettext with gettext
content = content.replace('from django.utils.translation import ugettext as _',
                         'from django.utils.translation import gettext as _')

# Write the updated content back to the file
with open(file_path, 'w') as f:
    f.write(content)

print("JWT authentication file patched successfully!")
