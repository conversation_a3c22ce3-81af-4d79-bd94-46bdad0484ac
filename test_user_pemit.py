import requests
import json

# Replace with your actual server URL and authentication token
BASE_URL = 'http://localhost:8000'
TOKEN = 'your_auth_token'  # Replace with an actual token

# Replace with an actual user ID
USER_ID = 1

# Replace with actual branch IDs
ADD_BRANCH_IDS = [1, 2]
REMOVE_BRANCH_IDS = [3]

# Set up headers with authentication
headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {TOKEN}'
}

# Payload for the UPDATE request
payload = {
    'user': USER_ID,
    'add': ADD_BRANCH_IDS,
    'remove': REMOVE_BRANCH_IDS
}

# Make the UPDATE request
response = requests.request(
    'UPDATE',  # Using UPDATE method
    f'{BASE_URL}/adminapp/user_pemit/{USER_ID}/',
    headers=headers,
    data=json.dumps(payload)
)

# Print the response
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")
