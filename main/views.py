from http import client
from pickle import TRUE
from django.http.response import HttpResponse
from django.utils import timezone
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils.decorators import method_decorator
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, as_completed
from playwright.sync_api import sync_playwright
from main.models import Bill_Mails, Datalog, Device, Branch, ViewPermission, Bill, Alert_Setting, Reading
from account.models import Client, User
from .serializers import ClientCreateSerializer, DeviceMaintenanceSerializer, DeviceSerializer, BranchSerializer, EquipmentSerializer, ViewSerializer
# from rest_framework_jwt.authentication import JSONWebTokenAuthentication
from rest_framework_simplejwt.authentication import JWTAuthentication
from main.scripts import dashboard_helpers, time_helpers
from .serializers import ClientsAllSerializer, DeviceSerializer, BranchSerializer, BranchLeanSerializer, ViewSerializer, Bill<PERSON>erializer, BillMailSerializer, CostTrackerSerializer, Alert_SettingSerializer, Month_End_Diesel_Balance, DieselCostSerializer, UtilityCostSerializer, IPPCostSerializer, DeviceListSerializer, ReadingListSerializer, DatalogListSerializer
from main.serializers import ACB_serializer, ACB_List_serializer
from django.views.decorators.cache import cache_page
from django.db.models import Prefetch
import datetime, logging, json, calendar
from main import sample_data
from wyre import settings
from django.http import FileResponse, JsonResponse
from django.template import loader
from django.shortcuts import get_object_or_404, render
from .models import Cost, Equipment, FuelConsumption, Reports, DieselOverviewHistory, MonthlyReport
from django.db import IntegrityError, transaction
from itertools import chain
from django.db.models import Avg
from main.scripts import time_helpers, mailgun
import base64
from main.tasks import push_otd, toggle_device
from main.scripts import aggregation_helpers
from io import BytesIO
import pandas as pd
import requests
from main import serializers
from main.models import KW_TO_KVA_MULTIPLIER, Tariff
from django.core.exceptions import ObjectDoesNotExist
from rest_framework.views import APIView
from main.serializers import FuelEntryCreateSerializer
from datetime import datetime
from django.views.decorators.csrf import csrf_exempt
import openai
import csv
import os
openai.api_key = settings.OPENAI_API_KEY

# This retrieves a Python logging instance (or creates it)
logger = logging.getLogger(__name__)

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
# @permission_classes([IsAuthenticated])
def dashboard(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("||||||||||||||||||| @ DASHBOARD MAIN DATA @ |||||||||||||||||||||||")
    print(f"START DATE: {start_date}, END DATE: {end_date}")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       'dashboard': {
                                            "total_kwh": {
                                            "unit": "kWh",
                                            "value": round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
                                            },
                                            "min_demand": {
                                                "unit": "kVA",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
                                            },
                                            "max_demand": {
                                                "unit": "kVA",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
                                            },
                                            "avg_demand": {
                                                "unit": "kVA",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
                                            },
                                            "dashboard_carbon_emissions": {
                                                "unit": "Tons",
                                                "value": round(device.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value"), 2)
                                            },
                                            "cost_of_energy": {
                                                "unit": "Naira/kWh",
                                                "value": device.get_cost_of_energy(start_date, end_date)
                                            },
                                            "today": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("today_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "yesterday": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("yesterday_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "solar_hours":{
                                                "value":device.get_solar_hours_consumption(start_date, end_date),
                                                "unit":"kWh"
                                            }
                                       },
                                       "score_card": {
                                            "is_generator": device.is_gen,
                                            "baseline_energy": device.get_base_line(end_date).get("baseline_energy"),
                                            "peak_to_avg_power_ratio": device.get_papr(start_date, end_date, report=False),
                                            "score_card_carbon_emissions": device.get_carbon_emmisions_score_cards(end_date),
                                            "generator_size_efficiency": device.get_gen_efficiency(end_date),
                                            "change_over_lags": device.get_change_over_lags(start_date, end_date),
                                            "operating_time": device.get_operating_time_bigdata(start_date, end_date),
                                            "fuel_consumption": device.get_fuel_consumption_score_cards(start_date, end_date)
                                            },
                                        "power_quality": device.get_power_quality(start_date, end_date, frequency),
                                        "last_reading": device.get_last_readings(),
                                        "power_demand": device.get_power_demand(start_date, end_date, frequency),
                            #             "time_of_use":  {
                            #                 "dates": [
                            #                     "2020-12-26T13:49:08.705702",
                            #                     "2020-12-26T14:04:08.705702",
                            #                     "2020-12-26T14:16:08.705702",
                            #                     "2020-12-26T14:31:08.705702",
                            #                     "2020-12-26T14:43:08.705702",
                            #                     "2020-12-26T14:56:08.705702",
                            #                     "2020-12-26T15:11:08.705702",
                            #                     "2020-12-26T15:25:08.705702",
                            #                     "2020-12-26T15:39:08.705702",
                            #                     "2020-12-26T15:51:08.705702",
                            #                     "2020-12-26T16:03:08.705702",
                            #                     "2020-12-26T16:15:08.705702",
                            #                     "2020-12-26T16:30:08.705702",
                            #                     "2020-12-26T16:42:08.705702",
                            #                     "2020-12-26T16:56:08.705702",
                            #                     "2020-12-26T17:08:08.705702",
                            #                     "2020-12-26T17:23:08.705702",
                            #                     "2020-12-26T17:37:08.705702",
                            #                     "2020-12-26T17:49:08.705702",
                            #                     "2020-12-26T18:03:08.705702",
                            #                     "2020-12-26T18:16:08.705702",
                            #                     "2020-12-26T18:31:08.705702",
                            #                     "2020-12-26T18:45:08.705702",
                            #                     "2020-12-26T19:00:08.705702",
                            #                     "2020-12-26T19:14:08.705702",
                            #                     "2020-12-26T19:27:08.705702",
                            #                     "2020-12-26T19:39:08.705702",
                            #                     "2020-12-26T19:52:08.705702",
                            #                     "2020-12-26T20:04:08.705702",
                            #                     "2020-12-26T20:18:08.705702"
                            #                 ],
                            #                 "values": [
                            #                     10,
                            #                     7,
                            #                     10,
                            #                     8,
                            #                     10,
                            #                     5,
                            #                     5,
                            #                     9,
                            #                     5,
                            #                     10,
                            #                     9,
                            #                     8,
                            #                     8,
                            #                     9,
                            #                     8,
                            #                     9,
                            #                     9,
                            #                     8,
                            #                     7,
                            #                     9,
                            #                     6,
                            #                     5,
                            #                     6,
                            #                     7,
                            #                     6,
                            #                     5,
                            #                     8,
                            #                     10,
                            #                     6,
                            #                     10
                            #                 ]
                            #                 },
                                            "energy_consumption": device.energy_consumption_data(start_date, end_date, frequency),
                                            "billing": device.get_billing_data(start_date, end_date),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "daily_kwh": permitted_view.branch.get_periodic_device_usage_daily(start_date, end_date, "daily"),
                                "usage_hours": permitted_view.branch.get_hours_of_use(start_date, end_date),
                                "time_of_use_table": permitted_view.branch.get_time_of_use(start_date, end_date),
                                "cost_tracker_qty_of_diesel": {
                                    "dates": [
                                        "2021-01-03T10:41:11.452244",
                                        "2021-01-03T10:53:11.452244",
                                        "2021-01-03T11:06:11.452244",
                                        "2021-01-03T11:20:11.452244",
                                        "2021-01-03T11:35:11.452244",
                                        "2021-01-03T11:49:11.452244",
                                        "2021-01-03T12:01:11.452244",
                                        "2021-01-03T12:15:11.452244",
                                        "2021-01-03T12:28:11.452244",
                                        "2021-01-03T12:40:11.452244",
                                        "2021-01-03T12:52:11.452244",
                                        "2021-01-03T13:06:11.452244",
                                        "2021-01-03T13:21:11.452244",
                                        "2021-01-03T13:36:11.452244",
                                        "2021-01-03T13:51:11.452244",
                                        "2021-01-03T14:06:11.452244",
                                        "2021-01-03T14:21:11.452244",
                                        "2021-01-03T14:36:11.452244",
                                        "2021-01-03T14:51:11.452244",
                                        "2021-01-03T15:06:11.452244"
                                    ],
                                    "values": [
                                        311,
                                        323,
                                        353,
                                        376,
                                        350,
                                        310,
                                        364,
                                        302,
                                        375,
                                        330,
                                        359,
                                        358,
                                        325,
                                        318,
                                        335,
                                        315,
                                        327,
                                        309,
                                        322,
                                        301
                                    ],
                                    "units": "litres"
                                },
                                "cost_tracker_monthly_cost": {
                                    "dates": ["Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug"],
                                    "values": [
                                        216542,
                                        120255,
                                        451496,
                                        172617,
                                        493132,
                                        230306,
                                        120608,
                                        445166
                                    ],
                                    "units": "Naira"
                                },
                                "cost_tracker_monthly_diesel_purchase": {
                                    "dates": ["Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug"],
                                    "values": [
                                        200,
                                        400,
                                        231,
                                        341,
                                        120,
                                        300,
                                        223,
                                        543
                                    ],
                                    "units": "Litres"
                                },
                                "cost_tracker_monthly_diesel_estimate": {
                                    "dates": ["Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug"],
                                    "values": [
                                        230,
                                        50,
                                        230,
                                        542,
                                        300,
                                        520,
                                        111,
                                        300
                                    ],
                                    "units": "Litres"
                                },
                                "cost_tracker_consumption_breakdown": {
                                    "dates": [
                                        "2021-01-03T10:41:11.452244",
                                        "2021-01-03T10:53:11.452244",
                                        "2021-01-03T11:06:11.452244",
                                        "2021-01-03T11:20:11.452244",
                                        "2021-01-03T11:35:11.452244",
                                        "2021-01-03T11:49:11.452244",
                                        "2021-01-03T12:01:11.452244",
                                        "2021-01-03T12:15:11.452244",
                                        "2021-01-03T12:28:11.452244",
                                        "2021-01-03T12:40:11.452244",
                                        "2021-01-03T12:52:11.452244",
                                        "2021-01-03T13:06:11.452244",
                                        "2021-01-03T13:21:11.452244",
                                        "2021-01-03T13:36:11.452244",
                                        "2021-01-03T13:51:11.452244",
                                        "2021-01-03T14:06:11.452244",
                                        "2021-01-03T14:21:11.452244",
                                        "2021-01-03T14:36:11.452244",
                                        "2021-01-03T14:51:11.452244",
                                        "2021-01-03T15:06:11.452244"
                                    ],
                                    "gen1": [
                                        136178,
                                        184450,
                                        274607,
                                        464500,
                                        319799,
                                        407092,
                                        256008,
                                        301218,
                                        334815,
                                        386142,
                                        291507,
                                        487042,
                                        267900,
                                        107536,
                                        138745,
                                        325639,
                                        202577,
                                        492487,
                                        225815,
                                        186782
                                    ],
                                    "gen2": [
                                        445916,
                                        187186,
                                        315449,
                                        408653,
                                        403977,
                                        363605,
                                        369188,
                                        357796,
                                        417820,
                                        161999,
                                        183815,
                                        420018,
                                        146487,
                                        152836,
                                        495514,
                                        315529,
                                        312372,
                                        267142,
                                        328836,
                                        490496
                                    ],
                                    "units": "litres"
                                },

                                "billing_totals": {
                                    "previous_total": {
                                        "usage_kwh": sum([device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["usage_kwh"] for device in permitted_view.branch.device_set.all()]),
                                        "value_naira": sum([device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["value_naira"] for device in permitted_view.branch.device_set.all()]),
                                    },
                                    "present_total": {
                                        "usage_kwh": sum([device.get_billing_data(start_date, end_date)["totals"]["present_total"]["usage_kwh"] for device in permitted_view.branch.device_set.all()]),
                                        "value_naira": sum([device.get_billing_data(start_date, end_date)["totals"]["present_total"]["value_naira"] for device in permitted_view.branch.device_set.all()]),
                                    },
                                    "metrics": permitted_view.branch.get_cost_of_per_kwh(start_date, end_date),
                                    "usage": {
                                        "previous_kwh": 202311,
                                        "present_kwh": 20311,
                                        "total_usage_kwh": 182000
                                    }
                                },
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

########################## DASHBOARD ENDPOINTS START #################################

def get_branch_data(permitted_view, start_date, end_date, frequency):
    usage_hours = permitted_view.branch.get_hours_of_use(start_date, end_date)

    devices = []
    branch_billing_totals = {
        "previous_total": {"usage_kwh": 0, "value_naira": 0},
        "present_total": {"usage_kwh": 0, "value_naira": 0},
    }

    for device_index, device in enumerate(permitted_view.branch.device_set.all()):
        device_billing_data = device.get_billing_data(start_date, end_date)
        device_data = {
            'name': device.name,
            'device_id': device.id,
            'is_load': device.is_load,
            'is_source': device.is_source,
            'device_type': device.type.choice_name,
            'source_tag': device.source_tag,
            'energy_consumption': device.energy_consumption_data(start_date, end_date, frequency),
            'id': device_index + 1,
            'usage_hours': next((hours for dev, hours in zip(usage_hours['devices'], usage_hours['hours']) if dev == device.name), 0),
            'billing_totals': {
                "previous_total": device_billing_data["totals"]["previous_total"],
                "present_total": device_billing_data["totals"]["present_total"],
            }
        }
        devices.append(device_data)

        # Accumulate branch totals
        for total_type in ["previous_total", "present_total"]:
            for metric in ["usage_kwh", "value_naira"]:
                branch_billing_totals[total_type][metric] += device_billing_data["totals"][total_type][metric]

    # Update devices with special configuration
    # updated_devices = dashboard_helpers.get_device_usage_config(devices)

    branch_data = {
        "name": permitted_view.branch.name,
        "branch_id": permitted_view.branch.id,
        "devices": devices,
        "usage_hours": {
            "period_total_hours": usage_hours['period_total_hours'],
            "black_out": usage_hours['black_out']
        },
        "billing_totals": {
            **branch_billing_totals,
            "metrics": permitted_view.branch.get_cost_of_per_kwh(start_date, end_date),
            "usage": {
                "previous_kwh": 202311,
                "present_kwh": 20311,
                "total_usage_kwh": 182000
            }
        }
    }

    return branch_data

def get_daily_kwh_data(permitted_view, start_date, end_date):
        branch = permitted_view.branch
        daily_kwh_data = branch.get_periodic_device_usage_daily(start_date, end_date, "daily")

        devices = []
        for device_index, device in enumerate(branch.device_set.all()):
            device_data = {
                'name': device.name,
                'device_id': device.id,
                'is_load': device.is_load,
                'is_source': device.is_source,
                'device_type': device.type.choice_name,
                'id': device_index + 1,
                'daily_kwh': daily_kwh_data.get(device.name, [])
            }
            devices.append(device_data)

        return {
            "name": branch.name,
            "branch_id": branch.id,
            "devices": devices,
            "dates": daily_kwh_data["dates"],
            "id": permitted_view.id
        }

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
# @permission_classes([IsAuthenticated])
def dashboard_total_energy_endpoint(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("||||||||||||||||||| @ DASHBOARD SEPARATED DATA @ |||||||||||||||||||||||")
    print(f"START DATE: {start_date}, END DATE: {end_date}")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       'source_tag': device.source_tag,
                                       'dashboard': {
                                            "total_kwh": {
                                            "unit": "kWh",
                                            "value": round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
                                            },
                                            "dashboard_carbon_emissions": {
                                                "unit": "Tons",
                                                "value": round(device.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value"), 2)
                                            },
                                            "cost_of_energy": {
                                                "unit": "Naira/kWh",
                                                "value": device.get_cost_of_energy(start_date, end_date)
                                            },
                                            "today": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("today_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "yesterday": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("yesterday_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "solar_hours":{
                                                "value":device.get_solar_hours_consumption(start_date, end_date),
                                                "unit":"kWh"
                                            }
                                       },
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],

                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }
    # Update data with special configuration
    # updated_data = dashboard_helpers.get_total_energy_config(data)

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def dashboard_device_usage_endpoint(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)
    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    branches_data = [get_branch_data(view, start_date, end_date, frequency) for view in permitted_views]

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    'branches': branches_data
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
# @permission_classes([IsAuthenticated])
def dashboard_daily_consumption_endpoint(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)
    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    kwh_data = [get_daily_kwh_data(view, start_date, end_date) for view in permitted_views]

    data = {
        'status': True,
        'message': "Successful",
        'authenticatedData': {
            'id': user.client.id,
            "user_id": user.id,
            'name': user.client.name,
            'image': user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
            'branches': kwh_data
        }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['GET'])
def dashboard_load_overview_endpoint(request, user_id, start_date, end_date, frequency):
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)
    frequency = frequency.lower()

    try:
        user = User.objects.get(id=user_id)
        if not user.client:
            data = {
                'status': False,
                'message': "User has no associated client",
                'authenticatedData': None
            }
            response = Response(data, status=status.HTTP_400_BAD_REQUEST)
            response["Access-Control-Allow-Origin"] = "*"
            return response

    except User.DoesNotExist:
        data = {
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }
        response = Response(data, status=status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    # Default to empty response if not BESPOKE client
    data = {}

    if user.client and user.client.client_type == "BESPOKE":
        branches_data = []

        for branch_index, permitted_view in enumerate(permitted_views):
            try:
                # Get energy data safely with error handling
                energy_data = permitted_view.branch.branch_total_energy_generated_by_sources_and_consumed_by_loads(
                    start_date, end_date
                )
                if not energy_data:
                    energy_generated = 0
                    load_consumption = 0
                    percentage_load_consumption = 0
                else:
                    energy_generated, load_consumption, percentage_load_consumption = energy_data

                devices_data = []
                for device_index, device in enumerate(permitted_view.branch.device_set.all()):
                    try:
                        consumption = device.get_total_energy_at_end_of_period(start_date, end_date)['usage']
                        power_demand = device.get_power_demand(start_date, end_date, frequency)
                        device_runtime = device.get_hours_used(start_date, end_date)["hours"]
                    except Exception as e:
                        logger.error(f"Error processing device {device.id}: {str(e)}")
                        consumption = 0
                        power_demand = {}
                        device_runtime = 0

                    devices_data.append({
                        'name': device.name,
                        'device_id': device.id,
                        'is_load': device.is_load,
                        'is_source': device.is_source,
                        'device_type': device.type.choice_name,
                        'source_tag': device.source_tag,
                        'consumption': consumption,
                        'power_demand': power_demand,
                        'device_runtime': device_runtime,
                        'id': device_index + 1
                    })

                branches_data.append({
                    "name": permitted_view.branch.name,
                    "branch_id": permitted_view.branch.id,
                    "energy_generated": energy_generated,
                    "load_consumption": load_consumption,
                    "percentage_load_consumption": percentage_load_consumption,
                    "devices": devices_data,
                    "id": branch_index + 1,
                })

            except Exception as e:
                logger.error(f"Error processing branch {permitted_view.branch.id}: {str(e)}")
                continue

        data = {
            'status': True,
            'message': "Successful",
            'authenticatedData': {
                'id': user.client.id,
                "user_id": user.id,
                'name': user.client.name,
                'client_type': user.client.client_type,
                'image': user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                "score_cards_date": time_helpers.score_cards_date(end_date),
                'branches': branches_data
            }
        }

    response = Response(data, status=status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

####################### SIMPLIFIED DASHBOARD OVERVIEW ##############################
# Decorator to ensure the user exists, has a client, and is BESPOKE
# Returns empty JSON ({}) with 200 OK for non-BESPOKE clients
def require_bespoke_client(view_fn):
    @wraps(view_fn)
    def wrapper(request, user_id, *args, **kwargs):
        user = get_object_or_404(User, id=user_id)
        client = getattr(user, 'client', None)
        if not client or client.client_type != 'BESPOKE':
            # For non-BESPOKE or missing client, return empty JSON
            response = Response({}, status=status.HTTP_200_OK)
            response['Access-Control-Allow-Origin'] = '*'
            return response
        
        return view_fn(request, user_id, *args, **kwargs)
    return wrapper

# Helper to build per-branch overview data
def build_branch_overview(branch, start_date, end_date, frequency, index):
    try:
        energy_data = branch.branch_total_energy_generated_by_sources_and_consumed_by_loads(start_date, end_date) or (0, 0, 0)
    except Exception as e:
        logger.error(f"Error fetching energy data for branch {branch.id}: {e}")
        energy_data = (0, 0, 0)

    return {
        'id': index + 1,
        'branch_id': branch.id,
        'name': branch.name,
        'energy_generated': energy_data[0],
        'load_consumption': energy_data[1],
        'percentage_load_consumption': energy_data[2],
    }

# Endpoint: List branch overviews (totals only)
@cache_page(60 * 6)
@api_view(['GET'])
# @permission_classes([IsAuthenticated])
@require_bespoke_client
def dashboard_branches_overview(request, user_id, start_date, end_date, frequency):
    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    start = time_helpers.convert_date(start_date)
    end = time_helpers.convert_date(end_date)
    frequency = frequency.lower()

    permitted = user.view_permissions.all()
    branches_data = [
        build_branch_overview(pv.branch, start, end, frequency, idx)
        for idx, pv in enumerate(permitted)
    ]

    payload = {
        'status': True,
        'message': 'Branch overview fetched successfully',
        'authenticatedData': {
            'user_id': user.id,
            'client_id': user.client.id,
            'client_name': user.client.name,
            'client_type': user.client.client_type,
            'image': user.client.logo.url if user.client.logo else None,
            'score_cards_date': time_helpers.score_cards_date(end),
            'branches': branches_data,
        }
    }
    response = Response(payload, status=status.HTTP_200_OK)
    response['Access-Control-Allow-Origin'] = '*'
    return response


@cache_page(60 * 6)
@api_view(['GET', 'POST'])  # accepts both GET and POST
def dashboard_energy_consumption(request, user_id, start_date, end_date, frequency):
    # parse incoming dates and normalize frequency
    start_dt = time_helpers.convert_date(start_date)
    end_dt = time_helpers.convert_date(end_date)
    freq = frequency.lower()

    # fetch the user or return a 404 error
    user = get_object_or_404(User, id=user_id)

    branches_payload = []
    # iterate each branch the user has permission to view
    for branch_index, pv in enumerate(user.view_permissions.all(), start=1):
        branch = pv.branch
        devices_payload = []

        # iterate each device in the branch
        for device_index, dev in enumerate(branch.device_set.all(), start=1):
            # a) compute aggregated metrics: previous, current, usage
            agg = dev.get_agg_kwh_for_period(start_dt, end_dt)
            prev = round(agg.get('min', 0) / KW_TO_KVA_MULTIPLIER, 2)
            curr = round(agg.get('max', 0) / KW_TO_KVA_MULTIPLIER, 2)
            usage = round(agg.get('avg', 0) / KW_TO_KVA_MULTIPLIER, 2)

            # b) fetch full time-series dict (this may include dates, values, etc.)
            ts = dev.energy_consumption_data(start_dt, end_dt, freq)

            # c) build the device entry, merging in full ts data
            device_entry = {
                'id': device_index,
                'name': dev.name,
                'device_id': dev.id,
                'is_load': dev.is_load,
                'is_source': dev.is_source,
                'device_type': dev.type.choice_name,
                'energy_consumption': {
                    'previous': prev,
                    'current': curr,
                    'usage': usage,
                    **ts  # include all keys from energy_consumption_data
                }
            }
            devices_payload.append(device_entry)

        # build the branch entry with devices list
        branch_entry = {
            'id': branch_index,
            'name': branch.name,
            'branch_id': branch.id,
            'devices': devices_payload
        }
        branches_payload.append(branch_entry)

    # wrap the full response
    response_payload = {
        'status': True,
        'message': 'Successful',
        'authenticatedData': {
            'score_cards_date': time_helpers.score_cards_date(end_dt),
            'branches': branches_payload
        }
    }
    # return JSON response
    return Response(response_payload, status=status.HTTP_200_OK)


@cache_page(60 * 6)
@api_view(['GET'])
def dashboard_last_reading(request, user_id, start_date):
    """
    Returns per-branch, per-source-device last-reading blocks,
    with standardized response format.
    """

    # 1) Parse date
    _ = time_helpers.convert_date(start_date)

    # 2) Load and authorize user
    user = get_object_or_404(User, id=user_id)
    permitted = user.view_permissions.all()

    branches = []
    for pv in permitted:
        branch = pv.branch
        sources = branch.device_set.filter(is_source=True)

        dev_list = []
        for idx, dev in enumerate(sources, start=1):
            last = dev.get_last_readings() or {}
            last_date = last.get("date")
            last_data = last.get("data", {})

            dev_list.append({
                "name": dev.name,
                "device_id": dev.id,
                "id": idx,
                "is_load": dev.is_load,
                "is_source": dev.is_source,
                "device_type": dev.type.choice_name,
                "last_reading": {
                    "date": last_date,
                    "data": last_data
                }
            })

        branches.append({
            "name": branch.name,
            "branch_id": branch.id,
            "devices": dev_list
        })

    response_data = {
        "status": True,
        "message": "Successfully",
        "data": {
            "branches": branches
        }
    }

    response = Response(response_data, status=status.HTTP_200_OK)
    return response



@cache_page(60 * 6)
@api_view(['GET'])
def dashboard_power_demand(request, user_id, start_date, end_date, frequency):
    """
    Returns, for each branch and each device, its power-demand series.
    """
    # 1) Parse & normalize inputs
    start = time_helpers.convert_date(start_date)
    end   = time_helpers.convert_date(end_date)
    freq  = frequency.lower()

    # 2) Load user (and bail out if not found)
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response(
            {"status": False, "message": "User not found"},
            status=status.HTTP_404_NOT_FOUND
        )

    branches_payload = []
    # 3) For each branch the user has access to...
    for pv in user.view_permissions.all():
        branch = pv.branch

        devices_payload = []
        # 4) Iterate **all** devices on this branch
        for idx, device in enumerate(branch.device_set.all(), start=1):
            pd = device.get_power_demand(start, end, freq)

            devices_payload.append({
                "name":         device.name,
                "device_id":    device.id,
                "id":           idx,
                "is_load":      device.is_load,
                "is_source":    device.is_source,
                "device_type":  device.type.choice_name,
                "power_demand": pd
            })

        branches_payload.append({
            "name":      branch.name,
            "branch_id": branch.id,
            "devices":   devices_payload
        })

    # 5) Return the assembled payload
    return Response({
        "status":  True,
        "message": "Successful",
        "branches": branches_payload
    }, status=status.HTTP_200_OK)


@cache_page(60 * 6)
@api_view(['GET'])
def dashboard_power_quality(request, user_id, start_date, end_date, frequency):
    """
    Per‑branch, per‑device power_quality time series.
    """
    start_dt = time_helpers.convert_date(start_date)
    end_dt   = time_helpers.convert_date(end_date)
    freq     = frequency.lower()

    user = get_object_or_404(User, id=user_id)
    branches = []
    for pv in user.view_permissions.all():
        b = pv.branch
        devs = []
        for idx, dev in enumerate(b.device_set.all(), start=1):
            pq = dev.get_power_quality(start_dt, end_dt, freq)
            devs.append({
                "name": dev.name,
                "device_id": dev.id,
                "id": idx,
                "is_load": dev.is_load,
                "is_source": dev.is_source,
                "device_type": dev.type.choice_name,
                "power_quality": pq
            })
        branches.append({
            "name": b.name,
            "branch_id": b.id,
            "devices": devs
        })

    return Response({
        "status":  True,
        "message": "Successful",
        "branches": branches
    }, status=status.HTTP_200_OK)

########################## DASHBOARD ENDPOINTS END #################################

########################## SCORECARD SECTION START #################################

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "score_card": {
                                            "is_generator": device.is_gen,
                                            "baseline_energy": device.get_base_line(end_date).get("baseline_energy"),
                                            "peak_to_avg_power_ratio": device.get_papr(start_date, end_date, report=False),
                                            "score_card_carbon_emissions": device.get_carbon_emmisions_score_cards(end_date),
                                            "generator_size_efficiency":device.get_gen_efficiency(end_date),
                                            "operating_time": device.get_operating_time(start_date, end_date),
                                            "fuel_consumption": device.get_fuel_consumption_score_cards(start_date, end_date)
                                            },
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard_baseline_energy(request, user_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    try:
        user = User.objects.prefetch_related(
            'view_permissions__branch__device_set'
        ).get(id=user_id)

    except User.DoesNotExist:
        data = {
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }
        return Response(data, status=status.HTTP_401_UNAUTHORIZED)

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "score_card": {
                                            "is_generator": device.is_gen,
                                            "baseline_energy": device.get_base_line(end_date).get("baseline_energy"),
                                            },
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response



# @cache_page(60 * 6)
# @api_view(['POST', 'GET'])
# def scorecard_peak_to_avg_power_ratio(request, user_id, start_date, end_date):

#     start_date = time_helpers.convert_date(start_date)
#     end_date = time_helpers.convert_date(end_date)

#     try:
#         user = User.objects.prefetch_related(
#             'view_permissions__branch__device_set'
#         ).get(id=user_id)

#     except User.DoesNotExist:
#         data = {
#             'status': False,
#             'message': "UserId might not exist",
#             'authenticatedData': None
#         }
#         return Response(data, status=status.HTTP_401_UNAUTHORIZED)

#     permitted_views = user.view_permissions.all()

#     data = {
#                 'status'  : True,
#                 'message' : "Successful",
#                 'authenticatedData' : {
#                     'id' : user.client.id,
#                     "user_id":user.id,
#                     'name'  :  user.client.name,
#                     'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
#                     "score_cards_date": time_helpers.score_cards_date(end_date),
#                     'branches': [
#                         {
#                             "name": permitted_view.branch.name,
#                             "branch_id": permitted_view.branch.id,
#                             "devices": [
#                                     {
#                                        'name': device.name,
#                                        'device_id': device.id,
#                                        'is_load'  : device.is_load,
#                                        'is_source': device.is_source,
#                                        'device_type': device.type.choice_name,
#                                        "score_card": {
#                                             "is_generator": device.is_gen,
#                                             "peak_to_avg_power_ratio": device.get_papr(start_date, end_date, report=False),
#                                             },
#                                         'id': device_index+1
#                                     }
#                                     for device_index, device in enumerate(permitted_view.branch.device_set.all())
#                                 ],
#                                 "id": branch_index+1,
#                         }
#                         for branch_index, permitted_view in enumerate(permitted_views)
#                     ]
#                 }
#     }

#     response = Response(data, status = status.HTTP_200_OK)
#     response["Access-Control-Allow-Origin"] = "*"
#     return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard_peak_to_avg_power_ratio(request, user_id, start_date, end_date):
    """
    - Loads User → (ViewPermission → Branch → Device → Reading) in one shot.
    - Calls device.get_papr(start_date, end_date, report=False) per device.
    - Returns the same nested JSON as before.
    """

    # 1) Parse dates
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    # 2) Fetch User + related Branch→Device→Reading
    try:
        user = User.objects.select_related('client').prefetch_related(
            Prefetch(
                'view_permissions__branch__device_set',
                queryset=Device.objects
                    .select_related('type', 'branch')
                    .prefetch_related('reading_set')
                    .only(
                        'id', 'name', 'is_load', 'is_source', 'gen_size',
                        'type_id', 'branch_id'
                    )
            )
        ).get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }, status=status.HTTP_401_UNAUTHORIZED)

    permitted_views = user.view_permissions.all()
    client = user.client

    # 3) Build JSON
    branches_list = []
    for branch_index, permitted_view in enumerate(permitted_views):
        branch = permitted_view.branch

        devices_list = []
        for device_index, device in enumerate(branch.device_set.all()):
            score_card = {
                'is_generator': device.is_gen,
                'peak_to_avg_power_ratio': device.get_papr(start_date, end_date, report=False)
            }

            devices_list.append({
                'name': device.name,
                'device_id': device.id,
                'is_load': device.is_load,
                'is_source': device.is_source,
                'device_type': device.type.choice_name,
                'score_card': score_card,
                'id': device_index + 1
            })

        branches_list.append({
            'name': branch.name,
            'branch_id': branch.id,
            'devices': devices_list,
            'id': branch_index + 1
        })

    data = {
        'status': True,
        'message': "Successful",
        'authenticatedData': {
            'id': client.id,
            'user_id': user.id,
            'name': client.name,
            'image': client.logo.url if client.logo else settings.DEFAULT_IMAGE,
            'score_cards_date': time_helpers.score_cards_date(end_date),
            'branches': branches_list
        }
    }

    response = Response(data, status=status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response



@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard_carbon_emissions(request, user_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    try:
        user = User.objects.prefetch_related(
            'view_permissions__branch__device_set'
        ).get(id=user_id)

    except User.DoesNotExist:
        data = {
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }
        return Response(data, status=status.HTTP_401_UNAUTHORIZED)

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "score_card": {
                                            "is_generator": device.is_gen,
                                            "score_card_carbon_emissions": device.get_carbon_emmisions_score_cards(end_date),
                                            },
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response


# @cache_page(60 * 6)
# @api_view(['POST', 'GET'])
# def scorecard_generator_size_efficiency(request, user_id, start_date, end_date):

#     start_date = time_helpers.convert_date(start_date)
#     end_date = time_helpers.convert_date(end_date)

#     try:
#         user = User.objects.prefetch_related(
#             'view_permissions__branch__device_set'
#         ).get(id=user_id)

#     except User.DoesNotExist:
#         data = {
#             'status': False,
#             'message': "UserId might not exist",
#             'authenticatedData': None
#         }
#         return Response(data, status=status.HTTP_401_UNAUTHORIZED)

#     permitted_views = user.view_permissions.all()

#     data = {
#                 'status'  : True,
#                 'message' : "Successful",
#                 'authenticatedData' : {
#                     'id' : user.client.id,
#                     "user_id":user.id,
#                     'name'  :  user.client.name,
#                     'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
#                     "score_cards_date": time_helpers.score_cards_date(end_date),
#                     'branches': [
#                         {
#                             "name": permitted_view.branch.name,
#                             "branch_id": permitted_view.branch.id,
#                             "devices": [
#                                     {
#                                        'name': device.name,
#                                        'device_id': device.id,
#                                        'is_load'  : device.is_load,
#                                        'is_source': device.is_source,
#                                        'device_type': device.type.choice_name,
#                                        "score_card": {
#                                             "is_generator": device.is_gen,
#                                             "generator_size_efficiency":device.get_gen_efficiency(end_date),
#                                             },
#                                         'id': device_index+1
#                                     }
#                                     for device_index, device in enumerate(permitted_view.branch.device_set.all())
#                                 ],
#                                 "id": branch_index+1,
#                         }
#                         for branch_index, permitted_view in enumerate(permitted_views)
#                     ]
#                 }
#     }

#     response = Response(data, status = status.HTTP_200_OK)
#     response["Access-Control-Allow-Origin"] = "*"
#     return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard_generator_size_efficiency(request, user_id, start_date, end_date):
    """
    Returns exactly the same JSON structure as before, but under the hood:
    - We load User → (ViewPermission → Branch → Device → Reading) in one shot.
    - Each Device’s get_gen_efficiency is cached (shared Redis/Memcached).
    """

    # 1) Parse dates only once
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    # 2) Fetch User + all related Branch→Device→Reading in a single trip to the DB
    try:
        user = User.objects.select_related('client').prefetch_related(
            Prefetch(
                'view_permissions__branch__device_set',      # correct relation path
                queryset=Device.objects
                    .select_related('type', 'branch')       # no 'client' here
                    .prefetch_related('reading_set')        # simply prefetch all readings
                    .only(
                        'id', 'name', 'is_load', 'is_source', 'gen_size',
                        'type_id', 'branch_id'
                    )
            )
        ).get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }, status=status.HTTP_401_UNAUTHORIZED)

    permitted_views = user.view_permissions.all()
    client = user.client

    # 3) Build the exact same nested JSON structure
    branches_list = []
    for branch_index, permitted_view in enumerate(permitted_views):
        branch = permitted_view.branch

        # For each Branch, its Device queryset was already prefetched above.
        devices_list = []
        for device_index, device in enumerate(branch.device_set.all()):
            # Compute score_card exactly as before via device.get_gen_efficiency(end_date)
            score_card = {
                'is_generator': device.is_gen,
                'generator_size_efficiency': device.get_gen_efficiency(end_date)
            }

            devices_list.append({
                'name': device.name,
                'device_id': device.id,
                'is_load': device.is_load,
                'is_source': device.is_source,
                'device_type': device.type.choice_name,
                'score_card': score_card,
                'id': device_index + 1
            })

        branches_list.append({
            'name': branch.name,
            'branch_id': branch.id,
            'devices': devices_list,
            'id': branch_index + 1
        })

    data = {
        'status': True,
        'message': "Successful",
        'authenticatedData': {
            'id': client.id,
            'user_id': user.id,
            'name': client.name,
            'image': client.logo.url if client.logo else settings.DEFAULT_IMAGE,
            'score_cards_date': time_helpers.score_cards_date(end_date),
            'branches': branches_list
        }
    }

    response = Response(data, status=status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

# @cache_page(60 * 6)
# @api_view(['POST', 'GET'])
# def scorecard_operating_time(request, user_id, start_date, end_date):

#     start_date = time_helpers.convert_date(start_date)
#     end_date = time_helpers.convert_date(end_date)

#     try:
#         user = User.objects.prefetch_related(
#             'view_permissions__branch__device_set'
#         ).get(id=user_id)

#     except User.DoesNotExist:
#         data = {
#             'status': False,
#             'message': "UserId might not exist",
#             'authenticatedData': None
#         }
#         return Response(data, status=status.HTTP_401_UNAUTHORIZED)

#     permitted_views = user.view_permissions.all()

#     data = {
#                 'status'  : True,
#                 'message' : "Successful",
#                 'authenticatedData' : {
#                     'id' : user.client.id,
#                     'user_id' : user.id,
#                     'name'  :  user.client.name,
#                     'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
#                     'score_cards_date' : time_helpers.score_cards_date(end_date),
#                     'branches': [
#                         {
#                             "name": permitted_view.branch.name,
#                             "branch_id": permitted_view.branch.id,
#                             "devices": [
#                                     {
#                                        'name': device.name,
#                                        'device_id': device.id,
#                                        'is_load'  : device.is_load,
#                                        'is_source': device.is_source,
#                                        'device_type': device.type.choice_name,
#                                        "score_card": {
#                                             "is_generator": device.is_gen,
#                                             "operating_time": device.get_operating_time(start_date, end_date),
#                                             },
#                                         'id': device_index+1
#                                     }
#                                     for device_index, device in enumerate(permitted_view.branch.device_set.all())
#                                 ],
#                                 "id": branch_index+1,
#                         }
#                         for branch_index, permitted_view in enumerate(permitted_views)
#                     ]
#                 }
#     }

#     response = Response(data, status = status.HTTP_200_OK)
#     response["Access-Control-Allow-Origin"] = "*"
#     return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard_operating_time(request, user_id, start_date, end_date):
    """
    - Loads User → (ViewPermission → Branch → Device → Reading) in one shot.
    - Calls each device.get_operating_time(start_date, end_date), which is cache-backed.
    - Returns the same nested JSON as before, but with "operating_time".
    """

    # 1) Parse incoming date strings once
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    # 2) Fetch User + related Branch→Device→Reading
    try:
        user = User.objects.select_related('client').prefetch_related(
            Prefetch(
                'view_permissions__branch__device_set',
                queryset=Device.objects
                    .select_related('type', 'branch')
                    .prefetch_related('reading_set')
                    .only(
                        'id', 'name', 'is_load', 'is_source', 'gen_size',
                        'type_id', 'branch_id'
                    )
            )
        ).get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }, status=status.HTTP_401_UNAUTHORIZED)

    permitted_views = user.view_permissions.all()
    client = user.client

    # 3) Build response JSON with operating_time per device
    branches_list = []
    for branch_index, permitted_view in enumerate(permitted_views):
        branch = permitted_view.branch

        devices_list = []
        for device_index, device in enumerate(branch.device_set.all()):
            score_card = {
                'is_generator': device.is_gen,
                'operating_time': device.get_operating_time(start_date, end_date)
            }

            devices_list.append({
                'name': device.name,
                'device_id': device.id,
                'is_load': device.is_load,
                'is_source': device.is_source,
                'device_type': device.type.choice_name,
                'score_card': score_card,
                'id': device_index + 1
            })

        branches_list.append({
            'name': branch.name,
            'branch_id': branch.id,
            'devices': devices_list,
            'id': branch_index + 1
        })

    data = {
        'status': True,
        'message': "Successful",
        'authenticatedData': {
            'id': client.id,
            'user_id': user.id,
            'name': client.name,
            'image': client.logo.url if client.logo else settings.DEFAULT_IMAGE,
            'score_cards_date': time_helpers.score_cards_date(end_date),
            'branches': branches_list
        }
    }

    response = Response(data, status=status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response


# @cache_page(60 * 6)
# @api_view(['POST', 'GET'])
# def scorecard_fuel_consumption(request, user_id, start_date, end_date):

#     start_date = time_helpers.convert_date(start_date)
#     end_date = time_helpers.convert_date(end_date)

#     try:
#         user = User.objects.prefetch_related(
#             'view_permissions__branch__device_set'
#         ).get(id=user_id)

#     except User.DoesNotExist:
#         data = {
#             'status': False,
#             'message': "UserId might not exist",
#             'authenticatedData': None
#         }
#         return Response(data, status=status.HTTP_401_UNAUTHORIZED)

#     permitted_views = user.view_permissions.all()

#     data = {
#                 'status'  : True,
#                 'message' : "Successful",
#                 'authenticatedData' : {
#                     'id' : user.client.id,
#                     "user_id":user.id,
#                     'name'  :  user.client.name,
#                     'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
#                     "score_cards_date": time_helpers.score_cards_date(end_date),
#                     'branches': [
#                         {
#                             "name": permitted_view.branch.name,
#                             "branch_id": permitted_view.branch.id,
#                             "devices": [
#                                     {
#                                        'name': device.name,
#                                        'device_id': device.id,
#                                        'is_load'  : device.is_load,
#                                        'is_source': device.is_source,
#                                        'device_type': device.type.choice_name,
#                                        "score_card": {
#                                             "is_generator": device.is_gen,
#                                             "fuel_consumption": device.get_fuel_consumption_score_cards(start_date, end_date)
#                                             },
#                                         'id': device_index+1
#                                     }
#                                     for device_index, device in enumerate(permitted_view.branch.device_set.all())
#                                 ],
#                                 "id": branch_index+1,
#                         }
#                         for branch_index, permitted_view in enumerate(permitted_views)
#                     ]
#                 }
#     }

#     response = Response(data, status = status.HTTP_200_OK)
#     response["Access-Control-Allow-Origin"] = "*"
#     return response


@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def scorecard_fuel_consumption(request, user_id, start_date, end_date):
    """
    - Loads User → (ViewPermission → Branch → Device → Reading) in one shot.
    - Calls each device.get_fuel_consumption_score_cards(start_date, end_date), which is cache-backed.
    - Returns the same nested JSON as before, but with "fuel_consumption".
    """

    # 1) Parse incoming date strings once
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    # 2) Fetch User + related Branch→Device→Reading
    try:
        user = User.objects.select_related('client').prefetch_related(
            Prefetch(
                'view_permissions__branch__device_set',
                queryset=Device.objects
                    .select_related('type', 'branch')
                    .prefetch_related('reading_set')
                    .only(
                        'id', 'name', 'is_load', 'is_source', 'gen_size',
                        'type_id', 'branch_id'
                    )
            )
        ).get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'status': False,
            'message': "UserId might not exist",
            'authenticatedData': None
        }, status=status.HTTP_401_UNAUTHORIZED)

    permitted_views = user.view_permissions.all()
    client = user.client

    # 3) Build response JSON with fuel_consumption per device
    branches_list = []
    for branch_index, permitted_view in enumerate(permitted_views):
        branch = permitted_view.branch

        devices_list = []
        for device_index, device in enumerate(branch.device_set.all()):
            score_card = {
                'is_generator': device.is_gen,
                'fuel_consumption': device.get_fuel_consumption_score_cards_optimized(start_date, end_date)
            }

            devices_list.append({
                'name': device.name,
                'device_id': device.id,
                'is_load': device.is_load,
                'is_source': device.is_source,
                'device_type': device.type.choice_name,
                'score_card': score_card,
                'id': device_index + 1
            })

        branches_list.append({
            'name': branch.name,
            'branch_id': branch.id,
            'devices': devices_list,
            'id': branch_index + 1
        })

    data = {
        'status': True,
        'message': "Successful",
        'authenticatedData': {
            'id': client.id,
            'user_id': user.id,
            'name': client.name,
            'image': client.logo.url if client.logo else settings.DEFAULT_IMAGE,
            'score_cards_date': time_helpers.score_cards_date(end_date),
            'branches': branches_list
        }
    }

    response = Response(data, status=status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

########################## SCORECARD SECTION END #################################

########################## PARAMETERS SECTION START #################################

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def energy_consumption(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "energy_consumption": device.energy_consumption_data(start_date, end_date, frequency),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def power_quality(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "power_quality": device.get_power_quality(start_date, end_date, frequency),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def power_demand(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "power_demand": device.get_power_demand(start_date, end_date, frequency),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def last_reading(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "last_reading": device.get_last_readings(),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
def billing(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    frequency = frequency.lower()

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    "score_cards_date": time_helpers.score_cards_date(end_date),
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       "billing": device.get_billing_data(start_date, end_date),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

########################## PARAMETERS SECTION END #################################

# @cache_page(60 * 6)
@api_view(['POST', 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
# @permission_classes([IsAuthenticated])
def billing_data(request, user_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                        'name': device.name,
                                        'device_id': device.id,
                                        'is_load'  : device.is_load,
                                        'is_source': device.is_source,
                                        'device_type': device.type.choice_name,
                                        "billing": device.get_billing_data(start_date, end_date),
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                               "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@cache_page(60 * 6)
@api_view(['POST', 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
# @permission_classes([IsAuthenticated])
def dashboard_data(request, user_id, start_date, end_date, frequency):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)
    frequency = frequency.lower()

    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("||||||||||||||||||| @ DASHBOARD DATA @ |||||||||||||||||||||||")
    print(f"START DATE: {start_date}, END DATE: {end_date}")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")
    print("|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||")

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    start_of_month = time_helpers.get_start_and_end_of_month_from_date(end_date)[0]
    current_date = timezone.now()
    start_date_time_diff = abs((start_of_month - start_date).total_seconds()/(60*120))
    end_date_time_diff = abs((end_date - current_date).total_seconds()/(60*120))

    use_cache = start_date_time_diff < 1 and \
                end_date_time_diff < 1 and \
                bool(user.cache_data)


    if True:
        # print(user.cache_data)

        data = {
                    'status'  : True,
                    'message' : "Successful",
                    'authenticatedData' : {
                        'id' : user.client.id,
                        "user_id":user.id,
                        'name'  :  user.client.name,
                        'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                        "score_cards_date": time_helpers.score_cards_date(end_date),
                        'branches': [
                            {
                                "name": permitted_view.branch.name,
                                "branch_id": permitted_view.branch.id,
                                "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                       'dashboard': {
                                            "total_kwh": {
                                            "unit": "kWh",
                                            "value": round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
                                            },
                                            "min_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("min"), settings.DECIMAL_PLACES)
                                            },
                                            "max_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("max"), settings.DECIMAL_PLACES)
                                            },
                                            "avg_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("avg"), settings.DECIMAL_PLACES)
                                            },
                                            "dashboard_carbon_emissions": {
                                                "unit": "Tons",
                                                "value": round(device.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value"), 2)
                                            },
                                            "cost_of_energy": {
                                                "unit": "Naira/KWh",
                                                "value": device.get_cost_of_energy(start_date, end_date)
                                            },
                                            "today": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("today_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "yesterday": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("yesterday_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "solar_hours":{
                                                "value":device.get_solar_hours_consumption(start_date, end_date),
                                                "unit":"kwh"
                                            }
                                       },
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                    "daily_kwh": permitted_view.branch.get_periodic_device_usage_daily(start_date, end_date, "daily"),
                                    "usage_hours": permitted_view.branch.get_hours_of_use(start_date, end_date),
                                    "time_of_use_table": permitted_view.branch.get_time_of_use(start_date, end_date),
                                    "billing_totals": {
                                        "previous_total": {
                                            "usage_kwh": sum([device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["usage_kwh"] for device in permitted_view.branch.device_set.all()]),
                                            "value_naira": sum([device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["value_naira"] for device in permitted_view.branch.device_set.all()]),
                                        },
                                        "present_total": {
                                            "usage_kwh": sum([device.get_billing_data(start_date, end_date)["totals"]["present_total"]["usage_kwh"] for device in permitted_view.branch.device_set.all()]),
                                            "value_naira": sum([device.get_billing_data(start_date, end_date)["totals"]["present_total"]["value_naira"] for device in permitted_view.branch.device_set.all()]),
                                        },
                                        "metrics": permitted_view.branch.get_cost_of_per_kwh(start_date, end_date),
                                        "usage": {
                                            "previous_kwh": 0,
                                            "present_kwh": 0,
                                            "total_usage_kwh": 0
                                        }
                                    },
                                    "id": branch_index+1,
                            }
                            for branch_index, permitted_view in enumerate(permitted_views)
                        ]
                    }
                }

    else:

        # user.cache_data = {}
        # user.save()
        data = json.loads(user.cache_data)

    response = Response(data, status = status.HTTP_200_OK)

    response["Access-Control-Allow-Origin"] = "*"
    return response


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def average_power_factor(request):

    serializer = serializers.AveragePowerFactorSerializer(data = request.data)

    if serializer.is_valid():

        device = Device.objects.filter(id = serializer.validated_data.get("device_id"))

        if device.exists():

            readings = device.first().reading_set.filter(post_date__range=[
                                                    serializer.validated_data.get("start_date"),
                                                    serializer.validated_data.get("end_date")
                                                    ]
                                        ).exclude(total_pf=0)

            if readings.exists():
                aggregate = readings.aggregate(Avg('total_pf'))

                data = {
                        'status'  : True,
                        'message' : "okay",
                        'data' : {
                            'avg_pf':round(abs(aggregate.get("total_pf__avg", 0)), 2)
                        },
                        'user' : request.user.username,
                        'device' : device.first().name,
                        'device_id' : device.first().id
                    }

                response = Response(data, status = status.HTTP_200_OK)

            else:
                data = {
                            'status'  : True,
                            'message' : "no data",
                            'data' : {
                                'avg_pf':0
                            },
                            'user' : request.user.username,
                            'device' : device.first().name,
                            'device_id' : device.first().id
                        }
                response = Response(data, status = status.HTTP_404_NOT_FOUND)


        else:

            data = {
                        'status'  : False,
                        'message' : "not found",
                        'data' : None,
                        'error': "no such device id"
                    }

            response = Response(data, status = status.HTTP_400_BAD_REQUEST)


    else:
        user = request.user
        data = {
            'status'  : False,
            'message' : "Field Error",
            'data' : None,
            'error': serializer.errors
        }

        response = Response(data, status = status.HTTP_400_BAD_REQUEST)

    response["Access-Control-Allow-Origin"] = "*"
    return response



@cache_page(60 * 6)
@api_view(['POST', 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
# @permission_classes([IsAuthenticated])
def side_bar_data(request, user_id):

    try:

        user = User.objects.get(id = user_id)

    except User.DoesNotExist:

        data = {
            'status'  : False,
            'message' : "UserId might not exist",
            'authenticatedData' : None
        }
        response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    permitted_views = user.view_permissions.all()

    data = {
                'status'  : True,
                'message' : "Successful",
                'authenticatedData' : {
                    'id' : user.client.id,
                    "user_id":user.id,
                    'name'  :  user.client.name,
                    'image'  :  user.client.logo.url if user.client.logo else settings.DEFAULT_IMAGE,
                    'branches': [
                        {
                            "name": permitted_view.branch.name,
                            "branch_id": permitted_view.branch.id,
                            "devices": [
                                    {
                                       'name': device.name,
                                       'device_id': device.id,
                                       'is_load'  : device.is_load,
                                       'is_source': device.is_source,
                                       'device_type': device.type.choice_name,
                                        'id': device_index+1
                                    }
                                    for device_index, device in enumerate(permitted_view.branch.device_set.all())
                                ],
                                "id": branch_index+1,
                        }
                        for branch_index, permitted_view in enumerate(permitted_views)
                    ]
                }
    }

    response = Response(data, status = status.HTTP_200_OK)
    response["Access-Control-Allow-Origin"] = "*"
    return response

@api_view(['GET', 'POST'])
def bills(request):

    if request.method == 'GET':
        bill = Bill.objects.all()
        serializer = BillSerializer(bill, many=True)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        serializer = BillSerializer(data = request.data)
        if serializer.is_valid():
            serializer.save()


            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data
            }

            return Response(data, status=status.HTTP_201_CREATED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
def alerts_data(request, user_id):

    if request.method == 'GET':

        try:

            alert_query = Alert_Setting.objects.get_or_create(user_id = user_id)
            alert, was_just_created = alert_query
            user = alert.user

            permitted_views = user.view_permissions.all()
            generators = [view_permission.branch.get_generators() for view_permission in permitted_views]
            result_list = list(chain(*generators))

        except IntegrityError as e:
            data = {
                'status' : False,
                'message' : 'Unsuccessful',
                'error' : "User id conflict"
                }

            return Response(data, status=status.HTTP_404_NOT_FOUND)


        serializer = Alert_SettingSerializer(alert)
        gen_serializer = DeviceMaintenanceSerializer(result_list, many = True)

        data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data,
                'generator_data': gen_serializer.data
                }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':

        alert = Alert_Setting.objects.get(user_id = user_id)
        serializer = Alert_SettingSerializer(alert, data = request.data.get('data'))
        user = alert.user

        gen_data = request.data.get('generator_data', [])

        for data in gen_data:

            device = Device.objects.get(id = data.get('id'))
            gen_serializer = DeviceMaintenanceSerializer(device, data = data)

            if gen_serializer.is_valid():

                gen_serializer.save()

        permitted_views = user.view_permissions.all()
        generators = [view_permission.branch.get_generators() for view_permission in permitted_views]
        result_list = list(chain(*generators))
        gen_serializer = DeviceMaintenanceSerializer(result_list, many = True)


        if serializer.is_valid():

            serializer.save()

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data,
                'generator_data': gen_serializer.data
            }

            return Response(data, status=status.HTTP_202_ACCEPTED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
def upload_monthly_diesel_balance(request, user_id):

    if request.method == 'POST':

        try:

            file      = request.data['image']
            branch_id = request.POST.get("branch")
            date      = request.POST.get("date")
            quantity  = request.POST.get("quantity")

            balance   = Month_End_Diesel_Balance.objects.create(image=file, branch_id = branch_id, date = date, quantity = quantity)
            print("BALANCE UPLOAD DATA: ", balance)

            data = {
                'status' : True,
                'message' : 'Successful'
            }

            return Response(data, status=status.HTTP_202_ACCEPTED)

        except:

            data = {
                'status' : False,
                'message' : 'Failed (Bad values)'
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    else:
        data = {
        'status' : False,
        'message' : 'Unsuccessful'
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def get_reports(request, user_id, end_date, period):

    end_date = time_helpers.convert_date(end_date)
    start_date = time_helpers.get_start_date_from(end_date, period)

    user = User.objects.filter(id = user_id)

    if user.exists():
        permitted_views = user[0].view_permissions.all()
        reports = {view.branch.name:view.branch.get_report_data(end_date, period) for view in permitted_views}

        if request.method == 'GET':

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : reports
            }

            return Response(data, status=status.HTTP_200_OK)

        elif request.method == 'POST':

            data = {
                'status' : False,
                'message' : 'Unsuccessful',
                'error' : ""
            }

            return Response(data, status=status.HTTP_405_METHOD_NOT_ALLOWED)
    else:
        return Response({}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def push_otd_view(request):

    if request.method == 'GET':

        push_otd.delay()

        return Response({},status=status.HTTP_200_OK)

    return Response({}, status=status.HTTP_404_NOT_FOUND)

class ACB_Control(APIView):

    permission_classes=[IsAuthenticated]
    serializer_class = ACB_serializer

    def post(self, request):

        serializer = self.serializer_class(data = request.data)

        serializer.is_valid(raise_exception=True)

        print(serializer.data)

        device_qs = Device.objects.filter(device_id = serializer.data.get("meter_id"), provider = "ACREL-ACB")

        if not device_qs.exists():
            return Response({"toggled":False, "error":"device not found"},status=status.HTTP_404_NOT_FOUND)

        toggle_device.delay(serializer.data.get("meter_id"), serializer.data.get("state"))

        return Response({"toggled":True},status=status.HTTP_200_OK)


class ACB_View(APIView):

    permission_classes=[IsAuthenticated]
    serializer_class = ACB_List_serializer

    def get(self, request, branch_id):

        device_qs = Device.objects.filter(branch_id = branch_id, provider = "ACREL-ACB")

        serializer = self.serializer_class(device_qs, many=True)

        # if not device_qs.exists():
        #     return Response({"toggled":False, "error":"device not found"},status=status.HTTP_404_NOT_FOUND)

        # toggle_device.delay(serializer.data.get("meter_id"), serializer.data.get("state"))

        return Response(serializer.data,status=status.HTTP_200_OK)


@api_view(['GET'])
def get_reports_baseline(request, user_id, end_date, period):

    end_date = time_helpers.convert_date(end_date).replace(hour=23, minute=59)

    start_date = time_helpers.get_start_date_from(end_date, period)
    user = User.objects.filter(id = user_id)

    if user.exists():
        permitted_views = user[0].view_permissions.all()
        reports = {view.branch.name:view.branch.get_separate_report_data(end_date, period) for view in permitted_views}

        if request.method == 'GET':

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : reports
            }

            return Response(data, status=status.HTTP_200_OK)

        elif request.method == 'POST':

            data = {
                'status' : False,
                'message' : 'Unsuccessful',
                'error' : ""
            }

            return Response(data, status=status.HTTP_405_METHOD_NOT_ALLOWED)
    else:
        return Response({}, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET', 'POST'])
def get_bill_schedule_data(request, user_id):

    if request.method == 'GET':
        bill = Bill_Mails.get_all_added_by(user_id=user_id)
        personal_data = Bill_Mails.get_personal_data(user_id=user_id)
        user = User.objects.get(id = user_id)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : {
                "external_recievers":bill,
                "personal_data": personal_data,
                "available_devices": user.get_permitted_devices()
            }
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        serializer = BillMailSerializer(data = request.data)
        if serializer.is_valid():
            serializer.save()


            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data
            }

            return Response(data, status=status.HTTP_201_CREATED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST', 'GET'])
def add_external_bill_reciever(request, user_id):

    if request.method == 'POST':

        received_json_data=json.loads(request.body)
        email = received_json_data['email']

        Bill_Mails.create_new(email = email, creator_id = user_id)
        added_recievers = Bill_Mails.get_all_added_by(user_id=user_id)
        personal_data = Bill_Mails.get_personal_data(user_id=user_id)
        user = User.objects.get(id = user_id)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : {
                "external_recievers":added_recievers,
                "personal_data": personal_data,
                "available_devices": user.get_permitted_devices()
            }
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status' : False,
            'message' : 'Successful',
            'data' : {
                "external_recievers":"",
                "personal_data": "",
                "available_devices": ""
            }
        }
        return Response({}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST', 'GET'])
def delete_mail_reciever(request, user_id):

    if request.method == 'POST':

        received_json_data=json.loads(request.body)
        reciever_id = received_json_data['receiver_id']

        Bill_Mails.objects.get(id = reciever_id).delete()

        added_recievers = Bill_Mails.get_all_added_by(user_id=user_id)
        personal_data = Bill_Mails.get_personal_data(user_id=user_id)
        user = User.objects.get(id = user_id)

        data = {
            'status' : True,
            'message' : 'Delete Successful',
            'data' : {
                "external_recievers":added_recievers,
                "personal_data": personal_data,
                "available_devices": user.get_permitted_devices()
            }
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status' : False,
            'message' : 'Successful',
            'data' : {
                "external_recievers":"",
                "personal_data": "",
                "available_devices": ""
            }
        }
        return Response({}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST', 'GET'])
def add_assigned_devices(request, user_id):

    if request.method == 'POST':

        received_json_data = json.loads(request.body)

        creator = Bill_Mails.objects.get(user_id = user_id)

        receiver_id = received_json_data.get('receiver_id', False)
        devices = received_json_data.get('selected_devices', False)
        frequency = received_json_data.get('frequency', False)

        if receiver_id: ## MEANING AN ATTEMPT TO ADD AN EXTERNAL DEVICE

            creator.add_reporting_device(*devices, external = True, external_receiver_id = receiver_id, frequency = frequency)

        else: ## MEANING AN ATTEMPT TO ADD A DEVICE PERSONALLY TO LOGGED IN USER

            creator.add_reporting_device(*devices, frequency = frequency)


        added_receivers = Bill_Mails.get_all_added_by(user_id=user_id)
        personal_data = Bill_Mails.get_personal_data(user_id=user_id)
        user = User.objects.get(id = user_id)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : {
                "external_recievers":added_receivers,
                "personal_data": personal_data,
                "available_devices": user.get_permitted_devices()
            }
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status' : False,
            'message' : 'Successful',
            'data' : {
                "external_recievers":"",
                "personal_data": "",
                "available_devices": ""
            }
        }
        return Response({}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def test_view(request, device_id, date1, date2):

    """Takes in the device id and two date ranges date1(old date) and date2(new date) and returns a list of the differences between the dates in the sequence"""

    d = Datalog.objects.all().filter(device=device_id).filter(post_date__range=(date1, date2))

    dates =  list(set([i.post_date for i in d]))

    kw_used = []
    for i in range(1, len(dates)):
        n = 0

        kw1 = d.filter(post_date = dates[i]).last().total_kw
        kw2 =  d.filter(post_date = dates[n]).last().total_kw
        n+=1
        kw_used.append(kw1-kw2)

    data = {
                'status'  : True,
                'message' : "Successful",
                'data' : kw_used,
            }

    return Response(data, status = status.HTTP_200_OK)


@api_view(['POST', 'GET'])
def download_pdf_view(request, user_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    if request.method == 'POST':

        received_json_data=json.loads(request.body)
        selected_devices = received_json_data['selected_devices']

        try:

            user = User.objects.get(id = user_id)
            recipient = Bill_Mails.objects.get(user_id = user.id)
            bill = Bill_Mails.make_bill(recipient, start_date, end_date, selected_devices)

            pdf = open(bill["file_path"], 'rb')

            response = FileResponse(pdf, status = status.HTTP_200_OK)
            response["Access-Control-Allow-Origin"] = "*"
            response["content_type"] = "text/pdf"

            return response

        except User.DoesNotExist:

            data = {
                'status'  : False,
                'message' : "UserId might not exist",
                'authenticatedData' : None
            }
            response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
            response["Access-Control-Allow-Origin"] = "*"
            return response

    else:
        data = {
            'status' : False,
            'message' : 'Failed',
            'data' : {
            }
        }
    return Response({}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST', 'GET'])
def mail_report_pdf_view(request, user_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    if request.method == 'POST':

        received_json_data=json.loads(request.body)
        selected_devices = received_json_data['selected_devices']
        email = received_json_data['email']

        try:

            user = User.objects.get(id = user_id)
            recipient = Bill_Mails.objects.get(user_id = user.id)
            bill = Bill_Mails.mail_pdf(recipient, start_date, end_date, selected_devices, email)


            pdf = open(bill["file_path"], 'rb')

            response = FileResponse(pdf, status = status.HTTP_200_OK)
            response["Access-Control-Allow-Origin"] = "*"
            response["content_type"] = "text/pdf"

            return response

        except User.DoesNotExist:

            data = {
                'status'  : False,
                'message' : "UserId might not exist",
                'authenticatedData' : None
            }
            response = Response(data, status = status.HTTP_401_UNAUTHORIZED)
            response["Access-Control-Allow-Origin"] = "*"
            return response

    else:
        data = {
            'status' : False,
            'message' : 'Failed',
            'data' : {
            }
        }
    return Response({}, status=status.HTTP_400_BAD_REQUEST)


# @api_view(['POST'])
# def send_monthly_report(request):
#     # 1) Validate
#     branch_id = request.data.get("branch_id")
#     month     = request.data.get("month")
#     year      = request.data.get("year")
#     if not all([branch_id, month, year]):
#         return Response({"error": "branch_id, month and year required"},
#                         status=status.HTTP_400_BAD_REQUEST)

#     # 2) Lookup branch and email
#     try:
#         branch = Branch.objects.get(id=branch_id)
#     except Branch.DoesNotExist:
#         return Response({"error": "Branch not found"},
#                         status=status.HTTP_404_NOT_FOUND)
#     if not branch.email:
#         return Response({"error": "Branch has no email on file"},
#                         status=status.HTTP_400_BAD_REQUEST)

#     # 3) Build URL and file paths
#     report_url = (
#         f"https://report-template-five.vercel.app/"
#         f"report?branch_id={branch_id}&month={month}&year={year}"
#     )
#     filename = f"report_{branch_id}{year}_{int(month):02d}.pdf"
#     pdf_path = f"/tmp/{filename}"

#     # 4) Spin up Playwright and snapshot to a single, exact-sized PDF
#     try:
#         with sync_playwright() as pw:
#             browser = pw.chromium.launch(headless=True)
#             page = browser.new_page(viewport={"width": 1280, "height": 1})

#             # load and wait for network + rendering
#             page.goto(report_url, wait_until="networkidle", timeout=120000)
#             page.wait_for_timeout(120000)

#             # measure full height of content
#             content_height = page.evaluate("""
#                 () => {
#                     const body = document.body;
#                     const html = document.documentElement;
#                     return Math.max(
#                       body.scrollHeight, body.offsetHeight,
#                       html.clientHeight, html.scrollHeight, html.offsetHeight
#                     );
#                 }
#             """)

#             # generate PDF at that exact size (in pixels)
#             page.pdf(
#                 path=pdf_path,
#                 print_background=True,
#                 width="1280px",
#                 height=f"{content_height}px",
#                 margin={"top": "0", "right": "0", "bottom": "0", "left": "0"},
#             )
#             browser.close()

#     except Exception as e:
#         return Response(
#             {"error": f"Playwright PDF generation failed: {e}"},
#             status=status.HTTP_500_INTERNAL_SERVER_ERROR
#         )

#     # 5) Send it through Mailgun, CC’ing any copy_email addresses
#     subject = f"Monthly Report: {branch.name} — {calendar.month_name[int(month)]} {year}"
#     text    = (
#         f"Hello {branch.name} Team,\n\n"
#         f"Please find attached your comprehensive energy report for "
#         f"{calendar.month_name[int(month)]} {year}. Let us know if you have any questions.\n\n"
#         f"Best regards,\n"
#         f"The Wyre Team"
#     )

#     # If branch.copy_email exists, just send exactly as a comma-separated string
#     cc_list = [email.strip() for email in branch.copy_email.split(',') if email.strip()] if branch.copy_email else None

#     try:
#         with open(pdf_path, "rb") as f:
#             post_data = {
#                 "from":    settings.SUPPORT_EMAIL,
#                 "to":      branch.email,
#                 "subject": subject,
#                 "text":    text,
#             }
#             # only include “cc” if there is at least one address
#             if cc_list:
#                 post_data["cc"] = cc_list

#             mail_resp = requests.post(
#                 settings.MAILGUN_URL,
#                 auth=("api", settings.MAILGUN_API_KEY),
#                 files={"attachment": (filename, f)},
#                 data=post_data,
#                 timeout=10
#             )
#             mail_resp.raise_for_status()
#     except requests.RequestException as e:
#         return Response(
#             {"error": f"Mailgun send failed: {e}"},
#             status=status.HTTP_502_BAD_GATEWAY
#         )
#     finally:
#         if os.path.exists(pdf_path):
#             os.remove(pdf_path)

#     return Response({"status": "ok", "message": f"PDF sent to {branch.email} (cc: {cc_list or 'none'})"})


from django.core.mail import EmailMessage
@api_view(['POST'])
def send_monthly_report(request):
    # 1) Validate
    branch_id = request.data.get("branch_id")
    month     = request.data.get("month")
    year      = request.data.get("year")
    if not all([branch_id, month, year]):
        return Response({"error": "branch_id, month and year required"},
                        status=status.HTTP_400_BAD_REQUEST)

    # 2) Lookup branch and email
    try:
        branch = Branch.objects.get(id=branch_id)
    except Branch.DoesNotExist:
        return Response({"error": "Branch not found"},
                        status=status.HTTP_404_NOT_FOUND)
    if not branch.email:
        return Response({"error": "Branch has no email on file"},
                        status=status.HTTP_400_BAD_REQUEST)

    # 3) Build URL and file paths
    report_url = (
        f"https://report-template-five.vercel.app/"
        f"report?branch_id={branch_id}&month={month}&year={year}"
    )
    filename = f"report_{branch_id}{year}_{int(month):02d}.pdf"
    pdf_path = f"/tmp/{filename}"

    # 4) Spin up Playwright and snapshot to a single, exact-sized PDF
    try:
        with sync_playwright() as pw:
            browser = pw.chromium.launch(headless=True)
            page = browser.new_page(viewport={"width": 1280, "height": 1})
            page.goto(report_url, wait_until="networkidle", timeout=120000)
            page.wait_for_timeout(120000)
            content_height = page.evaluate("""
                () => {
                    const body = document.body;
                    const html = document.documentElement;
                    return Math.max(
                      body.scrollHeight, body.offsetHeight,
                      html.clientHeight, html.scrollHeight, html.offsetHeight
                    );
                }
            """)
            page.pdf(
                path=pdf_path,
                print_background=True,
                width="1280px",
                height=f"{content_height}px",
                margin={"top": "0", "right": "0", "bottom": "0", "left": "0"},
            )
            browser.close()
    except Exception as e:
        return Response(
            {"error": f"Playwright PDF generation failed: {e}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # 5) Send it through Django backend email
    subject = f"Monthly Report: {branch.name} — {calendar.month_name[int(month)]} {year}"
    text    = (
        f"Hello {branch.name} Team,\n\n"
        f"Please find attached your comprehensive energy report for "
        f"{calendar.month_name[int(month)]} {year}. Let us know if you have any questions.\n\n"
        f"Best regards,\n"
        f"The Wyre Team"
    )
    cc_list = [email.strip() for email in branch.copy_email.split(',') if email.strip()] if branch.copy_email else []

    try:
        email = EmailMessage(
            subject=subject,
            body=text,
            from_email=settings.DEFAULT_FROM_EMAIL,
            # to= [branch.email],
            # cc= cc_list
            to= ["<EMAIL>"],
            cc= ["<EMAIL>", "<EMAIL>"]
        )
        with open(pdf_path, "rb") as f:
            email.attach(filename, f.read(), 'application/pdf')
        email.send(fail_silently=False)
    except Exception as e:
        return Response(
            {"error": f"Django email send failed: {e}"},
            status=status.HTTP_502_BAD_GATEWAY
        )
    finally:
        if os.path.exists(pdf_path):
            os.remove(pdf_path)

    return Response({"status": "ok", "message": f"PDF sent to {branch.email} (cc: {cc_list or 'none'})"})



@api_view(['GET', 'POST'])
def branches(request):

    logger.error("Test!!")
    # print(object.get)
    if request.method == 'GET':
        branch = Branch.objects.all()
        serializer = BranchSerializer(branch, many=True)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        serializer = BranchSerializer(data = request.data)
        if serializer.is_valid():
            serializer.save()

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data
            }

            return Response(data, status=status.HTTP_201_CREATED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
def clients_all(request):

    logger.error("Test!!")
    # print(object.get)
    if request.method == 'GET':
        client = Client.objects.all()
        serializer = ClientsAllSerializer(client, many=True)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':

            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : ""
        }

    return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
def client_branches_all(request, client_id):

    logger.error("Test!!")
    # print(object.get)
    if request.method == 'GET':
        branch = Branch.objects.filter(client__id=client_id)
        serializer = BranchLeanSerializer(branch, many=True)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':

            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : ""
        }

    return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view([ 'GET'])
# @authentication_classes([JSONWebTokenAuthentication])
@permission_classes([IsAuthenticated])
def temporary_admin_access(request, client_id, branch_id):

    if request.method == "GET":

        branch = Branch.objects.filter(client__id=client_id, id=branch_id)

        if branch.exists():

            ViewPermission.objects.filter(user=request.user).delete()

            ViewPermission.objects.create(user=request.user, branch=branch.last())

            response = Response({
                                'status'  : True,
                                'message' : "Access Granted",
                                'data' : [],
                                }, status = status.HTTP_200_OK)
            response["Access-Control-Allow-Origin"] = "*"
            return response


    response = Response({
                            'status'  : False,
                            'message' : "Unknown client/branch",
                            'data' : [],
                            }, status = status.HTTP_404_NOT_FOUND)
    response["Access-Control-Allow-Origin"] = "*"
    return response


@api_view(['GET', 'PUT', 'DELETE'])
def update_branch(request, branch_id):

    try:
        branch = Branch.objects.get(id=branch_id)
    except Branch.DoesNotExist:
        data = {
            'status' : False,
            'error' : 'Branch does not exist',
        }

        return Response(data, status=status.HTTP_404_NOT_FOUND)


    if request.method == 'GET':
        serializer = BranchSerializer(branch)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)


    elif request.method == 'PUT':
        serializer = BranchSerializer(branch, data = request.data, partial=True)

        if serializer.is_valid():
            serializer.save()

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data
            }

            return Response(data, status=status.HTTP_201_CREATED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


    elif request.method == 'DELETE':
        branch.delete()

        data = {
            'status' : True,
            'message' : 'Successful',

        }

        return Response(data, status=status.HTTP_204_NO_CONTENT)


@api_view(['GET', 'POST'])
def devices(request):

    if request.method == 'GET':
        device = Device.objects.all()
        serializer = DeviceSerializer(device, many=True)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        serializer = DeviceSerializer(data = request.data)
        if serializer.is_valid():
            serializer.save()

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data
            }

            return Response(data, status=status.HTTP_201_CREATED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'PUT', 'DELETE'])
def update_device(request, device_id):

    try:
        device = Device.objects.get(id=device_id)
    except Device.DoesNotExist:
        data = {
            'status' : False,
            'error' : 'Device does not exist',
        }

        return Response(data, status=status.HTTP_404_NOT_FOUND)


    if request.method == 'GET':
        serializer = DeviceSerializer(device)

        data = {
            'status' : True,
            'message' : 'Successful',
            'data' : serializer.data
        }

        return Response(data, status=status.HTTP_200_OK)


    elif request.method == 'PUT':
        serializer = DeviceSerializer(device, data = request.data, partial=True)

        if serializer.is_valid():
            serializer.save()

            data = {
                'status' : True,
                'message' : 'Successful',
                'data' : serializer.data
            }

            return Response(data, status=status.HTTP_201_CREATED)

        else:
            data = {
            'status' : False,
            'message' : 'Unsuccessful',
            'error' : serializer.errors
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


    elif request.method == 'DELETE':
        device.delete()

        data = {
            'status' : True,
            'message' : 'Successful',

        }

        return Response(data, status=status.HTTP_204_NO_CONTENT)


@api_view(['GET', 'POST'])
def user_pemit(request, user_id):

    """Getting and adding user permissions."""

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        data = {
                'status'  : False,
                'error' : "Does not exist",
            }

        return Response(data, status=status.HTTP_404_NOT_FOUND)


    if request.method == 'GET':
        perm = user.view_permissions.all()
        serializer = ViewSerializer(perm, many=True)

        data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
                }

        return Response(data, status=status.HTTP_200_OK)


    elif request.method == 'POST':
        try:
            d = {
                'user' : user.id,
                'branch' : request.data['branch']
            }
        except KeyError:
            data = {
                'error' : {"branch": ["This field is required"]},
            }

            return Response(data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


        serializer = ViewSerializer(data = d)
        if serializer.is_valid():
            serializer.save()

            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : serializer.data,
            }

            return Response(data, status = status.HTTP_201_CREATED)

        else:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : serializer.errors,
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def range_data(request, device_id, date1, date2):
    """Takes in the device id and two date ranges and returns the 'Summary energy register 1' difference i.e date1(recent) - date2(old date) """

    data1 = Datalog.objects.all().filter(device = device_id).filter(post_date= date1).last()
    data2 = Datalog.objects.all().filter(device = device_id).filter(post_date = date2).first()

    data = {
                'status'  : True,
                'message' : "Successful",
                'data' : data1.summary_energy_register_1 - data2.summary_energy_register_1,
            }

    return Response(data, status = status.HTTP_200_OK)


@api_view(['GET'])
def kilo_per_day(request, device_id, date1, date2):

    """Takes in the device id and two date ranges date1(old date) and date2(new date) and returns a list of the differences between the dates in the sequence"""

    d = Datalog.objects.all().filter(device=device_id).filter(post_date__range=(date1, date2))

    dates =  list(set([i.post_date for i in d]))

    kw_used = []
    for i in range(1, len(dates)):
        n = 0

        kw1 = d.filter(post_date = dates[i]).last().total_kw
        kw2 =  d.filter(post_date = dates[n]).last().total_kw
        n+=1
        kw_used.append(kw1-kw2)

    data = {
                'status'  : True,
                'message' : "Successful",
                'data' : kw_used,
            }

    return Response(data, status = status.HTTP_200_OK)


@api_view(['GET'])
def stats_per_day(request, device_id, date1, date2):

    """Takes in the device id and two date ranges date1(old date) and date2(new date) and returns a dictionary of the max_kw, min_kw and average_kw for each date in the range of two dates. """

    d = Datalog.objects.all().filter(device=device_id).filter(post_date__range=(date1, date2))

    dates =  list(set([i.post_date for i in d]))

    stats = {}
    for i in range(1, len(dates)):

        kw = [k.total_kw for k in d.filter(post_date = dates[i])]
        stats[dates[i]] ={'max_kw' : max(kw), 'min_kw' : min(kw), 'avg_kw' : sum(kw)/len(kw)}

    data = {
                'status'  : True,
                'message' : "Successful",
                'data' : stats,
            }

    return Response(data, status = status.HTTP_200_OK)

@api_view(['GET'])
def get_all_devices(request, password):
    print(settings.RAW_DATA_PASSWORD, password)
    if str(settings.RAW_DATA_PASSWORD) != str(password):

        return Response({"message":"INVALID PASSWORD"}, status = status.HTTP_401_UNAUTHORIZED)

    devices = Device.objects.all()
    serializer = DeviceListSerializer(devices, many=True)

    return Response(serializer.data, status = status.HTTP_200_OK)

@api_view(['GET'])
def get_aggregated_device_readings(request, password, device_id, start_date, end_date):
    if str(settings.RAW_DATA_PASSWORD) != str(password):
        return Response({"message":"INVALID PASSWORD"}, status = status.HTTP_401_UNAUTHORIZED)

    start_date = time_helpers.convert_date(start_date).date()
    end_date = time_helpers.convert_date(end_date).date()

    readings = Reading.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date])
    device = Device.objects.filter(device_id=device_id)

    if device.exists():
        if readings.exists():
            readings_values = readings.values()

            time_usage = aggregation_helpers.aggregate_time_of_use_from_readings(readings_values)
            energy_usage = aggregation_helpers.aggregate_usage_from_readings(readings_values)
            time_of_usage = aggregation_helpers.aggregate_time_of_use(readings_values)

            # Create Excel file using context manager
            buffer = BytesIO()
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                time_usage.to_excel(writer, index=False, sheet_name='Time Usage')
                energy_usage.to_excel(writer, index=False, sheet_name='Energy Usage')
                time_of_usage.to_excel(writer, index=False, sheet_name='Office hours Usage')

            # Prepare the response
            buffer.seek(0)
            response = HttpResponse(
                buffer.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{device.last().branch.name}-{device.last().name} from {start_date} to {end_date}.xlsx"'
            return response
        else:
            return Response({"error":"No Data!!"}, status = status.HTTP_404_NOT_FOUND)
    else:
        return Response({"error":"Device not found.!!"}, status = status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def get_device_readings(request, password, device_id, start_date, end_date):

    print(settings.RAW_DATA_PASSWORD, password)
    if str(settings.RAW_DATA_PASSWORD) != str(password):

        return Response({"message":"INVALID PASSWORD"}, status = status.HTTP_401_UNAUTHORIZED)

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    readings:Reading = Reading.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date])
    serializer = ReadingListSerializer(readings, many=True)

    return Response(serializer.data, status = status.HTTP_200_OK)


@api_view(['GET'])
def get_device_zero_readings(request, password, device_id, start_date, end_date):

    print(settings.RAW_DATA_PASSWORD, password)
    if str(settings.RAW_DATA_PASSWORD) != str(password):

        return Response({"message":"INVALID PASSWORD"}, status = status.HTTP_401_UNAUTHORIZED)

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    readings:Reading = Reading.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date], kwh_import__lte = 1)
    datalogs:Datalog = Datalog.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date], summary_energy_register_2__lte = 1)
    readings_serializer = ReadingListSerializer(readings, many=True)
    datalog_serializer = DatalogListSerializer(datalogs, many=True)

    return Response({
                        "readings":readings_serializer.data,
                        "datalogs":datalog_serializer.data
                    },
                            status = status.HTTP_200_OK)

@api_view(['GET'])
def delete_device_zero_readings(request, password, device_id, start_date, end_date):

    print(settings.RAW_DATA_PASSWORD, password)
    if str(settings.RAW_DATA_PASSWORD) != str(password):

        return Response({"message":"INVALID PASSWORD"}, status = status.HTTP_401_UNAUTHORIZED)

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    readings:Reading = Reading.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date], kwh_import__lte = 1).delete()
    datalogs:Datalog = Datalog.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date], summary_energy_register_2__lte = 1).delete()

    readings:Reading = Reading.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date], kwh_import__lte = 1)
    datalogs:Datalog = Datalog.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date], summary_energy_register_2__lte = 1)
    readings_serializer = ReadingListSerializer(readings, many=True)
    datalog_serializer = DatalogListSerializer(datalogs, many=True)


    return Response({
                        "readings":readings_serializer.data,
                        "datalogs":datalog_serializer.data
                    },
                            status = status.HTTP_200_OK)

#Here is the function to get the timed device readings
#TODO: Aggregate the readings
@api_view(['GET'])
def get_timed_device_readings(request, password, device_id, start_date, end_date, start_time, end_time):

    if str(settings.RAW_DATA_PASSWORD) != str(password):

        return Response({"message":"INVALID PASSWORD"}, status = status.HTTP_401_UNAUTHORIZED)

    start_date = time_helpers.convert_date(start_date).date()
    end_date = time_helpers.convert_date(end_date).date()

    readings:Reading = Reading.objects.filter(device__device_id = device_id, post_date__range = [start_date, end_date])
    device = Device.objects.filter(device_id=device_id)

    try:

        start_time, end_time = int(start_time), int(end_time)

    except:

        return Response({"error":"Invalid time!!"}, status = status.HTTP_404_NOT_FOUND)

    if device.exists():

        if readings.exists():

            readings_values = readings.values()

            # time_usage = aggregation_helpers.aggregate_time_of_use_from_readings(readings_values)
            # energy_usage = aggregation_helpers.aggregate_usage_from_readings(readings_values)
            time_of_usage = aggregation_helpers.aggregate_time_of_use(readings_values, start_time, end_time)

            # Save the DataFrame to a BytesIO object as an Excel file
            with BytesIO() as b:
                writer = pd.ExcelWriter(b, engine='openpyxl')
                time_of_usage.to_excel(writer, index=False, sheet_name='Office hours Usage')
                # time_usage.to_excel(writer, index=False, sheet_name='Time Usage')
                # energy_usage.to_excel(writer, index=False, sheet_name='Energy Usage')
                writer.save()
                b.seek(0)
                response = HttpResponse(b, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = f'attachment; filename="{device.last().branch.name}-{device.last().name} from {start_date} to {end_date}.xlsx"'
                return response

        else:
            return Response({"error":"No Data!!"}, status = status.HTTP_404_NOT_FOUND)
    else:
        return Response({"error":"Device not found.!!"}, status = status.HTTP_404_NOT_FOUND)

    return Response(serializer.data, status = status.HTTP_200_OK)

@api_view(['GET', 'POST', 'PUT', 'DELETE'])
def  cost_tracker(request, user_id, cost_type):

    if request.method == "GET":

        branch_id = request.data.get("branch", "")

        costs = Cost.objects.filter(branch=branch_id)

        serializer = CostTrackerSerializer(costs, many=True)

        return Response({'status': True, 'data': serializer.data})

    elif request.method == "POST":

        branch_id = request.data.get("branch", "")
        user      = User.objects.get(id = user_id)
        permitted_branch = user.view_permissions.filter(branch_id = branch_id)

        cost_types = ['petrol', 'diesel', 'ipp', 'pre-paid', 'post-paid']

        if not permitted_branch.exists():


            data = {
                'status'  : False,
                'message' : "Not permitted to alter this branch",
                'error' : "Not permitted to alter this branch",
            }

            return Response(data, status = status.HTTP_401_UNAUTHORIZED)

        if cost_type in cost_types:

            serializer = CostTrackerSerializer(data=request.data)

            if serializer.is_valid():

                cost = Cost.objects.create(**serializer.validated_data, cost_type = cost_type)


                serializer  = CostTrackerSerializer(cost)

                data = {
                    'status'  : True,
                    'message' : "Successful",
                    'data' : serializer.data,
                }

                return Response(data, status = status.HTTP_201_CREATED)

            else:
                data = {
                    'status'  : False,
                    'message' : "Unsuccessful",
                    'error' : serializer.errors,
                }

                return Response(data, status = status.HTTP_400_BAD_REQUEST)


        else:
                data = {
                    'status'  : False,
                    'message' : "Unsuccessful",
                    'error' : f"Invalid cost type in url. Must be {str(cost_types)}"
                }

                return Response(data, status = status.HTTP_400_BAD_REQUEST)

    elif request.method == "PUT":
        try:
            cost = Cost.objects.get(id=request.data.get("id"))
        except Cost.DoesNotExist:
            return Response({'error': 'Cost not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = CostTrackerSerializer(cost, data=request.data)

        if serializer.is_valid():
            serializer.save()
            data = {
                'status': True,
                'message': "Successful",
                'data': serializer.data,
            }
            return Response(data, status=status.HTTP_200_OK)
        else:
            data = {
                'status': False,
                'message': "Unsuccessful",
                'error': serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    elif request.method == "DELETE":
        try:
            cost = Cost.objects.get(id=request.data.get("id"))
        except Cost.DoesNotExist:
            return Response({'error': 'Cost entry not found'}, status=status.HTTP_404_NOT_FOUND)

        cost.delete()
        return Response({'status': True, 'message': 'Cost entry deleted successfully'}, status=status.HTTP_200_OK)


@api_view(['GET'])
# @permission_classes([IsAuthenticated])
def branch_generators(request, branch_id):
    """
    Return all devices of type "GENERATOR" for the given branch
    that the user is allowed to view.
    """
    # 1) Verify the branch exists
    try:
        branch = Branch.objects.get(id=branch_id)
    except Branch.DoesNotExist:
        return Response(
            {'error': 'Branch not found.'},
            status=status.HTTP_404_NOT_FOUND
        )

    # 2) Fetch only the generator devices for that branch
    generators = Device.objects.filter(
        branch_id=branch_id,
        type__choice_name='GENERATOR'
    ).only('name', 'device_id')

    # 3) Build the payload
    payload = [
        {
            'name': dev.name,
            'device_id': dev.device_id,
        }
        for dev in generators
    ]

    return Response({'generators': payload}, status=status.HTTP_200_OK)

@api_view(['GET'])
def  cost_tracker_overview(request, user_id):

    if request.method == "GET":

        user      = User.objects.get(id = user_id)
        permitted_views = user.view_permissions.all()

        costs = {}

        for permitted_view in permitted_views:

            branch = permitted_view.branch

            if permitted_view.branch.device_set.filter(type__choice_name = "IPP"):

                branch_diesel  = DieselCostSerializer(branch.cost_set.filter(cost_type = "diesel"), many =True)
                branch_utility = UtilityCostSerializer(branch.cost_set.exclude(cost_type = "diesel"), many =True)
                branch_ipp = IPPCostSerializer(branch.cost_set.filter(cost_type = "ipp"), many =True)

                costs["diesel_overview"] = json.loads(branch.cost_data)["diesel"]
                costs["utility_overview"] = json.loads(branch.cost_data)["utility"]
                costs["ipp_overview"] = json.loads(branch.cost_data)["ipp"]

                costs[branch.name] = dict(diesel = branch_diesel.data,
                                            utility = branch_utility.data,
                                            ipp = branch_ipp.data,
                                            baseline = branch.get_forcast_vs_usage(datetime.now())
                                            )
                costs["has_generator"] = branch.has_generator

            else:

                branch_diesel  = DieselCostSerializer(branch.cost_set.filter(cost_type = "diesel"), many =True)
                branch_utility = UtilityCostSerializer(branch.cost_set.exclude(cost_type = "diesel"), many =True)
                branch_ipp = IPPCostSerializer(branch.cost_set.filter(cost_type = "ipp"), many =True)

                costs["diesel_overview"] = json.loads(branch.cost_data)["diesel"]
                costs["utility_overview"] = json.loads(branch.cost_data)["utility"]

                costs[branch.name] = dict(diesel = branch_diesel.data,
                                            utility = branch_utility.data,
                                            baseline = branch.get_forcast_vs_usage(datetime.now())
                                            )
                costs["has_generator"] = branch.has_generator

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : costs,
        }

        return Response(data, status = status.HTTP_200_OK)

    else:
            data = {
                'status'  : False,
                'message' : "GET only allowed",
                'error' : "",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def get_quipments(request, user_id):

    if request.method == "GET":

        try:

            user = User.objects.get(id = user_id)

        except User.DoesNotExist:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : "bad user id",
            }

            return Response(data, status = status.HTTP_404_NOT_FOUND)


        permitted_views = user.view_permissions.all()

        equipments = {view.branch.name: {"id":view.branch.id,
                                        "equipments": view.branch.equipment} for view in permitted_views}

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : equipments,
        }

        return Response(data, status = status.HTTP_200_OK)


    else:
            data = {
                'status'  : False,
                'message' : "Unsuccessful",
                'error' : "BAD REQUEST ONLY GET ALLOWED"
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
def equipment(request, branch_id, user_id, equipment_id =None, action = None):
    """If equipment ID is included in the request, edit the equipment else, create a new equipment for the branch with the data sent."""


    if request.method == "POST":
        try:
            branch = Branch.objects.get(id=branch_id)

        except Branch.DoesNotExist:
            data = {
                    'status'  : False,
                    'message' : "Unsuccessful",
                    'error' : "Branch does not exist."
                }

            return Response(data, status = status.HTTP_404_NOT_FOUND)


        if equipment_id != None:
            try:
                object = Equipment.objects.get(id=equipment_id, branch=branch)

                if action == "delete":
                    object.delete()
                    data = {
                        'status'  : True,
                        'message' : "Deleted, Equipment will no longer exist."
                    }

                    return Response(data, status = status.HTTP_200_OK)

            except Equipment.DoesNotExist:
                data = {
                        'status'  : False,
                        'message' : "Unsuccessful",
                        'error' : "Equipment does not exist."
                    }

                return Response(data, status = status.HTTP_404_NOT_FOUND)

            serializer = EquipmentSerializer(object, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                data = {
                        'status'  : True,
                        'message' : "Successful",
                        'data' : serializer.data,
                    }

                return Response(data, status = status.HTTP_201_CREATED)

            else:
                data = {
                    'status'  : False,
                    'message' : "Unsuccessful",
                    'error' : serializer.errors,
                }

                return Response(data, status = status.HTTP_400_BAD_REQUEST)


        else:

            serializer = EquipmentSerializer(data= request.data)

            if serializer.is_valid():
                Equipment.objects.create(**serializer.validated_data, branch=branch)

                equipments = branch.equipments.all()

                serializer = EquipmentSerializer(equipments, many=True)

                data = {
                        'status'  : True,
                        'message' : "Successful",
                        'data' : serializer.data,
                    }

                return Response(data, status = status.HTTP_201_CREATED)

            else:
                data = {
                    'status'  : False,
                    'message' : "Unsuccessful",
                    'error' : serializer.errors,
                }

                return Response(data, status = status.HTTP_400_BAD_REQUEST)
            

class FuelConsumptionView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, branch_id):
        user_branch = request.user.view_permissions.all()
        if branch_id and user_branch:
            queryset = FuelConsumption.objects.filter(branch=branch_id)
            serializer = FuelEntryCreateSerializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response({"Error": "You have no branches attached to you"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request, branch_id):
        from datetime import datetime
        from calendar import monthrange

        branch = Branch.objects.get(id=branch_id)
        data = request.data.copy()
        data['branch'] = branch_id

        # Enforce required fields
        required_fields = ['start_date', 'quantity', 'generator_ids']
        missing = [field for field in required_fields if not data.get(field)]
        if missing:
            return Response(
                {"error": f"Missing required field(s): {', '.join(missing)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Handle generator_ids: expect a list, store as comma-separated string
        generator_ids = data.get('generator_ids', [])
        if isinstance(generator_ids, list):
            if not generator_ids:
                return Response({"error": "generator_ids cannot be empty"}, status=status.HTTP_400_BAD_REQUEST)
            data['generator_ids'] = ','.join(str(i) for i in generator_ids)
        else:
            if not generator_ids:
                return Response({"error": "generator_ids cannot be empty"}, status=status.HTTP_400_BAD_REQUEST)
            data['generator_ids'] = str(generator_ids)

        # Control end_date based on consumption_type
        consumption_type = data.get('consumption_type', None)
        start_date = data.get('start_date')
        if start_date and consumption_type:
            if consumption_type.lower() == 'daily':
                data['end_date'] = start_date
            elif consumption_type.lower() == 'monthly':
                dt = datetime.strptime(start_date, '%Y-%m-%d')
                last_day = monthrange(dt.year, dt.month)[1]
                data['end_date'] = dt.replace(day=last_day).strftime('%Y-%m-%d')

        # Check if an entry already exists for the start date of this Branch
        try:
            existing_entry = FuelConsumption.objects.get(branch=branch, start_date=data['start_date'])
        except FuelConsumption.DoesNotExist:
            existing_entry = 0

        if existing_entry:
            return Response({'error': 'An entry already exists for this date'}, status=status.HTTP_400_BAD_REQUEST)

        serializer = FuelEntryCreateSerializer(data=data)

        try:
            obj = DieselOverviewHistory.objects.get(branch=branch_id, start_date=data['start_date'])
            obj.delete()
        except DieselOverviewHistory.DoesNotExist:
            pass

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class FuelEntryApiView(APIView):
    permission_classes=[IsAuthenticated]

    detail_serializer = FuelEntryCreateSerializer
    create_serializer = FuelEntryCreateSerializer

    def get(self, request):

        from_date = request.GET.get("from_date")
        to_date = request.GET.get("to_date")

        user_branch = request.user.view_permissions.all()

        if user_branch.exists():

            if from_date and to_date:

                entries = user_branch.last()\
                                .branch\
                                .fuelconsumption_set.filter(
                                    start_date__gte=from_date,
                                    start_date__lte=to_date
                                    )\
                                .order_by("-id")

            else:

                entries = user_branch.last()\
                                .branch\
                                .fuelconsumption_set.all()\
                                .order_by("-id")

            if entries:

                serializer = self.create_serializer(entries, many = True)

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:

                return Response({"Error": "No entries found"}, status=status.HTTP_404_NOT_FOUND)
        else:

            return Response({"Error": "You have no branches attached to you"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):

        user_branch = request.user.view_permissions.all()

        if user_branch.exists():

            request.data["branch"] = user_branch.last().branch.id
            serializer = self.create_serializer(data=request.data)

            if serializer.is_valid():

                serializer.save()

                return Response(serializer.data, status=status.HTTP_200_OK)

            else:
                return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)

        else:

            return Response({"Error": "You have no branches attached to you"}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
def update_fuel_entry(request, pk):

    entry = get_object_or_404(FuelConsumption, id=pk)
    serializer = FuelEntryCreateSerializer(entry, data=request.data, partial=True)

    # Check if the start date is being changed and if an entry already exists for the new start date
    if 'start_date' in request.data and entry.start_date != datetime.datetime.strptime(request.data['start_date'], '%Y-%m-%d').date():

        # Check if an entry already exists for the start date of this Branch
        try:
            existing_entry = FuelConsumption.objects.get(branch=entry.branch, start_date=request.data['start_date'])
        except FuelConsumption.DoesNotExist:
            existing_entry = 0

        if existing_entry :
            return Response({'error': 'An entry already exists for this date, change only the Amount'}, status=status.HTTP_400_BAD_REQUEST)

    if serializer.is_valid():
        try:
            obj = DieselOverviewHistory.objects.get(branch=entry.branch.id, start_date=entry.start_date)
            if obj.data is None:
                obj.data = {}
            obj.data['quantity'] = serializer.initial_data['quantity']
            obj.save()
        except DieselOverviewHistory.DoesNotExist:
            pass

        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
def delete_fuel_entry(request, pk):
    entry = get_object_or_404(FuelConsumption, id=pk)

    try:
        obj = DieselOverviewHistory.objects.get(branch=entry.branch.id, start_date=entry.start_date)
        obj.delete()
    except DieselOverviewHistory.DoesNotExist:
        pass

    entry.delete()
    data = {
            'status'  : True,
            'message' : "Successfully deleted entry",
        }
    return Response(data, status=status.HTTP_204_NO_CONTENT)

@api_view(['GET'])
def get_diesel_entry_overview(request, user_id, year, month):

    if request.method == "GET":

        user = User.objects.get(id = user_id)
        permitted_views = user.view_permissions.all()

        for permitted_view in permitted_views:
            branch = permitted_view.branch

            diesel_entry = branch.get_diesel_entry_overview(year, month)

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : diesel_entry,
        }

        return Response(data, status = status.HTTP_200_OK)

    else:
            data = {
                'status'  : False,
                'message' : "GET only allowed",
                'error' : "",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def periodic_utility(request, start_date, end_date):
    from datetime import datetime

    if request.method == "GET":
        # Parse the start and end date strings into datetime objects
        start_date = datetime.strptime(start_date, "%d-%m-%Y %H:%M")
        end_date = datetime.strptime(end_date, "%d-%m-%Y %H:%M")

        # Convert the datetime objects to strings in the correct format
        formatted_start_date = start_date.strftime("%Y-%m-%d %H:%M")
        formatted_end_date = end_date.strftime("%Y-%m-%d %H:%M")

        branch = Branch.objects.get(id=21)
        value = branch.get_periodic_utility_usage(formatted_start_date, formatted_end_date, "monthly")

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : value,
        }

        return Response(data, status = status.HTTP_200_OK)

    else:
            data = {
                'status'  : False,
                'message' : "GET only allowed",
                'error' : "",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)

# @api_view(['GET'])
# def branch_demand(request, user_id, start_date, end_date):
#     from datetime import datetime

#     if request.method == "GET":

#         user = User.objects.get(id = user_id)
#         permitted_views = user.view_permissions.all()

#         for permitted_view in permitted_views:
#             branch = permitted_view.branch
#             # print('((((((((((((((()))))))))))))))')
#             # print(branch)
#             # print('((((((((((((((()))))))))))))))')

#             i = branch.branch_demand(start_date, end_date)

#         data = {
#             'status'  : True,
#             'message' : "Successful",
#             'data' : i,
#         }

#         return Response(data, status = status.HTTP_200_OK)

#     else:
#             data = {
#                 'status'  : False,
#                 'message' : "GET only allowed",
#                 'error' : "",
#             }

#             return Response(data, status = status.HTTP_400_BAD_REQUEST)

# @api_view(['GET'])
# def branch_demand2(request, user_id, start_date, end_date):
#     from datetime import datetime

#     if request.method == "GET":
#         try:
#             user = User.objects.get(id=user_id)
#         except User.DoesNotExist:
#             return Response({'status': False, 'message': 'User not found', 'data': None}, status=status.HTTP_404_NOT_FOUND)

#         permitted_views = user.view_permissions.all()
#         if not permitted_views:
#             return Response({'status': False, 'message': 'No permitted branches for this user', 'data': None}, status=status.HTTP_404_NOT_FOUND)

#         results = []
#         for permitted_view in permitted_views:
#             branch = permitted_view.branch
#             if not branch or not Branch.objects.filter(id=branch.id).exists():
#                 continue
#             result = branch.branch_demand(start_date, end_date)
#             results.append({
#                 # "branch_id": branch.id,
#                 # "branch_name": branch.name,
#                 "demand": result,
#             })

#         data = {
#             'status': True,
#             'message': "Successful",
#             'data': results,
#         }
#         return Response(data, status=status.HTTP_200_OK)
#     else:
#         data = {
#             'status': False,
#             'message': "GET only allowed",
#             'error': "",
#         }
#         return Response(data, status=status.HTTP_400_BAD_REQUEST)
    


@api_view(['GET'])
def branch_demand(request, user_id, start_date, end_date):
    from datetime import datetime

    if request.method != "GET":
        return Response({'status': False, 'message': "GET only allowed", 'error': ""}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({'status': False, 'message': 'User not found', 'data': None}, status=status.HTTP_404_NOT_FOUND)

    # Prefetch branches and devices in one go
    permitted_views = user.view_permissions.select_related('branch').all()
    branch_ids = [pv.branch_id for pv in permitted_views if pv.branch_id]
    branches = Branch.objects.filter(id__in=branch_ids).prefetch_related('device_set')
    branch_map = {b.id: b for b in branches}

    # Bulk fetch all devices for these branches
    devices = Device.objects.filter(branch_id__in=branch_ids).select_related('type')
    devices_by_branch = {}
    for device in devices:
        devices_by_branch.setdefault(device.branch_id, []).append(device)

    def compute_branch_demand(branch_id):
        branch = branch_map[branch_id]
        branch_devices = devices_by_branch.get(branch_id, [])
        device_demand = []

        # Parallelize per-device aggregation
        def agg_device(device):
            agg_data = device.get_agg_kwh_for_period_within_op_time(start_date, end_date)
            min_val = 0 if agg_data.get("min") is None else round(agg_data["min"]/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = 0 if agg_data.get("max") is None else round(agg_data["max"]/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = 0 if agg_data.get("avg") is None else round(agg_data["avg"]/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            papr = round(avg_val/max_val, settings.DECIMAL_PLACES) if max_val != 0 else 0
            return {"device_name": device.name, "unit": "kVA", "min": min_val, "max": max_val, "avg": avg_val, "papr": papr}

        with ThreadPoolExecutor(max_workers=8) as executor:
            results = list(executor.map(agg_device, branch_devices))

        device_demand.extend(results)
        power_factor = KW_TO_KVA_MULTIPLIER

        # Calculate overall branch PAPR
        branch_papr = 0
        if device_demand:
            max_values = [d['max'] for d in device_demand if d['max'] is not None]
            avg_values = [d['avg'] for d in device_demand if d['avg'] is not None]
            branch_max = max(max_values) if max_values else 0
            branch_avg = sum(avg_values) / len(avg_values) if avg_values else 0
            if branch_max > 0:
                branch_papr = round(branch_avg / branch_max, settings.DECIMAL_PLACES)

        return {
            "branch_id": branch.id,
            "branch_name": branch.name,
            "unit": "KVA", 
            "papr": branch_papr, 
            "p.f": power_factor, 
            "devices_demands": device_demand
        }

    # Parallelize per-branch computation
    results = []
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {executor.submit(compute_branch_demand, branch_id): branch_id for branch_id in branch_ids}
        for fut in as_completed(futures):
            result = fut.result()
            results.append(result)

    data = {
        'status': True,
        'message': "Successful",
        'data': results,
    }
    return Response(data, status=status.HTTP_200_OK)



@api_view(['GET'])
@permission_classes([IsAuthenticated])
def branch_demand_by_branch(request, branch_id, start_date, end_date):
    """
    Returns branch demand for a given branch_id if the authenticated user has permission.
    """
    from datetime import datetime

    # 1. Get the authenticated user
    user = request.user

    # 2. Check permission
    has_permission = user.view_permissions.filter(branch_id=branch_id).exists()
    if not has_permission:
        return Response(
            {'status': False, 'message': 'You do not have permission to view this branch.'},
            status=status.HTTP_403_FORBIDDEN
        )

    # 3. Get the branch
    try:
        branch = Branch.objects.get(id=branch_id)
    except Branch.DoesNotExist:
        return Response(
            {'status': False, 'message': 'Branch not found.'},
            status=status.HTTP_404_NOT_FOUND
        )

    # 4. Compute demand
    devices = branch.device_set.select_related('type').all()

    def agg_device(device):
        agg_data = device.get_agg_kwh_for_period_within_op_time(start_date, end_date)
        min_val = 0 if agg_data.get("min") is None else round(agg_data["min"]/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
        max_val = 0 if agg_data.get("max") is None else round(agg_data["max"]/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
        avg_val = 0 if agg_data.get("avg") is None else round(agg_data["avg"]/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
        papr = round(avg_val/max_val, settings.DECIMAL_PLACES) if max_val != 0 else 0
        return {"device_name": device.name, "unit": "kVA", "min": min_val, "max": max_val, "avg": avg_val, "papr": papr}

    from concurrent.futures import ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=8) as executor:
        device_demand = list(executor.map(agg_device, devices))

    power_factor = KW_TO_KVA_MULTIPLIER
    branch_papr = 0
    if device_demand:
        max_values = [d['max'] for d in device_demand if d['max'] is not None]
        avg_values = [d['avg'] for d in device_demand if d['avg'] is not None]
        branch_max = max(max_values) if max_values else 0
        branch_avg = sum(avg_values) / len(avg_values) if avg_values else 0
        if branch_max > 0:
            branch_papr = round(branch_avg / branch_max, settings.DECIMAL_PLACES)

    data = {
        "branch_id": branch.id,
        "branch_name": branch.name,
        "unit": "KVA",
        "papr": branch_papr,
        "p.f": power_factor,
        "devices_demands": device_demand
    }

    return Response({'status': True, 'message': "Successful", 'data': data}, status=status.HTTP_200_OK)
    

@api_view(['GET'])
def get_branch_uptime_outside_operating_hours(request, user_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    if request.method == "GET":

        user = User.objects.get(id = user_id)
        permitted_views = user.view_permissions.all()

        for permitted_view in permitted_views:
            branch = permitted_view.branch

            otd = branch.get_branch_uptime_outside_operating_hours(start_date, end_date)

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : otd,
        }

        return Response(data, status = status.HTTP_200_OK)

    else:
            data = {
                'status'  : False,
                'message' : "GET only allowed",
                'error' : "",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_client_uptime_outside_operating_hours(request, client_id, start_date, end_date):

    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    if request.method == "GET":

        client = Client.objects.get(id = client_id)
        branches = Branch.objects.filter(client = client)

        deviations = []

        for branch in branches:

            deviations.append(dict(
                branch_name = branch.name,
                operating_hours_deviation = branch.get_branch_uptime_outside_operating_hours(start_date, end_date)
            ))

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : deviations,
        }

        return Response(data, status = status.HTTP_200_OK)

    else:
            data = {
                'status'  : False,
                'message' : "GET only allowed",
                'error' : "",
            }

            return Response(data, status = status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def send_otd_report(request):
    if request.method == 'POST':

        client = Client.objects.get(id=15)
        branches = Branch.objects.filter(client=client)

        start_date, end_date = time_helpers.get_start_and_end_dates()
        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        today = datetime.datetime.today()
        yesterday = today - datetime.timedelta(days=1)
        yesterday_str = yesterday.strftime('%A, %d %B %Y')

        message = f"Operating Hours Deviation for {yesterday_str}\n\n{'BRANCH NAME'.ljust(20)}{'DEVICE NAME'.ljust(20)}{'UPTIME OUTSIDE OPERATING HOURS'.ljust(20)}{'WASTED ENERGY'.ljust(30)}\n"
        for branch in branches:
            otd = branch.get_branch_uptime_outside_operating_hours(start_date, end_date)
            for item in otd:
                device = Device.objects.get(id=item["device_id"])
                branch_name = branch.name
                device_name = device.name
                uptime = str(item['uptime_outside_operating_hours'])
                wasted_energy = str(item["wasted_kwh_energy"])
                print('((((((((((((((((((((( WASTED ENERGY )))))))))))))))))))))')
                print(wasted_energy)
                print('((((((((((((((((((((( WASTED ENERGY )))))))))))))))))))))')
                if uptime != "0 Hour(s) : 0 Minutes":
                    message += f"{branch_name.ljust(20)}{device_name.ljust(20)}{uptime.ljust(20)}{wasted_energy.ljust(30)}\n\n"
        if message == f"Operating Hours Deviation for {yesterday_str}\n\n{'BRANCH NAME'.ljust(20)}{'DEVICE NAME'.ljust(20)}{'UPTIME OUTSIDE OPERATING HOURS'.ljust(20)}{'WASTED ENERGY'.ljust(30)}\n":
            message = "No Deviations."
            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : message,
            }
            return Response(data, status = status.HTTP_200_OK)

        title = "Wyre-Operating-Hours-Deviation-Report"
        receievers = request.data.get('receivers')
        bcc = request.data.get('bcc')
        response = mailgun.Mailer.send_daily_otd(2, title, message, receievers, bcc)
        if response["status"] == True:
            message = "OTD report sent successfully."
            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : message,
            }
            return Response(data, status = status.HTTP_200_OK)
        else:
            message = "Failed to send OTD report."
            data = {
                'status'  : True,
                'message' : "Successful",
                'data' : message,
            }
            return Response(data, status = status.HTTP_400_BAD_REQUEST)

    else:
        return Response({'error': 'Only POST requests are allowed'}, status = status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_biling_data(request, device_id, start_date, end_date):

    if request.method == "GET":
        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        device = Device.objects.get(id=device_id)
        billing_data = device.get_billing_data(start_date, end_date)

        billing = {}
        billing['previous_month_kwh'] = billing_data["totals"]["previous_total"]["usage_kwh"]
        billing['previous_month_naira'] = billing_data["totals"]["previous_total"]["value_naira"]
        billing['present_month_kwh'] = billing_data["totals"]["present_total"]["usage_kwh"]
        billing['present_month_naira'] = billing_data["totals"]["present_total"]["value_naira"]

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : billing,
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status'  : False,
            'message' : "GET only allowed",
            'error' : "",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_base_line(request, device_id, end_date):

    if request.method == "GET":
        end_date = time_helpers.convert_date(end_date)

        device = Device.objects.get(id=device_id)
        baseline = device.get_base_line(end_date)

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : baseline,
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status'  : False,
            'message' : "GET only allowed",
            'error' : "",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_branch_blended_cost(request, branch_id, start_date, end_date):

    if request.method == "GET":

        end_date = time_helpers.convert_date(end_date)
        start_date = time_helpers.convert_date(start_date)

        branch = Branch.objects.get(id=branch_id)

        blended_cost = branch.get_branch_blended_cost(start_date, end_date)

        data = {
            'status'  : True,
            'message' : "Successful",
            'data' : blended_cost,
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status'  : False,
            'message' : "GET only allowed",
            'error' : "",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST', 'GET'])
def set_client_devices_operating_hours(request, client_id):

    client = Client.objects.get(id=client_id)
    devices = Device.objects.filter(client=client)

    if request.method == "POST":

        start_hour = time_helpers.time_handler(request.data.get('start'))
        end_hour = time_helpers.time_handler(request.data.get('end'))

        for device in devices:
            device.operating_hours_start = start_hour
            device.operating_hours_end = end_hour
            device.save()
            print('((((((((((((( OPERATING HOURS START ))))))))))))))')
            print(device.operating_hours_start)
            print(device.operating_hours_end)
            print('((((((((((((( OPERATING HOURS END)))))))))))))')


        data = {
            'status'  : True,
            'message' : "Successful",
        }

        return Response(data, status=status.HTTP_200_OK)

    if request.method == "GET":

        list = []
        for device in devices:
            i = dict(
                name = device.name,
                start_hour = device.operating_hours_start,
                end_hour = device.operating_hours_end
            )
            list.append(i)

        data = {
            'message' : "Successful",
            'data' : list
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status'  : False,
            'message' : "Error! Method not allowed",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
def post_weekly_diesel_usage(request, branch_id):

    if request.method == "POST":
        branch = Branch.objects.get(id=branch_id)
        data = request.data

        for date, value in data.items():

            date = time_helpers.convert_to_date_object(date)

            # Check if an entry already exists for the start date of this branch
            try:
                existing_entry = FuelConsumption.objects.get(branch=branch, start_date=date)
            except FuelConsumption.DoesNotExist:
                existing_entry = 0
            if existing_entry :
                print('((((((((((((((( existing entry )))))))))))))))')
                print(existing_entry)
                return Response({'error': 'An entry already exists for this week'}, status=status.HTTP_400_BAD_REQUEST)

            # Create the new entry
            consumption = {
                'branch'     : branch.id,
                'start_date': date,
                'quantity': value,
                'end_date': date,
                'fuel_type': 'diesel',
            }
            serializer = FuelEntryCreateSerializer(data=consumption)

            if serializer.is_valid():
                serializer.save()
                try:
                    obj = DieselOverviewHistory.objects.get(branch=branch_id, start_date=date)
                    obj.delete()

                except DieselOverviewHistory.DoesNotExist:
                    pass

            else:
                print('((((((((((((((( ERROR )))))))))))))))')
                print(serializer.errors)
                message = 'Please fill all the fields, enter 0 if no diesel was entered for that day'

                return Response({'error': message}, status=status.HTTP_400_BAD_REQUEST)

        data = {
            'status'  : False,
            'message' : "Weekly entry created successfully",
        }

        return Response(data, status=status.HTTP_201_CREATED)

    else:
        data = {
            'status'  : False,
            'message' : "POST only allowed",
            'error' : "",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def reminder_mail_check(request, branch_id):

    if request.method == "POST":

        branch = Branch.objects.get(id=branch_id)

        dates = request.data.get('ej')
        token = request.data.get('token')
        decoded_token = time_helpers.check_jwt_date(token)

        if decoded_token is True:
            message2 = 'Link is still valid, allow'
        else:
            message2 = 'Link is no longer valid'

        entries = FuelConsumption.objects.filter(branch=branch, start_date__gte=dates[0], end_date__lte=dates[-1])

        index = 0
        for _ in entries:
            index += 1

        branch_info = branch.get_weekly_fuel_consumption_entries(dates)
        branch_info_bytes = str(branch_info).encode('utf-8')
        branch_info = base64.b64encode(branch_info_bytes).decode()

        if index == 7:
            boolean = False
            message = 'All entries filled for this week'
        else:
            boolean = True
            message = f'{7 - index} entries left to be filled for this week'

        data = {
            'status'  : boolean,
            'message' : message,
            'branch_info' : branch_info,
            'message2' : message2,
        }

        return Response(data, status=status.HTTP_200_OK)

    else:
        data = {
            'status'  : False,
            'message' : "POST only allowed",
            'error' : "",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def  mail_cost_tracker(request, cost_type):

    body = json.loads(request.body)

    token = body.get('token')
    entry = body.get('entry')

    decoded_date_info = time_helpers.check_jwt_date(token)

    if decoded_date_info is True:

        if request.method == "POST":

            cost_types = ['petrol', 'diesel', 'ipp', 'pre-paid', 'post-paid']

            if cost_type in cost_types:

                serializer = CostTrackerSerializer(data=entry)

                if serializer.is_valid():

                    cost = Cost.objects.create(**serializer.validated_data, cost_type = cost_type)


                    serializer  = CostTrackerSerializer(cost)

                    data = {
                        'status'  : True,
                        'message' : "Utility entry created successfully",
                        'data' : serializer.data,
                    }

                    return Response(data, status = status.HTTP_201_CREATED)

                else:
                    data = {
                        'status'  : False,
                        'message' : "Unsuccessful",
                        'error' : serializer.errors,
                    }

                    return Response(data, status = status.HTTP_400_BAD_REQUEST)


            else:
                    data = {
                        'status'  : False,
                        'message' : "Unsuccessful",
                        'error' : f"Invalid cost type in url. Must be {str(cost_types)}"
                    }

                    return Response(data, status = status.HTTP_400_BAD_REQUEST)

        else:
            data = {
                'status'  : False,
                'message' : "Method not allowed",
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    elif type(decoded_date_info) == dict:

        data = {
                'status'  : False,
                'message' : decoded_date_info['message'],
            }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

    else:
        data = {
                'status'  : False,
                'message' : "JWT expired",
            }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def monthly_fuel_entry(request, branch_id):

    branch = Branch.objects.get(id=branch_id)

    data = request.data
    data['branch'] = branch_id

    # Check if an entry already exists for the start date of this Branch
    try:

        existing_entry = FuelConsumption.objects.get(branch=branch, start_date=data['start_date'])

    except FuelConsumption.DoesNotExist:

        existing_entry = 0

    if existing_entry :

        return Response({'error': 'An entry already exists for this date'}, status=status.HTTP_400_BAD_REQUEST)

    serializer = FuelEntryCreateSerializer(data=data)

    try:
        obj = DieselOverviewHistory.objects.get(branch=branch_id, start_date=data['start_date'])
        obj.delete()
    except DieselOverviewHistory.DoesNotExist:
        pass

    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def device_deviation_check(request, id, date):

    device = Device.objects.get(id=id)

    value = device.deviation_time_kwh_total_time(date)
    value['wasted_time'] = round(value['wasted_time'].total_seconds() / 3600, 4)
    value['total_time'] = round(value['total_time'].total_seconds() / 3600, 4)

    data = {
            'date' : date,
            'branch' : device.branch.name,
            'device name' : device.name,
            'data' : value,
        }

    return Response(data, status=status.HTTP_200_OK)

@api_view(['GET'])
def dashboard_deviation_check(request, name, date):

    client = Client.objects.get(name=name)

    branches = Branch.objects.filter(client=client)

    datas = []

    for branch in branches:

        devices = Device.objects.filter(branch=branch)

        branch_device_values = []

        for device in devices:

            value = device.deviation_time_kwh_total_time(date)
            value['Device'] = device.name

            wasted_seconds = value['wasted_time'].total_seconds()
            hours = int(wasted_seconds // 3600)
            minutes = int((wasted_seconds % 3600) // 60)

            value['wasted_time'] = f"{hours} hours, {minutes} minutes"

            total_seconds = value['total_time'].total_seconds()
            total_hours = int(total_seconds // 3600)
            total_minutes = int((total_seconds % 3600) // 60)

            value['total_time'] = f"{total_hours} hours, {total_minutes} minutes"

            branch_device_values.append(value)

        data = {
            'branch' : branch.name,
            'branch_device_values' : branch_device_values,
        }

        datas.append(data)

    return Response(datas, status=status.HTTP_200_OK)

@api_view(['GET'])
def branch_energy_check(request, branch_id, start_date, end_date):

    if request.method == "GET":

        end_date = time_helpers.convert_date(end_date)
        start_date = time_helpers.convert_date(start_date)

        # branch = Branch.objects.get(id=branch_id)
        client = Client.objects.get(id=branch_id)

        devices = Device.objects.filter(client=client)
        print('((((((((((((((( devices )))))))))))))))')
        print(devices)

        data = []
        for device in devices:
            if device.is_load:
                print(device.type)
                total_kwh = device.get_total_kwh_for_period(start_date, end_date)
                data.append({device.name: total_kwh})

        response_data = {
            'status': True,
            'message': "Successful",
            'data': data,
        }

        return Response(response_data, status=status.HTTP_200_OK)

@api_view(['GET'])
def branch_device_energy_check(request, branch_id, start_date, end_date):

    if request.method == "GET":

        end_date = time_helpers.convert_date(end_date)
        start_date = time_helpers.convert_date(start_date)

        branch = Branch.objects.get(id=branch_id)

        daily_kwh = branch.get_periodic_device_usage(start_date, end_date, "daily")

        CR = {}
        ac2 = {}
        for n, date in enumerate(daily_kwh["dates"]):
            CR[f'{date}'] = daily_kwh["COLD ROOM"][n]
            ac2[f'{date}'] = daily_kwh["AC2"][n]

        only_AC2_and_coldroom_kwh = dict(
            cold_room=CR,
            AC2=ac2
        )

        response_data = {
            'status': True,
            'message': "Successful",
            'data': only_AC2_and_coldroom_kwh,
        }

        return Response(response_data, status=status.HTTP_200_OK)

@api_view(['POST'])
def toggle_non_posting_attention(request, device_id):
    try:
        # Retrieve the device by id
        device = Device.objects.get(device_id=device_id)
        device_name = device.name

        # Toggle the non_post_attention attribute
        device.non_post_attention = not device.non_post_attention

        # Save the updated device instance
        device.save()

        # Return the updated state in the response
        return Response({
            "device_id": device_id,
            "device_name": device_name,
            "non_post_attention": device.non_post_attention
        }, status=status.HTTP_200_OK)

    except Device.DoesNotExist:
        # Return an error if the device does not exist
        return Response({"error": "Device not found."}, status=status.HTTP_404_NOT_FOUND)

def get_device_usage_hours(device_id, start_date, end_date, frequency):

    device = Device.objects.get(id=device_id)

    energy_consumed = device.energy_consumption_data(start_date, end_date, frequency).get('usage')
    # uh = device.get_hours_used(start_date, end_date).get('hours')

    # Convert usage hours from decimal to hours and minutes
    # hours = int(uh)
    # minutes = int((uh - hours) * 60)
    # usage_hours_formatted = f"{hours}h : {minutes}m"

    device_data = {
        'date_start_time': start_date,
        'date_end_time': end_date,
        # 'usage_hours': usage_hours_formatted,
        'usage_hours': f"3h : 24m",
        'energy_consumed' : energy_consumed
    }

    return device_data

@api_view(['GET'])
def device_usage_details(request, device_id, month, start_time, end_time):
    import calendar

    # start_time = "08:00"
    # end_time = "17:00"
    frequency = "hourly"

    # Get the current year
    year = datetime.now().year

    # Get the number of days in the given month
    num_days = calendar.monthrange(year, int(month))[1]

    # Convert start_time and end_time into datetime format
    start_time_obj = datetime.strptime(start_time, "%H:%M").time()
    end_time_obj = datetime.strptime(end_time, "%H:%M").time()

    if request.method == "GET":

        try:
            device = Device.objects.get(id=device_id)

            device_data = []

            # Loop through each day in the month
            for day in range(1, num_days + 1):
                # Create start_date and end_date for each day
                date = datetime(year, int(month), day)

                date_start_time = datetime.combine(date, start_time_obj)
                date_end_time = datetime.combine(date, end_time_obj)

                # Get device data for each day
                day_device_data = get_device_usage_hours(device_id, date_start_time, date_end_time, frequency)

                # 'energy_consumption': device.energy_consumption_data(start_date, end_date, frequency),
                device_data.append(day_device_data)

        except Device.DoesNotExist:
            data = {
                'status': False,
                'message': "DeviceId might not exist",
                'authenticatedData': None
            }
            response = Response(data, status=status.HTTP_401_UNAUTHORIZED)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        data = {
            'authenticatedData': {
                'name': device.name,
                'data': device_data
            }
        }

        response = Response(data, status=status.HTTP_200_OK)
        response["Access-Control-Allow-Origin"] = "*"
        return response

    else:
        data = {
            'status'  : False,
            'message' : "Error! Method not allowed",
        }

        return Response(data, status=status.HTTP_400_BAD_REQUEST)

# class  GenerateReportView(APIView):
#     def get(self, request):
#         branch_id = request.query_params.get("branch_id")
#         # report_id = request.query_params.get("report_id")

#         if not branch_id:
#             return Response(
#                 {"error": "branch_id is required."},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
#         try:
#             branch = Branch.objects.get(id=branch_id)
#             # report = Report.objects.get(id=report_id)
#         except ObjectDoesNotExist:
#             return Response(
#                 {"error": "Branch not found."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             return Response(
#                 {"error": str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

#         try:
#             data = branch.get_monthly_report()
#             # data = report.data
#         except Exception as e:
#             return Response(
#                 {"error": str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )
#         return Response(data, status=status.HTTP_200_OK)

#     def post(self, request):
#         branch_id = request.data.get("branch_id")

#         # 2. Validate parameters
#         if not branch_id:
#             return Response({"error": "branch_id is required"}, status=status.HTTP_400_BAD_REQUEST)

#         try:
#             # 3. Generate the report
#             branch = Branch.objects.get(id=branch_id)
#             # report = branch.get_monthly_report(month, year)
#             # report_id = Report.objects.create_report(report, branch, month, year)
#             # emails = [branch.email]
#             emails = [branch.email, "<EMAIL>"]

#             # 4. Send data to frontend service
#             # FRONTEND_URL = settings.FRONTEND_REPORT_ENDPOINT
#             FRONTEND_URL = "https://report-template-five.vercel.app/report?branch_id={branch_id}"

#             payload = {
#                 "url": f"https://report-template-five.vercel.app/report?branch_id={branch_id}",
#                 "recipients": emails,
#             }

#             headers = {
#                 "Content-Type": "application/json"
#             }

#             response = requests.post(FRONTEND_URL, json=payload, headers=headers)

#             # 5. Check if the frontend successfully received the data
#             if response.status_code == 200:
#                 return Response({"message": "Data sent successfully"}, status=status.HTTP_200_OK)
#             else:
#                 return Response({"error": "Failed to send to frontend", "frontend_response": response.text}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#         except Exception as e:
#             return Response({"error": f"Server error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GenerateReportView(APIView):
    """
    Endpoint to generate (and cache) a monthly report for a given branch.
    
    GET  /api/v1/generate-report/?branch_id=...&month=...&year=...
      - branch_id required
      - month/year optional; if missing, defaults to last calendar month
      - First checks MonthlyReport cache; if found, returns it.
      - Otherwise calls Branch.get_monthly_report, caches it (for past months),
        and returns it.
    
    POST /api/v1/generate-report/
      - JSON body: { branch_id, month, year }
      - Same behavior as GET, plus fires a webhook to your front-end service.
    """

    def _parse_period(self, month, year):
        """
        Given optional `month` and `year` strings:
          - If both provided, parse into ints and build start/end-of-that-month datetimes.
          - Otherwise, ask time_helpers for the previous-month window.
        Returns: (month_int, year_int, start_datetime, end_datetime)
        """
        if month and year:
            m, y = int(month), int(year)
            # start at beginning of month
            start = datetime(y, m, 1)
            # find last day of that month
            _, last_day = calendar.monthrange(y, m)
            # end at 23:59:59 of that day
            end = datetime(y, m, last_day, 23, 59, 59)
        else:
            # fallback: previous full calendar month
            start, end, _, _ = time_helpers.get_previous_month_start_and_end_dates_from_today()
            m, y = start.month, start.year
        return m, y, start, end

    def get(self, request):
        # 1) pull parameters
        branch_id = request.query_params.get("branch_id")
        if not branch_id:
            return Response(
                {"error": "branch_id is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 2) load branch or 404
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            return Response(
                {"error": "Branch not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # 3) determine the target period
        try:
            m, y, start_date, end_date = self._parse_period(
                request.query_params.get("month"),
                request.query_params.get("year")
            )
        except Exception as e:
            return Response(
                {"error": f"Invalid month/year: {e}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 4) check for an existing cached report
        cached = MonthlyReport.objects.filter(
            branch=branch, year=y, month=m
        ).first()

        if cached:
            # cached.data is normally already a dict thanks to JSONField
            payload = cached.data

            # If for some reason it's still a JSON‐encoded string, try to parse it
            if isinstance(payload, str):
                try:
                    payload = json.loads(payload)
                except ValueError:
                    # Bad cached entry: delete it and fall through to regenerate
                    cached.delete()
                    payload = None

            # If we have valid payload, return it immediately
            if payload is not None:
                return Response(payload, status=status.HTTP_200_OK)
        # else, either no cache or it was corrupted → regenerate below
        try:
            report_data = branch.get_monthly_report(
                start_date=start_date,
                end_date=end_date
            )
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # 6) store it for future GETs (only when querying a past month)
        now = datetime.now()
        if (y, m) < (now.year, now.month):
            try:
                with transaction.atomic():
                    MonthlyReport.objects.create(
                        branch=branch,
                        year=y,
                        month=m,
                        data=report_data
                    )
            except IntegrityError:
                # If two requests race, ignore duplicate
                pass

        # 7) return the freshly generated report
        return Response(report_data, status=status.HTTP_200_OK)

    # def post(self, request):
    #     # Mirror GET logic but read branch_id/month/year from JSON body
    #     branch_id = request.data.get("branch_id")
    #     if not branch_id:
    #         return Response(
    #             {"error": "branch_id is required."},
    #             status=status.HTTP_400_BAD_REQUEST
    #         )

    #     try:
    #         branch = Branch.objects.get(id=branch_id)
    #     except Branch.DoesNotExist:
    #         return Response(
    #             {"error": "Branch not found."},
    #             status=status.HTTP_404_NOT_FOUND
    #         )

    #     # parse the requested period (or fallback)
    #     try:
    #         m, y, start_date, end_date = self._parse_period(
    #             request.data.get("month"),
    #             request.data.get("year")
    #         )
    #     except Exception:
    #         # fallback to previous month
    #         start_date, end_date, _, _ = time_helpers.get_previous_month_start_and_end_dates_from_today()
    #         m, y = start_date.month, start_date.year

    #     # fire frontend/email webhook (don’t block on failure)
    #     report_url = f"https://report-template-five.vercel.app/report?branch_id={branch_id}&month={m}&year={y}"
    #     payload = {
    #         "url": report_url,
    #         "recipients": [branch.email] if branch.email else []
    #     }
    #     try:
    #         requests.post(report_url, json=payload, timeout=10)
    #     except requests.RequestException:
    #         pass


    #     # check cache
    #     cached = MonthlyReport.objects.filter(
    #         branch=branch, year=y, month=m
    #     ).first()
    #     if cached:
    #         # cached.data is normally already a dict
    #         payload = cached.data

    #         # If for some reason it's still a JSON‐encoded string, try to parse it
    #         if isinstance(payload, str):
    #             try:
    #                 payload = json.loads(payload)
    #             except ValueError:
    #                 # Bad cached entry: delete it and fall through to regenerate
    #                 cached.delete()
    #                 payload = None

    #         # If we have valid payload, return it immediately
    #         if payload is not None:
    #             return Response(payload, status=status.HTTP_200_OK)
    #     # else, either no cache or it was corrupted → regenerate below
    #     else:
    #         try:
    #             data = branch.get_monthly_report(
    #                 start_date=start_date,
    #                 end_date=end_date
    #             )
    #         except Exception as e:
    #             return Response(
    #                 {"error": str(e)},
    #                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #             )

    #         # cache if it’s a past month
    #         now = datetime.now()
    #         if (y, m) < (now.year, now.month):
    #             try:
    #                 with transaction.atomic():
    #                     MonthlyReport.objects.create(
    #                         branch=branch, year=y, month=m, data=data
    #                     )
    #             except IntegrityError:
    #                 pass

    #     return Response(data, status=status.HTTP_200_OK)



class ReportConfigurationView(APIView):
    """API view to manage report generation configuration"""

    def get(self, request):
        """Get current configuration"""
        from main.models import ReportConfiguration, Client, Branch

        config = ReportConfiguration.objects.first()
        if not config:
            config = ReportConfiguration.objects.create()

        excluded_clients = list(config.excluded_clients.all().values('id', 'name'))
        excluded_branches = list(config.excluded_branches.all().values('id', 'name'))

        return Response({
            "enabled": config.enabled,
            "excluded_clients": excluded_clients,
            "excluded_branches": excluded_branches,
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """Update configuration"""
        from main.models import ReportConfiguration, Client, Branch

        enabled = request.data.get("enabled")
        excluded_client_ids = request.data.get("excluded_client_ids", [])
        excluded_branch_ids = request.data.get("excluded_branch_ids", [])

        config = ReportConfiguration.objects.first()
        if not config:
            config = ReportConfiguration.objects.create()

        if enabled is not None:
            config.enabled = enabled

        # Update excluded clients
        if excluded_client_ids is not None:
            config.excluded_clients.clear()
            for client_id in excluded_client_ids:
                try:
                    client = Client.objects.get(id=client_id)
                    config.excluded_clients.add(client)
                except Client.DoesNotExist:
                    pass

        # Update excluded branches
        if excluded_branch_ids is not None:
            config.excluded_branches.clear()
            for branch_id in excluded_branch_ids:
                try:
                    branch = Branch.objects.get(id=branch_id)
                    config.excluded_branches.add(branch)
                except Branch.DoesNotExist:
                    pass

        config.save()

        return Response({
            "message": "Configuration updated successfully",
            "enabled": config.enabled,
            "excluded_clients_count": config.excluded_clients.count(),
            "excluded_branches_count": config.excluded_branches.count(),
        }, status=status.HTTP_200_OK)

def fetch_aggregated_readings_data(password, device_id, start_date, end_date):
    """
    Example helper function that queries the database for device readings
    and aggregates them, returning a string of relevant data or None/empty
    if invalid.
    """
    # Validate password
    if str(settings.RAW_DATA_PASSWORD) != str(password):
        return None  # indicates invalid password

    # Convert date strings to datetime objects
    try:
        start_dt = time_helpers.convert_date(start_date).date()
        end_dt = time_helpers.convert_date(end_date).date()
    except Exception as e:
        return e

    # Query readings
    readings = Reading.objects.filter(device__device_id=device_id, post_date__range=[start_dt, end_dt])
    if not readings.exists():
        return ""  # indicates no data found

    # Example of aggregating data using helper functions
    time_usage = aggregation_helpers.aggregate_time_of_use_from_readings(readings.values())
    energy_usage = aggregation_helpers.aggregate_usage_from_readings(readings.values())
    office_hours_usage = aggregation_helpers.aggregate_time_of_use(readings.values())

    # Convert DataFrames to CSV strings
    office_hours_str = time_usage.to_csv(index=False, sep="\t")
    energy_usage_str = energy_usage.to_csv(index=False, sep="\t")
    time_usage_str = office_hours_usage.to_csv(index=False, sep="\t")

    # Combine into a single text block
    aggregated_text = (
        f"Office Hours:\n{office_hours_str}\n\n"
        f"Energy Usage:\n{energy_usage_str}\n\n"
        f"Time Usage:\n{time_usage_str}"
    )
    return aggregated_text

@csrf_exempt
def chatbot_view(request):
    """
    Stateless view that handles two scenarios:
    1. Initialization: When no query is provided, the view expects device details (device_id, password, start_date, end_date),
       retrieves the aggregated readings data, and returns it to the client.
    2. Chat: When a query is provided, the view expects that the client includes the aggregated device data (from the initialization)
       in the POST data. The view then uses that data along with the query to generate a response.
    """
    if request.method == 'GET':
        return JsonResponse({"message": "Please send a POST request with device details to initialize."})

    if request.method == 'POST':
        # If there's no 'query' parameter, treat this as an initialization request.
        if not request.POST.get('query'):
            device_id = request.POST.get('device_id')
            password = request.POST.get('password')
            start_date = request.POST.get('start_date')
            end_date = request.POST.get('end_date')

            if not all([device_id, password, start_date, end_date]):
                return JsonResponse({
                    "error": "Please provide device_id, password, start_date, and end_date."
                }, status=400)

            aggregated_data = fetch_aggregated_readings_data(password, device_id, start_date, end_date)
            if aggregated_data is None:
                return JsonResponse({"error": "Invalid password provided."}, status=401)
            if aggregated_data == "":
                return JsonResponse({"error": "No device data was found."}, status=404)

            # Return the aggregated data to the client (the client should store it locally)
            return JsonResponse({
                "message": f"Device ID: {device_id} data from: {start_date} to: {end_date}, retrieved successfully. You can now send queries.",
                "aggregated_data": aggregated_data
            })

        # Otherwise, we treat it as a chat query.
        user_query = request.POST.get('query')
        aggregated_data = request.POST.get('aggregated_data')

        if not user_query:
            return JsonResponse({"error": "User query is required."}, status=400)
        if not aggregated_data:
            return JsonResponse({"error": "Aggregated device data is missing. Please reinitialize with device details."}, status=400)

        system_prompt = """
            You are a professional AI assistant that helps users analyze IoT device electrical energy data.
            Provide concise and professional answers based solely on the device readings.
        """
        user_prompt = f"""
            User Query: {user_query}

            Aggregated Device Readings Data:
            {aggregated_data}

            Provide a simple, well-structured and insightful response.
        """
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            openai_response = openai.chat.completions.create(
                model="gpt-3.5-turbo",  # Change model as needed
                messages=messages,
                max_tokens=1024,
                temperature=0.7
            )
            bot_reply = openai_response.choices[0].message.content.strip()
        except Exception as e:
            return JsonResponse({"error": f"AI error: {str(e)}"}, status=500)

        return JsonResponse({"response": bot_reply})

    return JsonResponse({"error": "Invalid request method"}, status=405)


######################################################################################################################################
###                                             Dlete after testing with server
#######################################################################################################################################
import time
from django.http import JsonResponse, HttpResponseBadRequest
from django.shortcuts import get_object_or_404
from .models import Device

def test_time_of_use(request, device_id, start_date, end_date):
    """
    URL params:
      - device_id: PK of Device (integer)
      - start_date: YYYY-MM-DD
      - end_date:   YYYY-MM-DD

      GET /test-usage/5/2025-05-01/2025-05-31/
    """
    try:
        t0 = time.perf_counter()
        device = get_object_or_404(Device, pk=device_id)
        hours = device.get_hourly_time_of_use(start_date, end_date)
        t1 = time.perf_counter()
    except Exception as e:
        return HttpResponseBadRequest(f"Error: {e}")

    return JsonResponse({
        'device': device_id,
        'hours': hours,
        'duration_ms': (t1 - t0) * 1000,
    })



class DeviceFuelEfficiencyDebugView(APIView):
    """
    Endpoint: /api/main/device-fuel-efficiency-debug/
    Query params: device_id, start_date, end_date (YYYY-MM-DD)
    Only accessible to admin users.
    """

    def get(self, request):
        device_id = request.data.get('device_id')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not (device_id and start_date and end_date):
            return Response({'error': 'device_id, start_date, and end_date are required.'}, status=400)

        try:
            device = Device.objects.get(id=device_id)
        except Device.DoesNotExist:
            return Response({'error': 'Device not found.'}, status=404)

        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

        data = device.get_fuel_consumption_score_cards(start_date_obj, end_date_obj)
        
        return Response({
            "device_id": device_id,
            "device_name": device.name,
            "debug_data": data
        })