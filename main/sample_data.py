data= {
  "authenticatedData": {
    "name": "Sapio Utilities",
    "branches": [
      {
        "name": "Richmond Gate",
        "devices": [
          {
            "name": "Utility",
            "dashboard": {
              "total_kwh": {
                "unit": "kWh",
                "value": 1000
              },
              "min_demand": {
                "unit": "kW",
                "value": 200
              },
              "max_demand": {
                "unit": "kW",
                "value": 300
              },
              "avg_demand": {
                "unit": "kW",
                "value": 150
              },
              "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": 34.25
              },
              "cost_of_energy": {
                "unit": "Naira",
                "value": 4000
              },
              "today": {
                "value": 150,
                "unit": "kWh"
              },
              "yesterday": {
                "value": 3000,
                "unit": "kWh"
              }
            },
            "score_card": {
              "is_generator": False,
              "baseline_energy": {
                "unit": "kWh",
                "forecast": 1000,
                "used": 500
              },
              "peak_to_avg_power_ratio": {
                "unit": "kW",
                "peak": 1000,
                "avg": 500,
                "message": "Not so efficient, Higher is better",
                "message_color": "green"
              },
              "score_card_carbon_emissions": {
                "unit": "Tons",
                "estimated_value": 110,
                "actual_value": 50,
                "message": "Equivalent to 15 Acacia trees"
              },
              "generator_size_efficiency": False,
              "change_over_lags": False,
              "operating_time": {
                "chart": {
                  "dates": [
                    "Jan 1",
                    "Jan 2",
                    "Jan 3",
                    "Jan 4",
                    "Jan 5",
                    "Jan 6",
                    "Jan 7",
                    "Jan 8",
                    "Jan 9",
                    "Jan 10",
                    "Jan 11",
                    "Jan 12",
                    "Jan 13",
                    "Jan 14",
                    "Jan 15",
                    "Jan 16",
                    "Jan 17",
                    "Jan 18",
                    "Jan 19",
                    "Jan 10",
                    "Jan 21",
                    "Jan 22",
                    "Jan 23",
                    "Jan 24",
                    "Jan 25",
                    "Jan 26",
                    "Jan 27",
                    "Jan 28",
                    "Jan 29",
                    "Jan 30",
                    "Jan 31"
                  ],
                  "values": [
                    199,
                    78,
                    52,
                    140,
                    111,
                    44,
                    186,
                    122,
                    12,
                    158,
                    174,
                    51,
                    218,
                    88,
                    196,
                    154,
                    70,
                    47,
                    54,
                    189,
                    17,
                    217,
                    25,
                    164,
                    243,
                    20,
                    5,
                    39,
                    227,
                    168,
                    161
                  ]
                },
                "estimated_time_wasted": {
                  "unit": "hours",
                  "value": 12
                },
                "estimated_diesel_wasted": False,
                "estimated_cost": {
                  "unit": "Naira",
                  "value": 12000
                }
              },
              "fuel_consumption": False
            },
            "power_quality": {
              "dates": {
                "dates": [
                  "2020-12-14T20:14:57.329485",
                  "2020-12-14T20:27:57.329485",
                  "2020-12-14T20:40:57.329485",
                  "2020-12-14T20:53:57.329485",
                  "2020-12-14T21:05:57.329485",
                  "2020-12-14T21:19:57.329485",
                  "2020-12-14T21:33:57.329485",
                  "2020-12-14T21:48:57.329485",
                  "2020-12-14T22:01:57.329485",
                  "2020-12-14T22:15:57.329485",
                  "2020-12-14T22:29:57.329485"
                ],
                "units": ""
              },
              "voltage": {
                "l1": [240, 236, 226, 234, 221, 240, 239, 229, 223, 233, 238],
                "l2": [235, 226, 221, 230, 234, 224, 229, 224, 221, 229, 233],
                "l3": [234, 239, 237, 225, 237, 238, 231, 234, 223, 229, 239],
                "neutral": [7, 10, 16, 17, 5, 13, 16, 7, 19, 6, 9],
                "units": "volts"
              },
              "current": {
                "l1": [169, 178, 171, 155, 167, 174, 168, 159, 160, 158, 159],
                "l2": [152, 150, 167, 156, 176, 153, 165, 169, 167, 173, 167],
                "l3": [171, 161, 157, 161, 172, 176, 159, 177, 169, 157, 164],
                "neutral": [18, 5, 20, 18, 19, 17, 8, 5, 20, 7, 20],
                "units": "amps"
              },
              "active_power": {
                "l1": [57, 56, 69, 56, 55, 55, 63, 65, 58, 63, 63],
                "l2": [61, 68, 57, 64, 60, 69, 66, 67, 55, 62, 67],
                "l3": [57, 59, 66, 68, 60, 57, 56, 56, 58, 66, 58],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kW"
              },
              "reactive_power": {
                "l1": [53, 70, 88, 83, 75, 63, 85, 83, 62, 72, 73],
                "l2": [74, 69, 80, 88, 77, 65, 70, 65, 78, 61, 55],
                "l3": [71, 79, 69, 79, 67, 79, 56, 65, 85, 59, 77],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kVar"
              },
              "energy": {
                "l1": [353, 326, 320, 313, 350, 317, 331, 312, 362, 324, 321],
                "l2": [371, 341, 344, 374, 312, 317, 354, 334, 313, 324, 354],
                "l3": [343, 373, 353, 380, 306, 357, 341, 324, 363, 368, 321],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kWh"
              },
              "frequency": {
                "average": [67, 30, 44, 70, 22, 33, 69, 78, 40, 66, 45],
                "units": "hz"
              },
              "power_factor": {
                "l1_l2_l3": [
                  0.99,
                  0.97,
                  0.84,
                  0.97,
                  0.86,
                  0.99,
                  0.93,
                  0.82,
                  0.87,
                  0.96,
                  0.91
                ],
                "units": ""
              }
            },
            "last_reading": {
              "date": "2020-12-18T09:27:32.394646",
              "data": {
                "phase_measures": [
                  {
                    "name": "voltage",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "volts"
                  },
                  {
                    "name": "current",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "amps"
                  },
                  {
                    "name": "active_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf",
                    "l1": 0.9,
                    "l2": 0.89,
                    "l3": 0.89,
                    "unit": "pf"
                  }
                ],
                "totals": [
                  {
                    "name": "active_power",
                    "value": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "value": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "value": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "frequency",
                    "value": 231,
                    "unit": "hz"
                  },
                  {
                    "name": "pf",
                    "value": 0.8,
                    "unit": "pf"
                  },
                  {
                    "name": "neutral_current",
                    "value": 70,
                    "unit": "A"
                  }
                ],
                "harmonic_distortion": [
                  {
                    "name": "voltage_thd_ln",
                    "l1": 0.7,
                    "l2": 0.7,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_thd",
                    "l1": 18,
                    "l2": 15.1,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_tdd",
                    "l1": 36,
                    "l2": 3.6,
                    "l3": 1.6,
                    "unit": "%"
                  }
                ],
                "energy": [
                  {
                    "name": "active_energy",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "active_energy_export",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "reactive_energy",
                    "value": 70,
                    "unit": "kvarh"
                  },
                  {
                    "name": "apparent_energy",
                    "value": 70,
                    "unit": "kvah"
                  }
                ],
                "demands": [
                  {
                    "name": "max_amp",
                    "l1": 70,
                    "l2": 70,
                    "l3": 70,
                    "unit": "A"
                  }
                ],
                "total_demands": [
                  {
                    "name": "max_power_demand",
                    "value": 1270,
                    "unit": "A"
                  },
                  {
                    "name": "accumulated_power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "max_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "accumulated_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf_import_at_sliding_winow",
                    "value": 0.9,
                    "unit": "pf"
                  }
                ]
              }
            },
            "power_demand": {
              "dates": {
                "dates": [
                  "2020-12-26T13:53:19.525251",
                  "2020-12-26T14:06:19.525251",
                  "2020-12-26T14:21:19.525251",
                  "2020-12-26T14:35:19.525251",
                  "2020-12-26T14:47:19.525251",
                  "2020-12-26T15:01:19.525251",
                  "2020-12-26T15:14:19.525251",
                  "2020-12-26T15:26:19.525251",
                  "2020-12-26T15:41:19.525251",
                  "2020-12-26T15:54:19.525251",
                  "2020-12-26T16:06:19.525251",
                  "2020-12-26T16:18:19.525251",
                  "2020-12-26T16:30:19.525251",
                  "2020-12-26T16:42:19.525251",
                  "2020-12-26T16:57:19.525251",
                  "2020-12-26T17:10:19.525251",
                  "2020-12-26T17:22:19.525251",
                  "2020-12-26T17:34:19.525251",
                  "2020-12-26T17:48:19.525251",
                  "2020-12-26T18:03:19.525251",
                  "2020-12-26T18:16:19.525251",
                  "2020-12-26T18:31:19.525251",
                  "2020-12-26T18:46:19.525251",
                  "2020-12-26T19:00:19.525251",
                  "2020-12-26T19:14:19.525251",
                  "2020-12-26T19:28:19.525251",
                  "2020-12-26T19:43:19.525251",
                  "2020-12-26T19:55:19.525251",
                  "2020-12-26T20:10:19.525251",
                  "2020-12-26T20:22:19.525251"
                ],
                "units": ""
              },
              "power_demand_values": {
                "demand": [
                  63,
                  58,
                  64,
                  68,
                  70,
                  55,
                  65,
                  55,
                  66,
                  69,
                  63,
                  62,
                  63,
                  65,
                  63,
                  61,
                  58,
                  70,
                  65,
                  66,
                  67,
                  56,
                  55,
                  58,
                  56,
                  68,
                  68,
                  63,
                  63,
                  68
                ],
                "min": [
                  61,
                  61,
                  69,
                  70,
                  57,
                  60,
                  63,
                  66,
                  60,
                  58,
                  59,
                  60,
                  56,
                  65,
                  66,
                  65,
                  70,
                  57,
                  58,
                  62,
                  59,
                  70,
                  60,
                  66,
                  61,
                  69,
                  67,
                  61,
                  66,
                  57
                ],
                "max": [
                  56,
                  55,
                  60,
                  68,
                  57,
                  66,
                  65,
                  59,
                  61,
                  58,
                  60,
                  56,
                  66,
                  62,
                  56,
                  68,
                  65,
                  69,
                  58,
                  66,
                  55,
                  65,
                  67,
                  59,
                  63,
                  69,
                  70,
                  62,
                  61,
                  63
                ],
                "avg": [
                  56,
                  69,
                  69,
                  67,
                  69,
                  60,
                  70,
                  65,
                  55,
                  56,
                  62,
                  70,
                  58,
                  56,
                  65,
                  64,
                  70,
                  57,
                  65,
                  62,
                  59,
                  60,
                  62,
                  66,
                  58,
                  66,
                  65,
                  63,
                  68,
                  59
                ],
                "units": "kW"
              }
            },
            "time_of_use": {
              "dates": [
                "2020-12-26T13:49:08.705702",
                "2020-12-26T14:04:08.705702",
                "2020-12-26T14:16:08.705702",
                "2020-12-26T14:31:08.705702",
                "2020-12-26T14:43:08.705702",
                "2020-12-26T14:56:08.705702",
                "2020-12-26T15:11:08.705702",
                "2020-12-26T15:25:08.705702",
                "2020-12-26T15:39:08.705702",
                "2020-12-26T15:51:08.705702",
                "2020-12-26T16:03:08.705702",
                "2020-12-26T16:15:08.705702",
                "2020-12-26T16:30:08.705702",
                "2020-12-26T16:42:08.705702",
                "2020-12-26T16:56:08.705702",
                "2020-12-26T17:08:08.705702",
                "2020-12-26T17:23:08.705702",
                "2020-12-26T17:37:08.705702",
                "2020-12-26T17:49:08.705702",
                "2020-12-26T18:03:08.705702",
                "2020-12-26T18:16:08.705702",
                "2020-12-26T18:31:08.705702",
                "2020-12-26T18:45:08.705702",
                "2020-12-26T19:00:08.705702",
                "2020-12-26T19:14:08.705702",
                "2020-12-26T19:27:08.705702",
                "2020-12-26T19:39:08.705702",
                "2020-12-26T19:52:08.705702",
                "2020-12-26T20:04:08.705702",
                "2020-12-26T20:18:08.705702"
              ],
              "values": [
                10,
                7,
                10,
                8,
                10,
                5,
                5,
                9,
                5,
                10,
                9,
                8,
                8,
                9,
                8,
                9,
                9,
                8,
                7,
                9,
                6,
                5,
                6,
                7,
                6,
                5,
                8,
                10,
                6,
                10
              ]
            },
            "energy_consumption": {
              "previous": 1387,
              "current": 2443,
              "usage": 1056,
              "dates": {
                "dates": [
                  "2020-12-27T14:42:33.379395",
                  "2020-12-27T14:54:33.379395",
                  "2020-12-27T15:09:33.379395",
                  "2020-12-27T15:22:33.379395",
                  "2020-12-27T15:36:33.379395",
                  "2020-12-27T15:48:33.379395",
                  "2020-12-27T16:01:33.379395",
                  "2020-12-27T16:15:33.379395",
                  "2020-12-27T16:28:33.379395",
                  "2020-12-27T16:40:33.379395",
                  "2020-12-27T16:55:33.379395",
                  "2020-12-27T17:09:33.379395",
                  "2020-12-27T17:24:33.379395",
                  "2020-12-27T17:39:33.379395",
                  "2020-12-27T17:53:33.379395",
                  "2020-12-27T18:05:33.379395",
                  "2020-12-27T18:20:33.379395",
                  "2020-12-27T18:35:33.379395",
                  "2020-12-27T18:50:33.379395",
                  "2020-12-27T19:04:33.379395",
                  "2020-12-27T19:19:33.379395",
                  "2020-12-27T19:32:33.379395",
                  "2020-12-27T19:45:33.379395",
                  "2020-12-27T19:57:33.379395",
                  "2020-12-27T20:09:33.379395",
                  "2020-12-27T20:23:33.379395",
                  "2020-12-27T20:38:33.379395",
                  "2020-12-27T20:52:33.379395",
                  "2020-12-27T21:04:33.379395",
                  "2020-12-27T21:17:33.379395"
                ],
                "units": ""
              },
              "energy_consumption_values": {
                "value": [
                  370,
                  356,
                  334,
                  363,
                  374,
                  341,
                  326,
                  322,
                  347,
                  313,
                  314,
                  310,
                  324,
                  330,
                  333,
                  326,
                  321,
                  328,
                  334,
                  343,
                  340,
                  357,
                  344,
                  343,
                  375,
                  378,
                  375,
                  374,
                  300,
                  334
                ],
                "units": "kWh"
              }
            },
            "billing": {
              "totals": {
                "previous_total": {
                  "usage_kwh": 24000,
                  "value_naira": 5000000
                },
                "present_total": {
                  "usage_kwh": 2000,
                  "value_naira": 5000000
                }
              },
              "consumption_kwh": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  358,
                  350,
                  341,
                  367,
                  321,
                  306,
                  347,
                  376,
                  352,
                  343,
                  321,
                  306,
                  376,
                  340,
                  362,
                  365,
                  379,
                  310,
                  361,
                  303,
                  315,
                  318,
                  348,
                  359,
                  304,
                  308,
                  374,
                  304,
                  327,
                  349
                ]
              },
              "consumption_naira": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  324,
                  347,
                  357,
                  334,
                  324,
                  337,
                  358,
                  324,
                  345,
                  354,
                  329,
                  329,
                  305,
                  318,
                  322,
                  322,
                  364,
                  365,
                  315,
                  366,
                  308,
                  372,
                  341,
                  377,
                  370,
                  335,
                  359,
                  354,
                  306,
                  358
                ]
              }
            },
            "id": 4
          },
          {
            "name": "Gen1",
            "dashboard": {
              "total_kwh": {
                "unit": "kWh",
                "value": 1000
              },
              "min_demand": {
                "unit": "kW",
                "value": 200
              },
              "max_demand": {
                "unit": "kW",
                "value": 300
              },
              "avg_demand": {
                "unit": "kW",
                "value": 150
              },
              "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": 34.25
              },
              "cost_of_energy": {
                "unit": "Naira",
                "value": 4000
              },
              "today": {
                "value": 150,
                "unit": "kWh"
              },
              "yesterday": {
                "value": 3000,
                "unit": "kWh"
              }
            },
            "score_card": {
              "is_generator": True,
              "baseline_energy": {
                "unit": "kWh",
                "forecast": 1000,
                "used": 500
              },
              "peak_to_avg_power_ratio": {
                "unit": "kW",
                "peak": 1000,
                "avg": 500,
                "message": "Not so efficient, Higher is better",
                "message_color": "green"
              },
              "score_card_carbon_emissions": {
                "unit": "Tons",
                "estimated_value": 110,
                "actual_value": 50,
                "message": "Equivalent to 15 Acacia trees"
              },
              "generator_size_efficiency": {
                "size": "100kva",
                "usage": 30,
                "unit": "%"
              },
              "change_over_lags": {
                "data": [
                  {
                    "date": "Jan 1 2020",
                    "lag_duration": 15,
                    "diesel_cost": 20,
                    "diesel_value": 4800,
                    "id": 1
                  },
                  {
                    "date": "Jan 2 2020",
                    "lag_duration": 14,
                    "diesel_cost": 10,
                    "diesel_value": 2400,
                    "id": 2
                  },
                  {
                    "date": "Jan 3 2020",
                    "lag_duration": 60,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 3
                  },
                  {
                    "date": "Jan 4 2020",
                    "lag_duration": 120,
                    "diesel_cost": 30,
                    "diesel_value": 7200,
                    "id": 4
                  },
                  {
                    "date": "Jan 5 2020",
                    "lag_duration": 90,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 5
                  }
                ],
                "units": {
                  "lag_duration": "Minutes",
                  "diesel_cost": "Litres"
                }
              },
              "operating_time": {
                "chart": {
                  "dates": [
                    "Jan 1",
                    "Jan 2",
                    "Jan 3",
                    "Jan 4",
                    "Jan 5",
                    "Jan 6",
                    "Jan 7",
                    "Jan 8",
                    "Jan 9",
                    "Jan 10",
                    "Jan 11",
                    "Jan 12",
                    "Jan 13",
                    "Jan 14",
                    "Jan 15",
                    "Jan 16",
                    "Jan 17",
                    "Jan 18",
                    "Jan 19",
                    "Jan 10",
                    "Jan 21",
                    "Jan 22",
                    "Jan 23",
                    "Jan 24",
                    "Jan 25",
                    "Jan 26",
                    "Jan 27",
                    "Jan 28",
                    "Jan 29",
                    "Jan 30",
                    "Jan 31"
                  ],
                  "values": [
                    199,
                    78,
                    52,
                    140,
                    111,
                    44,
                    186,
                    122,
                    12,
                    158,
                    174,
                    51,
                    218,
                    88,
                    196,
                    154,
                    70,
                    47,
                    54,
                    189,
                    17,
                    217,
                    25,
                    164,
                    243,
                    20,
                    5,
                    39,
                    227,
                    168,
                    161
                  ]
                },
                "estimated_time_wasted": {
                  "unit": "hours",
                  "value": 12
                },
                "estimated_diesel_wasted": {
                  "unit": "Litres",
                  "value": 300
                },
                "estimated_cost": {
                  "unit": "Naira",
                  "value": 12000
                }
              },
              "fuel_consumption": {
                "name": "main_generator",
                "size": "100kva",
                "diesel_usage": 30,
                "time_used": "12",
                "hours_to_maintenance": {
                  "hours": 23,
                  "unit": "hours"
                }
              }
            },
            "power_quality": {
              "dates": {
                "dates": [
                  "2020-12-14T20:15:57.329485",
                  "2020-12-14T20:30:57.329485",
                  "2020-12-14T20:42:57.329485",
                  "2020-12-14T20:57:57.329485",
                  "2020-12-14T21:11:57.329485",
                  "2020-12-14T21:26:57.329485",
                  "2020-12-14T21:39:57.329485",
                  "2020-12-14T21:51:57.329485",
                  "2020-12-14T22:05:57.329485",
                  "2020-12-14T22:20:57.329485",
                  "2020-12-14T22:35:57.329485"
                ],
                "units": ""
              },
              "voltage": {
                "l1": [225, 227, 239, 223, 222, 231, 239, 231, 234, 227, 229],
                "l2": [230, 221, 238, 229, 227, 220, 233, 228, 221, 222, 226],
                "l3": [228, 225, 224, 226, 237, 226, 232, 226, 233, 234, 231],
                "neutral": [10, 8, 8, 5, 11, 16, 6, 20, 8, 13, 20],
                "units": "volts"
              },
              "current": {
                "l1": [162, 159, 174, 175, 166, 171, 171, 176, 173, 172, 150],
                "l2": [153, 176, 166, 179, 173, 169, 173, 155, 152, 160, 171],
                "l3": [156, 163, 157, 151, 171, 165, 163, 172, 178, 150, 167],
                "neutral": [5, 15, 10, 12, 20, 12, 14, 11, 9, 7, 14],
                "units": "amps"
              },
              "active_power": {
                "l1": [67, 70, 58, 68, 63, 70, 70, 63, 65, 56, 65],
                "l2": [60, 69, 59, 66, 57, 61, 69, 57, 70, 68, 69],
                "l3": [62, 70, 57, 57, 58, 63, 65, 58, 68, 61, 67],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kW"
              },
              "reactive_power": {
                "l1": [78, 59, 83, 65, 52, 68, 83, 57, 59, 50, 50],
                "l2": [86, 70, 55, 52, 74, 65, 51, 84, 85, 90, 64],
                "l3": [53, 52, 82, 55, 86, 72, 59, 78, 51, 69, 69],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kVar"
              },
              "energy": {
                "l1": [365, 325, 377, 371, 312, 355, 324, 322, 354, 309, 331],
                "l2": [323, 315, 333, 323, 317, 301, 345, 380, 312, 314, 316],
                "l3": [323, 376, 348, 329, 352, 319, 356, 304, 312, 312, 316],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kWh"
              },
              "frequency": {
                "average": [67, 30, 44, 70, 22, 33, 69, 78, 40, 66, 45],
                "units": "hz"
              },
              "power_factor": {
                "l1_l2_l3": [
                  0.82,
                  0.92,
                  0.87,
                  0.9,
                  0.87,
                  0.98,
                  0.9,
                  0.85,
                  0.89,
                  0.93,
                  0.99
                ],
                "units": ""
              }
            },
            "last_reading": {
              "date": "2020-12-18T09:27:32.394646",
              "data": {
                "phase_measures": [
                  {
                    "name": "voltage",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "volts"
                  },
                  {
                    "name": "current",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "amps"
                  },
                  {
                    "name": "active_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf",
                    "l1": 0.9,
                    "l2": 0.89,
                    "l3": 0.89,
                    "unit": "pf"
                  }
                ],
                "totals": [
                  {
                    "name": "active_power",
                    "value": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "value": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "value": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "frequency",
                    "value": 231,
                    "unit": "hz"
                  },
                  {
                    "name": "pf",
                    "value": 0.8,
                    "unit": "pf"
                  },
                  {
                    "name": "neutral_current",
                    "value": 70,
                    "unit": "A"
                  }
                ],
                "harmonic_distortion": [
                  {
                    "name": "voltage_thd_ln",
                    "l1": 0.7,
                    "l2": 0.7,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_thd",
                    "l1": 18,
                    "l2": 15.1,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_tdd",
                    "l1": 36,
                    "l2": 3.6,
                    "l3": 1.6,
                    "unit": "%"
                  }
                ],
                "energy": [
                  {
                    "name": "active_energy",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "active_energy_export",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "reactive_energy",
                    "value": 70,
                    "unit": "kvarh"
                  },
                  {
                    "name": "apparent_energy",
                    "value": 70,
                    "unit": "kvah"
                  }
                ],
                "demands": [
                  {
                    "name": "max_amp",
                    "l1": 70,
                    "l2": 70,
                    "l3": 70,
                    "unit": "A"
                  }
                ],
                "total_demands": [
                  {
                    "name": "max_power_demand",
                    "value": 1270,
                    "unit": "A"
                  },
                  {
                    "name": "accumulated_power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "max_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "accumulated_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf_import_at_sliding_winow",
                    "value": 0.9,
                    "unit": "pf"
                  }
                ]
              }
            },
            "power_demand": {
              "dates": {
                "dates": [
                  "2020-12-26T13:53:19.525251",
                  "2020-12-26T14:06:19.525251",
                  "2020-12-26T14:21:19.525251",
                  "2020-12-26T14:35:19.525251",
                  "2020-12-26T14:47:19.525251",
                  "2020-12-26T15:01:19.525251",
                  "2020-12-26T15:14:19.525251",
                  "2020-12-26T15:26:19.525251",
                  "2020-12-26T15:41:19.525251",
                  "2020-12-26T15:54:19.525251",
                  "2020-12-26T16:06:19.525251",
                  "2020-12-26T16:18:19.525251",
                  "2020-12-26T16:30:19.525251",
                  "2020-12-26T16:42:19.525251",
                  "2020-12-26T16:57:19.525251",
                  "2020-12-26T17:10:19.525251",
                  "2020-12-26T17:22:19.525251",
                  "2020-12-26T17:34:19.525251",
                  "2020-12-26T17:48:19.525251",
                  "2020-12-26T18:03:19.525251",
                  "2020-12-26T18:16:19.525251",
                  "2020-12-26T18:31:19.525251",
                  "2020-12-26T18:46:19.525251",
                  "2020-12-26T19:00:19.525251",
                  "2020-12-26T19:14:19.525251",
                  "2020-12-26T19:28:19.525251",
                  "2020-12-26T19:43:19.525251",
                  "2020-12-26T19:55:19.525251",
                  "2020-12-26T20:10:19.525251",
                  "2020-12-26T20:22:19.525251"
                ],
                "units": ""
              },
              "power_demand_values": {
                "demand": [
                  57,
                  62,
                  63,
                  64,
                  69,
                  55,
                  57,
                  60,
                  58,
                  59,
                  63,
                  57,
                  69,
                  67,
                  70,
                  63,
                  63,
                  59,
                  58,
                  61,
                  66,
                  64,
                  62,
                  57,
                  64,
                  59,
                  61,
                  70,
                  64,
                  64
                ],
                "min": [
                  66,
                  60,
                  66,
                  56,
                  55,
                  70,
                  58,
                  56,
                  57,
                  69,
                  64,
                  58,
                  55,
                  64,
                  68,
                  56,
                  61,
                  65,
                  55,
                  68,
                  55,
                  69,
                  57,
                  55,
                  65,
                  63,
                  58,
                  59,
                  55,
                  60
                ],
                "max": [
                  63,
                  66,
                  67,
                  66,
                  69,
                  63,
                  61,
                  60,
                  57,
                  59,
                  64,
                  68,
                  65,
                  58,
                  57,
                  65,
                  70,
                  56,
                  64,
                  65,
                  69,
                  67,
                  63,
                  58,
                  58,
                  69,
                  64,
                  65,
                  56,
                  59
                ],
                "avg": [
                  60,
                  70,
                  62,
                  57,
                  64,
                  63,
                  70,
                  61,
                  67,
                  62,
                  57,
                  61,
                  67,
                  57,
                  66,
                  58,
                  62,
                  58,
                  60,
                  61,
                  68,
                  67,
                  57,
                  56,
                  60,
                  63,
                  62,
                  70,
                  57,
                  56
                ],
                "units": "kW"
              }
            },
            "time_of_use": {
              "dates": [
                "2020-12-26T13:49:08.705702",
                "2020-12-26T14:01:08.705702",
                "2020-12-26T14:16:08.705702",
                "2020-12-26T14:29:08.705702",
                "2020-12-26T14:43:08.705702",
                "2020-12-26T14:56:08.705702",
                "2020-12-26T15:10:08.705702",
                "2020-12-26T15:24:08.705702",
                "2020-12-26T15:36:08.705702",
                "2020-12-26T15:51:08.705702",
                "2020-12-26T16:05:08.705702",
                "2020-12-26T16:18:08.705702",
                "2020-12-26T16:33:08.705702",
                "2020-12-26T16:46:08.705702",
                "2020-12-26T17:00:08.705702",
                "2020-12-26T17:12:08.705702",
                "2020-12-26T17:26:08.705702",
                "2020-12-26T17:40:08.705702",
                "2020-12-26T17:53:08.705702",
                "2020-12-26T18:06:08.705702",
                "2020-12-26T18:20:08.705702",
                "2020-12-26T18:33:08.705702",
                "2020-12-26T18:45:08.705702",
                "2020-12-26T18:57:08.705702",
                "2020-12-26T19:12:08.705702",
                "2020-12-26T19:27:08.705702",
                "2020-12-26T19:41:08.705702",
                "2020-12-26T19:54:08.705702",
                "2020-12-26T20:06:08.705702",
                "2020-12-26T20:19:08.705702"
              ],
              "values": [
                7,
                7,
                5,
                8,
                7,
                8,
                9,
                7,
                7,
                8,
                5,
                7,
                8,
                10,
                6,
                7,
                7,
                9,
                9,
                8,
                7,
                9,
                7,
                8,
                6,
                6,
                8,
                9,
                8,
                10
              ]
            },
            "energy_consumption": {
              "previous": 1318,
              "current": 1933,
              "usage": 615,
              "dates": {
                "dates": [
                  "2020-12-27T14:42:33.379395",
                  "2020-12-27T14:54:33.379395",
                  "2020-12-27T15:09:33.379395",
                  "2020-12-27T15:22:33.379395",
                  "2020-12-27T15:36:33.379395",
                  "2020-12-27T15:48:33.379395",
                  "2020-12-27T16:01:33.379395",
                  "2020-12-27T16:15:33.379395",
                  "2020-12-27T16:28:33.379395",
                  "2020-12-27T16:40:33.379395",
                  "2020-12-27T16:55:33.379395",
                  "2020-12-27T17:09:33.379395",
                  "2020-12-27T17:24:33.379395",
                  "2020-12-27T17:39:33.379395",
                  "2020-12-27T17:53:33.379395",
                  "2020-12-27T18:05:33.379395",
                  "2020-12-27T18:20:33.379395",
                  "2020-12-27T18:35:33.379395",
                  "2020-12-27T18:50:33.379395",
                  "2020-12-27T19:04:33.379395",
                  "2020-12-27T19:19:33.379395",
                  "2020-12-27T19:32:33.379395",
                  "2020-12-27T19:45:33.379395",
                  "2020-12-27T19:57:33.379395",
                  "2020-12-27T20:09:33.379395",
                  "2020-12-27T20:23:33.379395",
                  "2020-12-27T20:38:33.379395",
                  "2020-12-27T20:52:33.379395",
                  "2020-12-27T21:04:33.379395",
                  "2020-12-27T21:17:33.379395"
                ],
                "units": ""
              },
              "energy_consumption_values": {
                "value": [
                  358,
                  376,
                  374,
                  375,
                  336,
                  318,
                  323,
                  325,
                  315,
                  362,
                  346,
                  343,
                  308,
                  379,
                  362,
                  342,
                  309,
                  334,
                  348,
                  324,
                  317,
                  365,
                  322,
                  300,
                  379,
                  378,
                  310,
                  349,
                  332,
                  375
                ],
                "units": "kWh"
              }
            },
            "billing": {
              "totals": {
                "previous_total": {
                  "usage_kwh": 24000,
                  "value_naira": 5000000
                },
                "present_total": {
                  "usage_kwh": 2000,
                  "value_naira": 5000000
                }
              },
              "consumption_kwh": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  309,
                  304,
                  308,
                  350,
                  309,
                  359,
                  340,
                  323,
                  313,
                  367,
                  346,
                  330,
                  330,
                  366,
                  358,
                  341,
                  302,
                  352,
                  320,
                  362,
                  369,
                  353,
                  379,
                  319,
                  351,
                  380,
                  371,
                  300,
                  377,
                  359
                ]
              },
              "consumption_naira": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  375,
                  344,
                  320,
                  322,
                  321,
                  324,
                  359,
                  303,
                  336,
                  301,
                  314,
                  302,
                  372,
                  305,
                  358,
                  375,
                  302,
                  353,
                  375,
                  318,
                  376,
                  335,
                  379,
                  378,
                  317,
                  376,
                  362,
                  380,
                  312,
                  311
                ]
              }
            },
            "id": 2
          },
          {
            "name": "Gen2",
            "dashboard": {
              "total_kwh": {
                "unit": "kWh",
                "value": 1000
              },
              "min_demand": {
                "unit": "kW",
                "value": 200
              },
              "max_demand": {
                "unit": "kW",
                "value": 300
              },
              "avg_demand": {
                "unit": "kW",
                "value": 150
              },
              "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": 34.25
              },
              "cost_of_energy": {
                "unit": "Naira",
                "value": 4000
              },
              "today": {
                "value": 150,
                "unit": "kWh"
              },
              "yesterday": {
                "value": 3000,
                "unit": "kWh"
              }
            },
            "score_card": {
              "is_generator": True,
              "baseline_energy": {
                "unit": "kWh",
                "forecast": 1000,
                "used": 500
              },
              "peak_to_avg_power_ratio": {
                "unit": "kW",
                "peak": 1000,
                "avg": 500,
                "message": "Not so efficient, Higher is better",
                "message_color": "green"
              },
              "score_card_carbon_emissions": {
                "unit": "Tons",
                "estimated_value": 110,
                "actual_value": 50,
                "message": "Equivalent to 15 Acacia trees"
              },
              "generator_size_efficiency": {
                "size": "50kva",
                "usage": 20,
                "unit": "%"
              },
              "change_over_lags": {
                "data": [
                  {
                    "date": "Jan 1 2020",
                    "lag_duration": 15,
                    "diesel_cost": 20,
                    "diesel_value": 4800,
                    "id": 1
                  },
                  {
                    "date": "Jan 2 2020",
                    "lag_duration": 14,
                    "diesel_cost": 10,
                    "diesel_value": 2400,
                    "id": 2
                  },
                  {
                    "date": "Jan 3 2020",
                    "lag_duration": 60,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 3
                  },
                  {
                    "date": "Jan 4 2020",
                    "lag_duration": 120,
                    "diesel_cost": 30,
                    "diesel_value": 7200,
                    "id": 4
                  },
                  {
                    "date": "Jan 5 2020",
                    "lag_duration": 90,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 5
                  }
                ],
                "units": {
                  "lag_duration": "Minutes",
                  "diesel_cost": "Litres"
                }
              },
              "operating_time": {
                "chart": {
                  "dates": [
                    "Jan 1",
                    "Jan 2",
                    "Jan 3",
                    "Jan 4",
                    "Jan 5",
                    "Jan 6",
                    "Jan 7",
                    "Jan 8",
                    "Jan 9",
                    "Jan 10",
                    "Jan 11",
                    "Jan 12",
                    "Jan 13",
                    "Jan 14",
                    "Jan 15",
                    "Jan 16",
                    "Jan 17",
                    "Jan 18",
                    "Jan 19",
                    "Jan 10",
                    "Jan 21",
                    "Jan 22",
                    "Jan 23",
                    "Jan 24",
                    "Jan 25",
                    "Jan 26",
                    "Jan 27",
                    "Jan 28",
                    "Jan 29",
                    "Jan 30",
                    "Jan 31"
                  ],
                  "values": [
                    199,
                    78,
                    52,
                    140,
                    111,
                    44,
                    186,
                    122,
                    12,
                    158,
                    174,
                    51,
                    218,
                    88,
                    196,
                    154,
                    70,
                    47,
                    54,
                    189,
                    17,
                    217,
                    25,
                    164,
                    243,
                    20,
                    5,
                    39,
                    227,
                    168,
                    161
                  ]
                },
                "estimated_time_wasted": {
                  "unit": "hours",
                  "value": 12
                },
                "estimated_diesel_wasted": {
                  "unit": "Litres",
                  "value": 300
                },
                "estimated_cost": {
                  "unit": "Naira",
                  "value": 12000
                }
              },
              "fuel_consumption": {
                "name": "secondary generator",
                "size": "50kva",
                "diesel_usage": 20,
                "time_used": "13",
                "hours_to_maintenance": {
                  "hours": 23,
                  "unit": "hours"
                }
              }
            },
            "power_quality": {
              "dates": {
                "dates": [
                  "2020-12-14T20:14:57.329485",
                  "2020-12-14T20:27:57.329485",
                  "2020-12-14T20:40:57.329485",
                  "2020-12-14T20:53:57.329485",
                  "2020-12-14T21:08:57.329485",
                  "2020-12-14T21:23:57.329485",
                  "2020-12-14T21:38:57.329485",
                  "2020-12-14T21:50:57.329485",
                  "2020-12-14T22:04:57.329485",
                  "2020-12-14T22:16:57.329485",
                  "2020-12-14T22:31:57.329485"
                ],
                "units": ""
              },
              "voltage": {
                "l1": [237, 225, 237, 220, 238, 226, 229, 238, 227, 227, 224],
                "l2": [239, 236, 230, 227, 232, 232, 229, 238, 233, 232, 228],
                "l3": [220, 238, 223, 221, 234, 226, 230, 234, 239, 240, 228],
                "neutral": [19, 11, 11, 10, 16, 5, 20, 14, 9, 14, 16],
                "units": "volts"
              },
              "current": {
                "l1": [170, 161, 167, 151, 158, 165, 164, 160, 173, 151, 153],
                "l2": [159, 176, 162, 167, 164, 172, 163, 172, 165, 156, 157],
                "l3": [169, 174, 180, 156, 152, 159, 156, 159, 179, 168, 158],
                "neutral": [15, 20, 16, 13, 5, 10, 19, 9, 12, 18, 20],
                "units": "amps"
              },
              "active_power": {
                "l1": [56, 70, 57, 66, 55, 65, 60, 67, 67, 62, 58],
                "l2": [65, 63, 60, 58, 57, 69, 58, 57, 59, 68, 63],
                "l3": [62, 63, 65, 63, 57, 65, 64, 55, 62, 57, 62],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kW"
              },
              "reactive_power": {
                "l1": [63, 86, 90, 50, 62, 88, 65, 53, 55, 72, 50],
                "l2": [55, 88, 77, 90, 85, 86, 70, 78, 57, 81, 80],
                "l3": [60, 70, 51, 54, 71, 64, 64, 68, 75, 52, 78],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kVar"
              },
              "energy": {
                "l1": [307, 349, 373, 321, 335, 315, 314, 339, 323, 372, 321],
                "l2": [336, 314, 358, 311, 339, 322, 335, 342, 304, 370, 314],
                "l3": [360, 317, 344, 318, 312, 322, 307, 307, 315, 345, 349],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kWh"
              },
              "frequency": {
                "average": [67, 30, 44, 70, 22, 33, 69, 78, 40, 66, 45],
                "units": "hz"
              },
              "power_factor": {
                "l1_l2_l3": [
                  0.81,
                  0.87,
                  0.96,
                  0.81,
                  0.9,
                  0.92,
                  0.9,
                  0.82,
                  0.86,
                  0.85,
                  0.95
                ],
                "units": ""
              }
            },
            "last_reading": {
              "date": "2020-12-18T09:27:32.394646",
              "data": {
                "phase_measures": [
                  {
                    "name": "voltage",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "volts"
                  },
                  {
                    "name": "current",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "amps"
                  },
                  {
                    "name": "active_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf",
                    "l1": 0.9,
                    "l2": 0.89,
                    "l3": 0.89,
                    "unit": "pf"
                  }
                ],
                "totals": [
                  {
                    "name": "active_power",
                    "value": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "value": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "value": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "frequency",
                    "value": 231,
                    "unit": "hz"
                  },
                  {
                    "name": "pf",
                    "value": 0.8,
                    "unit": "pf"
                  },
                  {
                    "name": "neutral_current",
                    "value": 70,
                    "unit": "A"
                  }
                ],
                "harmonic_distortion": [
                  {
                    "name": "voltage_thd_ln",
                    "l1": 0.7,
                    "l2": 0.7,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_thd",
                    "l1": 18,
                    "l2": 15.1,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_tdd",
                    "l1": 36,
                    "l2": 3.6,
                    "l3": 1.6,
                    "unit": "%"
                  }
                ],
                "energy": [
                  {
                    "name": "active_energy",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "active_energy_export",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "reactive_energy",
                    "value": 70,
                    "unit": "kvarh"
                  },
                  {
                    "name": "apparent_energy",
                    "value": 70,
                    "unit": "kvah"
                  }
                ],
                "demands": [
                  {
                    "name": "max_amp",
                    "l1": 70,
                    "l2": 70,
                    "l3": 70,
                    "unit": "A"
                  }
                ],
                "total_demands": [
                  {
                    "name": "max_power_demand",
                    "value": 1270,
                    "unit": "A"
                  },
                  {
                    "name": "accumulated_power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "max_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "accumulated_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf_import_at_sliding_winow",
                    "value": 0.9,
                    "unit": "pf"
                  }
                ]
              }
            },
            "power_demand": {
              "dates": {
                "dates": [
                  "2020-12-26T13:53:19.525251",
                  "2020-12-26T14:06:19.525251",
                  "2020-12-26T14:21:19.525251",
                  "2020-12-26T14:35:19.525251",
                  "2020-12-26T14:47:19.525251",
                  "2020-12-26T15:01:19.525251",
                  "2020-12-26T15:14:19.525251",
                  "2020-12-26T15:26:19.525251",
                  "2020-12-26T15:41:19.525251",
                  "2020-12-26T15:54:19.525251",
                  "2020-12-26T16:06:19.525251",
                  "2020-12-26T16:18:19.525251",
                  "2020-12-26T16:30:19.525251",
                  "2020-12-26T16:42:19.525251",
                  "2020-12-26T16:57:19.525251",
                  "2020-12-26T17:10:19.525251",
                  "2020-12-26T17:22:19.525251",
                  "2020-12-26T17:34:19.525251",
                  "2020-12-26T17:48:19.525251",
                  "2020-12-26T18:03:19.525251",
                  "2020-12-26T18:16:19.525251",
                  "2020-12-26T18:31:19.525251",
                  "2020-12-26T18:46:19.525251",
                  "2020-12-26T19:00:19.525251",
                  "2020-12-26T19:14:19.525251",
                  "2020-12-26T19:28:19.525251",
                  "2020-12-26T19:43:19.525251",
                  "2020-12-26T19:55:19.525251",
                  "2020-12-26T20:10:19.525251",
                  "2020-12-26T20:22:19.525251"
                ],
                "units": ""
              },
              "power_demand_values": {
                "demand": [
                  67,
                  70,
                  61,
                  70,
                  64,
                  55,
                  60,
                  68,
                  63,
                  55,
                  70,
                  57,
                  65,
                  64,
                  67,
                  67,
                  64,
                  63,
                  65,
                  62,
                  63,
                  69,
                  70,
                  59,
                  65,
                  69,
                  65,
                  61,
                  70,
                  69
                ],
                "min": [
                  56,
                  65,
                  69,
                  70,
                  66,
                  56,
                  60,
                  69,
                  69,
                  67,
                  69,
                  58,
                  66,
                  66,
                  55,
                  63,
                  60,
                  67,
                  57,
                  68,
                  66,
                  70,
                  66,
                  69,
                  59,
                  67,
                  61,
                  63,
                  65,
                  63
                ],
                "max": [
                  56,
                  62,
                  60,
                  69,
                  64,
                  69,
                  58,
                  55,
                  55,
                  57,
                  57,
                  62,
                  61,
                  55,
                  56,
                  61,
                  68,
                  62,
                  70,
                  62,
                  60,
                  68,
                  66,
                  57,
                  57,
                  67,
                  60,
                  63,
                  66,
                  55
                ],
                "avg": [
                  70,
                  63,
                  64,
                  64,
                  70,
                  60,
                  64,
                  56,
                  62,
                  59,
                  61,
                  63,
                  58,
                  56,
                  56,
                  59,
                  56,
                  67,
                  61,
                  58,
                  66,
                  55,
                  59,
                  59,
                  59,
                  64,
                  68,
                  60,
                  65,
                  61
                ],
                "units": "kW"
              }
            },
            "time_of_use": {
              "dates": [
                "2020-12-26T13:51:08.705702",
                "2020-12-26T14:04:08.705702",
                "2020-12-26T14:16:08.705702",
                "2020-12-26T14:31:08.705702",
                "2020-12-26T14:45:08.705702",
                "2020-12-26T14:59:08.705702",
                "2020-12-26T15:14:08.705702",
                "2020-12-26T15:29:08.705702",
                "2020-12-26T15:44:08.705702",
                "2020-12-26T15:58:08.705702",
                "2020-12-26T16:10:08.705702",
                "2020-12-26T16:25:08.705702",
                "2020-12-26T16:37:08.705702",
                "2020-12-26T16:49:08.705702",
                "2020-12-26T17:01:08.705702",
                "2020-12-26T17:16:08.705702",
                "2020-12-26T17:28:08.705702",
                "2020-12-26T17:41:08.705702",
                "2020-12-26T17:53:08.705702",
                "2020-12-26T18:07:08.705702",
                "2020-12-26T18:19:08.705702",
                "2020-12-26T18:34:08.705702",
                "2020-12-26T18:48:08.705702",
                "2020-12-26T19:00:08.705702",
                "2020-12-26T19:15:08.705702",
                "2020-12-26T19:30:08.705702",
                "2020-12-26T19:44:08.705702",
                "2020-12-26T19:56:08.705702",
                "2020-12-26T20:09:08.705702",
                "2020-12-26T20:24:08.705702"
              ],
              "values": [
                10,
                5,
                9,
                7,
                8,
                10,
                10,
                9,
                8,
                10,
                7,
                8,
                10,
                9,
                8,
                5,
                8,
                5,
                5,
                5,
                5,
                9,
                6,
                7,
                10,
                9,
                6,
                5,
                6,
                10
              ]
            },
            "energy_consumption": {
              "previous": 1340,
              "current": 1614,
              "usage": 274,
              "dates": {
                "dates": [
                  "2020-12-27T14:42:33.379395",
                  "2020-12-27T14:54:33.379395",
                  "2020-12-27T15:09:33.379395",
                  "2020-12-27T15:22:33.379395",
                  "2020-12-27T15:36:33.379395",
                  "2020-12-27T15:48:33.379395",
                  "2020-12-27T16:01:33.379395",
                  "2020-12-27T16:15:33.379395",
                  "2020-12-27T16:28:33.379395",
                  "2020-12-27T16:40:33.379395",
                  "2020-12-27T16:55:33.379395",
                  "2020-12-27T17:09:33.379395",
                  "2020-12-27T17:24:33.379395",
                  "2020-12-27T17:39:33.379395",
                  "2020-12-27T17:53:33.379395",
                  "2020-12-27T18:05:33.379395",
                  "2020-12-27T18:20:33.379395",
                  "2020-12-27T18:35:33.379395",
                  "2020-12-27T18:50:33.379395",
                  "2020-12-27T19:04:33.379395",
                  "2020-12-27T19:19:33.379395",
                  "2020-12-27T19:32:33.379395",
                  "2020-12-27T19:45:33.379395",
                  "2020-12-27T19:57:33.379395",
                  "2020-12-27T20:09:33.379395",
                  "2020-12-27T20:23:33.379395",
                  "2020-12-27T20:38:33.379395",
                  "2020-12-27T20:52:33.379395",
                  "2020-12-27T21:04:33.379395",
                  "2020-12-27T21:17:33.379395"
                ],
                "units": ""
              },
              "energy_consumption_values": {
                "value": [
                  361,
                  308,
                  373,
                  367,
                  331,
                  354,
                  377,
                  378,
                  363,
                  377,
                  327,
                  346,
                  319,
                  377,
                  346,
                  322,
                  365,
                  302,
                  326,
                  373,
                  313,
                  366,
                  364,
                  343,
                  326,
                  356,
                  315,
                  333,
                  337,
                  368
                ],
                "units": "kWh"
              }
            },
            "billing": {
              "totals": {
                "previous_total": {
                  "usage_kwh": 24000,
                  "value_naira": 5000000
                },
                "present_total": {
                  "usage_kwh": 2000,
                  "value_naira": 5000000
                }
              },
              "consumption_kwh": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  347,
                  302,
                  309,
                  366,
                  348,
                  308,
                  325,
                  338,
                  347,
                  322,
                  342,
                  326,
                  300,
                  322,
                  357,
                  302,
                  369,
                  303,
                  303,
                  328,
                  362,
                  318,
                  332,
                  312,
                  313,
                  317,
                  376,
                  378,
                  355,
                  337
                ]
              },
              "consumption_naira": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  372,
                  317,
                  330,
                  363,
                  340,
                  324,
                  364,
                  371,
                  302,
                  303,
                  302,
                  358,
                  377,
                  360,
                  310,
                  317,
                  375,
                  324,
                  304,
                  362,
                  310,
                  301,
                  327,
                  376,
                  354,
                  365,
                  372,
                  377,
                  345,
                  321
                ]
              }
            },
            "id": 3
          }
        ],
        "daily_kwh": {
          "dates": [
            "Jan 1",
            "Jan 2",
            "Jan 3",
            "Jan 4",
            "Jan 5",
            "Jan 6",
            "Jan 7",
            "Jan 8",
            "Jan 9",
            "Jan 10",
            "Jan 11",
            "Jan 12",
            "Jan 13",
            "Jan 14",
            "Jan 15",
            "Jan 16",
            "Jan 17",
            "Jan 18",
            "Jan 19",
            "Jan 10",
            "Jan 21",
            "Jan 22",
            "Jan 23",
            "Jan 24",
            "Jan 25",
            "Jan 26",
            "Jan 27",
            "Jan 28",
            "Jan 29",
            "Jan 30",
            "Jan 31"
          ],
          "Utility": [
            203,
            852,
            863,
            355,
            290,
            664,
            315,
            866,
            828,
            102,
            728,
            267,
            138,
            848,
            330,
            78,
            207,
            164,
            426,
            702,
            262,
            822,
            63,
            889,
            278,
            547,
            844,
            794,
            227,
            857,
            729
          ],
          "Gen1": [
            203,
            852,
            863,
            355,
            290,
            664,
            315,
            866,
            828,
            102,
            728,
            267,
            138,
            848,
            330,
            78,
            207,
            164,
            426,
            702,
            262,
            822,
            63,
            889,
            278,
            547,
            844,
            794,
            227,
            857,
            729
          ],
          "Gen2": [
            203,
            852,
            863,
            355,
            290,
            664,
            315,
            866,
            828,
            102,
            728,
            267,
            138,
            848,
            330,
            78,
            207,
            164,
            426,
            702,
            262,
            822,
            63,
            889,
            278,
            547,
            844,
            794,
            227,
            857,
            729
          ]
        },
        "usage_hours": {
          "devices": ["Utility", "Gen1", "Gen2"],
          "hours": [23, 10, 94]
        },
        "time_of_use_table": {
          "dates": {
            "dates": [
              "2020-12-26T13:50:08.705702",
              "2020-12-26T14:05:08.705702",
              "2020-12-26T14:18:08.705702",
              "2020-12-26T14:33:08.705702",
              "2020-12-26T14:46:08.705702",
              "2020-12-26T15:01:08.705702",
              "2020-12-26T15:14:08.705702",
              "2020-12-26T15:29:08.705702",
              "2020-12-26T15:42:08.705702",
              "2020-12-26T15:55:08.705702",
              "2020-12-26T16:08:08.705702",
              "2020-12-26T16:21:08.705702",
              "2020-12-26T16:35:08.705702",
              "2020-12-26T16:47:08.705702",
              "2020-12-26T16:59:08.705702",
              "2020-12-26T17:14:08.705702",
              "2020-12-26T17:26:08.705702",
              "2020-12-26T17:38:08.705702",
              "2020-12-26T17:53:08.705702",
              "2020-12-26T18:05:08.705702",
              "2020-12-26T18:17:08.705702",
              "2020-12-26T18:29:08.705702",
              "2020-12-26T18:43:08.705702",
              "2020-12-26T18:55:08.705702",
              "2020-12-26T19:09:08.705702",
              "2020-12-26T19:24:08.705702",
              "2020-12-26T19:38:08.705702",
              "2020-12-26T19:50:08.705702",
              "2020-12-26T20:02:08.705702",
              "2020-12-26T20:15:08.705702"
            ],
            "units": ""
          },
          "values": {
            "utility": [
              9,
              5,
              6,
              5,
              5,
              5,
              10,
              8,
              9,
              10,
              10,
              6,
              6,
              6,
              10,
              10,
              9,
              5,
              7,
              7,
              8,
              7,
              6,
              5,
              7,
              7,
              7,
              6,
              10,
              6
            ],
            "gen1": [
              8,
              10,
              5,
              7,
              10,
              7,
              10,
              10,
              6,
              7,
              8,
              6,
              9,
              6,
              7,
              5,
              7,
              9,
              8,
              9,
              10,
              10,
              8,
              8,
              7,
              9,
              7,
              9,
              5,
              7
            ],
            "gen2": [
              6,
              8,
              7,
              8,
              6,
              9,
              5,
              7,
              6,
              6,
              6,
              6,
              6,
              10,
              10,
              8,
              9,
              6,
              9,
              10,
              10,
              9,
              6,
              9,
              8,
              9,
              5,
              10,
              9,
              5
            ],
            "active_gen": [
              "utility",
              "gen1",
              "gen2",
              "utility",
              "utility",
              "utility",
              "gen2",
              "gen2",
              "gen2",
              "gen1",
              "gen2",
              "gen1",
              "gen1",
              "utility",
              "gen1",
              "gen1",
              "utility",
              "utility",
              "gen1",
              "utility",
              "gen1",
              "gen1",
              "gen1",
              "gen1",
              "utility",
              "gen1",
              "gen2",
              "gen1",
              "gen1",
              "gen2"
            ],
            "time_on": [
              "05:33 AM",
              "11:50 AM",
              "02:51 AM",
              "12:11 PM",
              "11:57 AM",
              "12:03 AM",
              "12:11 PM",
              "10:04 AM",
              "07:13 AM",
              "09:58 AM",
              "12:22 PM",
              "09:53 AM",
              "10:29 AM",
              "05:58 AM",
              "05:02 AM",
              "12:21 AM",
              "07:02 AM",
              "02:21 AM",
              "02:50 AM",
              "03:25 AM",
              "11:21 AM",
              "06:58 AM",
              "10:44 AM",
              "03:56 AM",
              "04:28 AM",
              "04:31 AM",
              "05:55 AM",
              "06:17 AM",
              "09:06 AM",
              "08:34 AM"
            ],
            "time_off": [
              "07:15 AM",
              "06:05 AM",
              "07:38 AM",
              "03:50 AM",
              "11:10 AM",
              "12:56 AM",
              "11:08 AM",
              "04:51 AM",
              "03:35 AM",
              "12:20 PM",
              "10:50 AM",
              "10:36 AM",
              "08:27 AM",
              "08:14 AM",
              "11:10 AM",
              "09:42 AM",
              "06:03 AM",
              "09:31 AM",
              "06:37 AM",
              "10:54 AM",
              "12:10 AM",
              "12:45 PM",
              "09:03 AM",
              "12:55 AM",
              "01:04 AM",
              "12:58 PM",
              "03:32 AM",
              "01:21 AM",
              "11:04 AM",
              "01:36 AM"
            ],
            "hours_of_use": [
              "11:11 AM",
              "08:07 AM",
              "07:58 AM",
              "01:04 AM",
              "03:25 AM",
              "06:26 AM",
              "10:16 AM",
              "11:32 AM",
              "09:02 AM",
              "09:15 AM",
              "12:50 PM",
              "04:43 AM",
              "06:47 AM",
              "11:21 AM",
              "06:15 AM",
              "09:58 AM",
              "10:53 AM",
              "06:57 AM",
              "10:28 AM",
              "11:51 AM",
              "12:13 AM",
              "09:22 AM",
              "12:01 AM",
              "06:40 AM",
              "10:14 AM",
              "04:56 AM",
              "11:02 AM",
              "07:32 AM",
              "09:58 AM",
              "10:01 AM"
            ]
          }
        },
        "cost_tracker_qty_of_diesel": {
          "dates": [
            "2021-01-03T10:44:11.451274",
            "2021-01-03T10:58:11.451274",
            "2021-01-03T11:10:11.451274",
            "2021-01-03T11:25:11.451274",
            "2021-01-03T11:40:11.451274",
            "2021-01-03T11:55:11.451274",
            "2021-01-03T12:10:11.451274",
            "2021-01-03T12:24:11.451274",
            "2021-01-03T12:37:11.451274",
            "2021-01-03T12:51:11.451274",
            "2021-01-03T13:04:11.451274",
            "2021-01-03T13:18:11.451274",
            "2021-01-03T13:32:11.451274",
            "2021-01-03T13:44:11.451274",
            "2021-01-03T13:59:11.451274",
            "2021-01-03T14:12:11.451274",
            "2021-01-03T14:24:11.451274",
            "2021-01-03T14:38:11.451274",
            "2021-01-03T14:51:11.451274",
            "2021-01-03T15:04:11.451274"
          ],
          "values": [
            374,
            355,
            318,
            342,
            353,
            362,
            307,
            372,
            304,
            379,
            362,
            341,
            329,
            375,
            312,
            360,
            348,
            348,
            372,
            336
          ],
          "units": "litres"
        },
        "cost_tracker_monthly_cost": {
          "dates": ["Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug"],
          "values": [
            130069,
            130890,
            386638,
            340670,
            204928,
            379737,
            499902,
            490501
          ],
          "units": "Naira"
        },
        "cost_tracker_consumption_breakdown": {
          "dates": [
            "2021-01-03T10:44:11.451274",
            "2021-01-03T10:58:11.451274",
            "2021-01-03T11:10:11.451274",
            "2021-01-03T11:25:11.451274",
            "2021-01-03T11:40:11.451274",
            "2021-01-03T11:55:11.451274",
            "2021-01-03T12:10:11.451274",
            "2021-01-03T12:24:11.451274",
            "2021-01-03T12:37:11.451274",
            "2021-01-03T12:51:11.451274",
            "2021-01-03T13:04:11.451274",
            "2021-01-03T13:18:11.451274",
            "2021-01-03T13:32:11.451274",
            "2021-01-03T13:44:11.451274",
            "2021-01-03T13:59:11.451274",
            "2021-01-03T14:12:11.451274",
            "2021-01-03T14:24:11.451274",
            "2021-01-03T14:38:11.451274",
            "2021-01-03T14:51:11.451274",
            "2021-01-03T15:04:11.451274"
          ],
          "gen1": [
            111908,
            209064,
            428958,
            198344,
            440047,
            277248,
            142245,
            305513,
            447656,
            480031,
            240516,
            492333,
            457292,
            420354,
            479373,
            135359,
            330892,
            154439,
            230464,
            494097
          ],
          "gen2": [
            396045,
            128608,
            246769,
            286449,
            360969,
            278225,
            319562,
            253900,
            225472,
            116030,
            137465,
            326628,
            472324,
            429497,
            127056,
            299002,
            224864,
            377776,
            176927,
            199125
          ],
          "units": "litres"
        },
        "billing_totals": {
          "previous_total": {
            "usage_kwh": 24000,
            "value_naira": 5000000
          },
          "present_total": {
            "usage_kwh": 24000,
            "value_naira": 5000000
          },
          "metrics": {
            "diesel_per_kwh": 70,
            "utility_per_kwh": 30,
            "blended_cost_per_kwh": 60,
            "unit": "₦"
          },
          "usage": {
            "previous_kwh": 202311,
            "present_kwh": 20311,
            "total_usage_kwh": 182000
          }
        },
        "id": 8
      },
      {
        "name": "Meadow hall school",
        "devices": [
          {
            "name": "Utility",
            "dashboard": {
              "total_kwh": {
                "unit": "kWh",
                "value": 1000
              },
              "min_demand": {
                "unit": "kW",
                "value": 200
              },
              "max_demand": {
                "unit": "kW",
                "value": 300
              },
              "avg_demand": {
                "unit": "kW",
                "value": 150
              },
              "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": 34.25
              },
              "cost_of_energy": {
                "unit": "Naira",
                "value": 4000
              },
              "today": {
                "value": 150,
                "unit": "kWh"
              },
              "yesterday": {
                "value": 3000,
                "unit": "kWh"
              }
            },
            "score_card": {
              "is_generator": False,
              "baseline_energy": {
                "unit": "kWh",
                "forecast": 1000,
                "used": 500
              },
              "peak_to_avg_power_ratio": {
                "unit": "kW",
                "peak": 1000,
                "avg": 500,
                "message": "Not so efficient, Higher is better",
                "message_color": "green"
              },
              "score_card_carbon_emissions": {
                "unit": "Tons",
                "estimated_value": 110,
                "actual_value": 50,
                "message": "Equivalent to 15 Acacia trees"
              },
              "generator_size_efficiency": False,
              "change_over_lags": False,
              "operating_time": {
                "chart": {
                  "dates": [
                    "Jan 1",
                    "Jan 2",
                    "Jan 3",
                    "Jan 4",
                    "Jan 5",
                    "Jan 6",
                    "Jan 7",
                    "Jan 8",
                    "Jan 9",
                    "Jan 10",
                    "Jan 11",
                    "Jan 12",
                    "Jan 13",
                    "Jan 14",
                    "Jan 15",
                    "Jan 16",
                    "Jan 17",
                    "Jan 18",
                    "Jan 19",
                    "Jan 10",
                    "Jan 21",
                    "Jan 22",
                    "Jan 23",
                    "Jan 24",
                    "Jan 25",
                    "Jan 26",
                    "Jan 27",
                    "Jan 28",
                    "Jan 29",
                    "Jan 30",
                    "Jan 31"
                  ],
                  "values": [
                    199,
                    78,
                    52,
                    140,
                    111,
                    44,
                    186,
                    122,
                    12,
                    158,
                    174,
                    51,
                    218,
                    88,
                    196,
                    154,
                    70,
                    47,
                    54,
                    189,
                    17,
                    217,
                    25,
                    164,
                    243,
                    20,
                    5,
                    39,
                    227,
                    168,
                    161
                  ]
                },
                "estimated_time_wasted": {
                  "unit": "hours",
                  "value": 12
                },
                "estimated_diesel_wasted": False,
                "estimated_cost": {
                  "unit": "Naira",
                  "value": 12000
                }
              },
              "fuel_consumption": False
            },
            "power_quality": {
              "dates": {
                "dates": [
                  "2020-12-14T20:15:57.329485",
                  "2020-12-14T20:27:57.329485",
                  "2020-12-14T20:41:57.329485",
                  "2020-12-14T20:53:57.329485",
                  "2020-12-14T21:06:57.329485",
                  "2020-12-14T21:18:57.329485",
                  "2020-12-14T21:33:57.329485",
                  "2020-12-14T21:46:57.329485",
                  "2020-12-14T22:00:57.329485",
                  "2020-12-14T22:12:57.329485",
                  "2020-12-14T22:27:57.329485"
                ],
                "units": ""
              },
              "voltage": {
                "l1": [221, 236, 228, 228, 227, 237, 240, 222, 234, 227, 228],
                "l2": [237, 230, 229, 228, 224, 224, 228, 231, 231, 223, 230],
                "l3": [239, 225, 239, 234, 236, 220, 228, 231, 234, 236, 221],
                "neutral": [20, 10, 18, 8, 11, 14, 11, 5, 18, 6, 19],
                "units": "volts"
              },
              "current": {
                "l1": [165, 175, 179, 178, 174, 180, 178, 175, 172, 180, 162],
                "l2": [157, 152, 175, 160, 166, 174, 156, 153, 167, 156, 164],
                "l3": [169, 174, 169, 167, 176, 176, 178, 165, 179, 171, 177],
                "neutral": [10, 10, 20, 6, 13, 15, 20, 5, 19, 11, 11],
                "units": "amps"
              },
              "active_power": {
                "l1": [66, 67, 60, 57, 70, 60, 62, 57, 65, 63, 69],
                "l2": [65, 70, 59, 56, 70, 62, 61, 59, 57, 65, 58],
                "l3": [67, 56, 59, 63, 60, 66, 58, 68, 59, 63, 64],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kW"
              },
              "reactive_power": {
                "l1": [88, 83, 71, 51, 86, 90, 85, 60, 89, 71, 55],
                "l2": [71, 65, 72, 62, 75, 67, 57, 53, 66, 75, 89],
                "l3": [67, 53, 64, 62, 68, 59, 74, 66, 54, 68, 69],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kVar"
              },
              "energy": {
                "l1": [300, 322, 301, 346, 355, 310, 341, 370, 371, 363, 338],
                "l2": [308, 340, 307, 374, 377, 325, 321, 325, 324, 318, 308],
                "l3": [367, 301, 312, 371, 300, 360, 357, 370, 361, 333, 345],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kWh"
              },
              "frequency": {
                "average": [67, 30, 44, 70, 22, 33, 69, 78, 40, 66, 45],
                "units": "hz"
              },
              "power_factor": {
                "l1_l2_l3": [
                  0.98,
                  0.85,
                  0.87,
                  0.83,
                  0.85,
                  0.99,
                  0.81,
                  0.95,
                  0.92,
                  0.97,
                  0.97
                ],
                "units": ""
              }
            },
            "last_reading": {
              "date": "2020-12-18T09:27:32.394646",
              "data": {
                "phase_measures": [
                  {
                    "name": "voltage",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "volts"
                  },
                  {
                    "name": "current",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "amps"
                  },
                  {
                    "name": "active_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf",
                    "l1": 0.9,
                    "l2": 0.89,
                    "l3": 0.89,
                    "unit": "pf"
                  }
                ],
                "totals": [
                  {
                    "name": "active_power",
                    "value": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "value": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "value": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "frequency",
                    "value": 231,
                    "unit": "hz"
                  },
                  {
                    "name": "pf",
                    "value": 0.8,
                    "unit": "pf"
                  },
                  {
                    "name": "neutral_current",
                    "value": 70,
                    "unit": "A"
                  }
                ],
                "harmonic_distortion": [
                  {
                    "name": "voltage_thd_ln",
                    "l1": 0.7,
                    "l2": 0.7,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_thd",
                    "l1": 18,
                    "l2": 15.1,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_tdd",
                    "l1": 36,
                    "l2": 3.6,
                    "l3": 1.6,
                    "unit": "%"
                  }
                ],
                "energy": [
                  {
                    "name": "active_energy",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "active_energy_export",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "reactive_energy",
                    "value": 70,
                    "unit": "kvarh"
                  },
                  {
                    "name": "apparent_energy",
                    "value": 70,
                    "unit": "kvah"
                  }
                ],
                "demands": [
                  {
                    "name": "max_amp",
                    "l1": 70,
                    "l2": 70,
                    "l3": 70,
                    "unit": "A"
                  }
                ],
                "total_demands": [
                  {
                    "name": "max_power_demand",
                    "value": 1270,
                    "unit": "A"
                  },
                  {
                    "name": "accumulated_power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "max_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "accumulated_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf_import_at_sliding_winow",
                    "value": 0.9,
                    "unit": "pf"
                  }
                ]
              }
            },
            "power_demand": {
              "dates": {
                "dates": [
                  "2020-12-26T13:53:19.525251",
                  "2020-12-26T14:06:19.525251",
                  "2020-12-26T14:21:19.525251",
                  "2020-12-26T14:35:19.525251",
                  "2020-12-26T14:47:19.525251",
                  "2020-12-26T15:01:19.525251",
                  "2020-12-26T15:14:19.525251",
                  "2020-12-26T15:26:19.525251",
                  "2020-12-26T15:41:19.525251",
                  "2020-12-26T15:54:19.525251",
                  "2020-12-26T16:06:19.525251",
                  "2020-12-26T16:18:19.525251",
                  "2020-12-26T16:30:19.525251",
                  "2020-12-26T16:42:19.525251",
                  "2020-12-26T16:57:19.525251",
                  "2020-12-26T17:10:19.525251",
                  "2020-12-26T17:22:19.525251",
                  "2020-12-26T17:34:19.525251",
                  "2020-12-26T17:48:19.525251",
                  "2020-12-26T18:03:19.525251",
                  "2020-12-26T18:16:19.525251",
                  "2020-12-26T18:31:19.525251",
                  "2020-12-26T18:46:19.525251",
                  "2020-12-26T19:00:19.525251",
                  "2020-12-26T19:14:19.525251",
                  "2020-12-26T19:28:19.525251",
                  "2020-12-26T19:43:19.525251",
                  "2020-12-26T19:55:19.525251",
                  "2020-12-26T20:10:19.525251",
                  "2020-12-26T20:22:19.525251"
                ],
                "units": ""
              },
              "power_demand_values": {
                "demand": [
                  55,
                  56,
                  68,
                  69,
                  60,
                  59,
                  60,
                  55,
                  60,
                  66,
                  59,
                  66,
                  69,
                  60,
                  66,
                  56,
                  60,
                  65,
                  59,
                  69,
                  70,
                  58,
                  55,
                  57,
                  61,
                  58,
                  58,
                  68,
                  60,
                  67
                ],
                "min": [
                  63,
                  57,
                  58,
                  68,
                  66,
                  59,
                  67,
                  70,
                  68,
                  62,
                  67,
                  60,
                  64,
                  62,
                  63,
                  60,
                  64,
                  62,
                  65,
                  59,
                  60,
                  58,
                  56,
                  57,
                  60,
                  59,
                  69,
                  67,
                  56,
                  68
                ],
                "max": [
                  70,
                  58,
                  65,
                  67,
                  66,
                  68,
                  61,
                  58,
                  58,
                  63,
                  69,
                  67,
                  61,
                  61,
                  59,
                  65,
                  57,
                  67,
                  57,
                  66,
                  57,
                  61,
                  70,
                  56,
                  68,
                  58,
                  64,
                  62,
                  69,
                  61
                ],
                "avg": [
                  64,
                  63,
                  63,
                  55,
                  58,
                  59,
                  58,
                  64,
                  68,
                  65,
                  59,
                  67,
                  70,
                  61,
                  70,
                  63,
                  63,
                  64,
                  66,
                  58,
                  68,
                  69,
                  64,
                  62,
                  70,
                  70,
                  66,
                  67,
                  57,
                  56
                ],
                "units": "kW"
              }
            },
            "time_of_use": {
              "dates": [
                "2020-12-26T13:50:08.714701",
                "2020-12-26T14:04:08.714701",
                "2020-12-26T14:19:08.714701",
                "2020-12-26T14:33:08.714701",
                "2020-12-26T14:48:08.714701",
                "2020-12-26T15:03:08.714701",
                "2020-12-26T15:15:08.714701",
                "2020-12-26T15:29:08.714701",
                "2020-12-26T15:43:08.714701",
                "2020-12-26T15:58:08.714701",
                "2020-12-26T16:10:08.714701",
                "2020-12-26T16:24:08.714701",
                "2020-12-26T16:39:08.714701",
                "2020-12-26T16:54:08.714701",
                "2020-12-26T17:06:08.714701",
                "2020-12-26T17:19:08.714701",
                "2020-12-26T17:33:08.714701",
                "2020-12-26T17:45:08.714701",
                "2020-12-26T17:58:08.714701",
                "2020-12-26T18:11:08.714701",
                "2020-12-26T18:25:08.714701",
                "2020-12-26T18:38:08.714701",
                "2020-12-26T18:52:08.714701",
                "2020-12-26T19:07:08.714701",
                "2020-12-26T19:19:08.714701",
                "2020-12-26T19:32:08.714701",
                "2020-12-26T19:44:08.714701",
                "2020-12-26T19:58:08.714701",
                "2020-12-26T20:11:08.714701",
                "2020-12-26T20:26:08.714701"
              ],
              "values": [
                9,
                5,
                9,
                9,
                5,
                8,
                10,
                8,
                8,
                7,
                10,
                5,
                10,
                8,
                10,
                5,
                8,
                6,
                7,
                8,
                9,
                6,
                9,
                6,
                5,
                7,
                5,
                7,
                7,
                10
              ]
            },
            "energy_consumption": {
              "previous": 1406,
              "current": 1560,
              "usage": 154,
              "dates": {
                "dates": [
                  "2020-12-27T14:42:33.379395",
                  "2020-12-27T14:54:33.379395",
                  "2020-12-27T15:09:33.379395",
                  "2020-12-27T15:22:33.379395",
                  "2020-12-27T15:36:33.379395",
                  "2020-12-27T15:48:33.379395",
                  "2020-12-27T16:01:33.379395",
                  "2020-12-27T16:15:33.379395",
                  "2020-12-27T16:28:33.379395",
                  "2020-12-27T16:40:33.379395",
                  "2020-12-27T16:55:33.379395",
                  "2020-12-27T17:09:33.379395",
                  "2020-12-27T17:24:33.379395",
                  "2020-12-27T17:39:33.379395",
                  "2020-12-27T17:53:33.379395",
                  "2020-12-27T18:05:33.379395",
                  "2020-12-27T18:20:33.379395",
                  "2020-12-27T18:35:33.379395",
                  "2020-12-27T18:50:33.379395",
                  "2020-12-27T19:04:33.379395",
                  "2020-12-27T19:19:33.379395",
                  "2020-12-27T19:32:33.379395",
                  "2020-12-27T19:45:33.379395",
                  "2020-12-27T19:57:33.379395",
                  "2020-12-27T20:09:33.379395",
                  "2020-12-27T20:23:33.379395",
                  "2020-12-27T20:38:33.379395",
                  "2020-12-27T20:52:33.379395",
                  "2020-12-27T21:04:33.379395",
                  "2020-12-27T21:17:33.379395"
                ],
                "units": ""
              },
              "energy_consumption_values": {
                "value": [
                  375,
                  325,
                  337,
                  315,
                  328,
                  377,
                  365,
                  315,
                  370,
                  346,
                  331,
                  351,
                  316,
                  373,
                  319,
                  360,
                  310,
                  310,
                  355,
                  368,
                  361,
                  344,
                  315,
                  362,
                  371,
                  341,
                  352,
                  352,
                  332,
                  313
                ],
                "units": "kWh"
              }
            },
            "billing": {
              "totals": {
                "previous_total": {
                  "usage_kwh": 24000,
                  "value_naira": 5000000
                },
                "present_total": {
                  "usage_kwh": 2000,
                  "value_naira": 5000000
                }
              },
              "consumption_kwh": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  333,
                  310,
                  374,
                  367,
                  366,
                  338,
                  351,
                  323,
                  364,
                  330,
                  316,
                  301,
                  322,
                  353,
                  306,
                  370,
                  308,
                  372,
                  354,
                  331,
                  304,
                  349,
                  314,
                  356,
                  376,
                  346,
                  324,
                  354,
                  331,
                  354
                ]
              },
              "consumption_naira": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  369,
                  358,
                  324,
                  338,
                  325,
                  322,
                  353,
                  331,
                  360,
                  319,
                  324,
                  341,
                  320,
                  358,
                  371,
                  313,
                  378,
                  322,
                  334,
                  310,
                  338,
                  377,
                  314,
                  343,
                  307,
                  322,
                  341,
                  354,
                  375,
                  319
                ]
              }
            },
            "id": 1
          },
          {
            "name": "Gen1",
            "dashboard": {
              "total_kwh": {
                "unit": "kWh",
                "value": 1000
              },
              "min_demand": {
                "unit": "kW",
                "value": 200
              },
              "max_demand": {
                "unit": "kW",
                "value": 300
              },
              "avg_demand": {
                "unit": "kW",
                "value": 150
              },
              "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": 34.25
              },
              "cost_of_energy": {
                "unit": "Naira",
                "value": 4000
              },
              "today": {
                "value": 150,
                "unit": "kWh"
              },
              "yesterday": {
                "value": 3000,
                "unit": "kWh"
              }
            },
            "score_card": {
              "is_generator": True,
              "baseline_energy": {
                "unit": "kWh",
                "forecast": 1000,
                "used": 500
              },
              "peak_to_avg_power_ratio": {
                "unit": "kW",
                "peak": 1000,
                "avg": 500,
                "message": "Not so efficient, Higher is better",
                "message_color": "green"
              },
              "score_card_carbon_emissions": {
                "unit": "Tons",
                "estimated_value": 110,
                "actual_value": 50,
                "message": "Equivalent to 15 Acacia trees"
              },
              "generator_size_efficiency": {
                "size": "100kva",
                "usage": 30,
                "unit": "%"
              },
              "change_over_lags": {
                "data": [
                  {
                    "date": "Jan 1 2020",
                    "lag_duration": 15,
                    "diesel_cost": 20,
                    "diesel_value": 4800,
                    "id": 1
                  },
                  {
                    "date": "Jan 2 2020",
                    "lag_duration": 14,
                    "diesel_cost": 10,
                    "diesel_value": 2400,
                    "id": 2
                  },
                  {
                    "date": "Jan 3 2020",
                    "lag_duration": 60,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 3
                  },
                  {
                    "date": "Jan 4 2020",
                    "lag_duration": 120,
                    "diesel_cost": 30,
                    "diesel_value": 7200,
                    "id": 4
                  },
                  {
                    "date": "Jan 5 2020",
                    "lag_duration": 90,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 5
                  }
                ],
                "units": {
                  "lag_duration": "Minutes",
                  "diesel_cost": "Litres"
                }
              },
              "operating_time": {
                "chart": {
                  "dates": [
                    "Jan 1",
                    "Jan 2",
                    "Jan 3",
                    "Jan 4",
                    "Jan 5",
                    "Jan 6",
                    "Jan 7",
                    "Jan 8",
                    "Jan 9",
                    "Jan 10",
                    "Jan 11",
                    "Jan 12",
                    "Jan 13",
                    "Jan 14",
                    "Jan 15",
                    "Jan 16",
                    "Jan 17",
                    "Jan 18",
                    "Jan 19",
                    "Jan 10",
                    "Jan 21",
                    "Jan 22",
                    "Jan 23",
                    "Jan 24",
                    "Jan 25",
                    "Jan 26",
                    "Jan 27",
                    "Jan 28",
                    "Jan 29",
                    "Jan 30",
                    "Jan 31"
                  ],
                  "values": [
                    199,
                    78,
                    52,
                    140,
                    111,
                    44,
                    186,
                    122,
                    12,
                    158,
                    174,
                    51,
                    218,
                    88,
                    196,
                    154,
                    70,
                    47,
                    54,
                    189,
                    17,
                    217,
                    25,
                    164,
                    243,
                    20,
                    5,
                    39,
                    227,
                    168,
                    161
                  ]
                },
                "estimated_time_wasted": {
                  "unit": "hours",
                  "value": 12
                },
                "estimated_diesel_wasted": {
                  "unit": "Litres",
                  "value": 300
                },
                "estimated_cost": {
                  "unit": "Naira",
                  "value": 12000
                }
              },
              "fuel_consumption": {
                "name": "main_generator",
                "size": "100kva",
                "diesel_usage": 30,
                "time_used": "12",
                "hours_to_maintenance": {
                  "hours": 23,
                  "unit": "hours"
                }
              }
            },
            "power_quality": {
              "dates": {
                "dates": [
                  "2020-12-14T20:14:57.329485",
                  "2020-12-14T20:27:57.329485",
                  "2020-12-14T20:41:57.329485",
                  "2020-12-14T20:53:57.329485",
                  "2020-12-14T21:06:57.329485",
                  "2020-12-14T21:21:57.329485",
                  "2020-12-14T21:34:57.329485",
                  "2020-12-14T21:46:57.329485",
                  "2020-12-14T21:59:57.329485",
                  "2020-12-14T22:14:57.329485",
                  "2020-12-14T22:28:57.329485"
                ],
                "units": ""
              },
              "voltage": {
                "l1": [234, 228, 224, 223, 234, 222, 236, 228, 231, 225, 221],
                "l2": [226, 237, 229, 232, 223, 235, 224, 232, 224, 227, 224],
                "l3": [232, 239, 220, 230, 227, 220, 226, 228, 233, 240, 237],
                "neutral": [20, 17, 7, 16, 10, 11, 10, 10, 14, 20, 14],
                "units": "volts"
              },
              "current": {
                "l1": [152, 162, 179, 150, 169, 172, 176, 153, 166, 165, 160],
                "l2": [168, 160, 171, 171, 170, 176, 164, 172, 178, 150, 157],
                "l3": [160, 150, 179, 179, 161, 174, 150, 151, 150, 156, 174],
                "neutral": [18, 19, 8, 5, 6, 9, 8, 11, 16, 7, 17],
                "units": "amps"
              },
              "active_power": {
                "l1": [56, 70, 70, 55, 62, 68, 58, 59, 67, 65, 64],
                "l2": [63, 63, 64, 68, 68, 63, 70, 69, 55, 61, 70],
                "l3": [56, 66, 68, 69, 60, 61, 68, 67, 62, 58, 63],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kW"
              },
              "reactive_power": {
                "l1": [66, 74, 56, 86, 64, 55, 82, 87, 57, 55, 78],
                "l2": [61, 60, 52, 79, 77, 62, 56, 52, 80, 55, 79],
                "l3": [62, 89, 51, 63, 66, 59, 73, 51, 72, 60, 71],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kVar"
              },
              "energy": {
                "l1": [357, 361, 330, 301, 303, 328, 344, 340, 300, 345, 352],
                "l2": [315, 333, 359, 343, 332, 339, 338, 309, 373, 352, 325],
                "l3": [360, 349, 316, 352, 308, 377, 366, 308, 302, 326, 342],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kWh"
              },
              "frequency": {
                "average": [67, 30, 44, 70, 22, 33, 69, 78, 40, 66, 45],
                "units": "hz"
              },
              "power_factor": {
                "l1_l2_l3": [
                  0.93,
                  0.98,
                  0.98,
                  0.83,
                  0.86,
                  0.87,
                  0.89,
                  0.82,
                  0.94,
                  0.84,
                  0.93
                ],
                "units": ""
              }
            },
            "last_reading": {
              "date": "2020-12-18T09:27:32.394646",
              "data": {
                "phase_measures": [
                  {
                    "name": "voltage",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "volts"
                  },
                  {
                    "name": "current",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "amps"
                  },
                  {
                    "name": "active_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf",
                    "l1": 0.9,
                    "l2": 0.89,
                    "l3": 0.89,
                    "unit": "pf"
                  }
                ],
                "totals": [
                  {
                    "name": "active_power",
                    "value": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "value": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "value": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "frequency",
                    "value": 231,
                    "unit": "hz"
                  },
                  {
                    "name": "pf",
                    "value": 0.8,
                    "unit": "pf"
                  },
                  {
                    "name": "neutral_current",
                    "value": 70,
                    "unit": "A"
                  }
                ],
                "harmonic_distortion": [
                  {
                    "name": "voltage_thd_ln",
                    "l1": 0.7,
                    "l2": 0.7,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_thd",
                    "l1": 18,
                    "l2": 15.1,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_tdd",
                    "l1": 36,
                    "l2": 3.6,
                    "l3": 1.6,
                    "unit": "%"
                  }
                ],
                "energy": [
                  {
                    "name": "active_energy",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "active_energy_export",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "reactive_energy",
                    "value": 70,
                    "unit": "kvarh"
                  },
                  {
                    "name": "apparent_energy",
                    "value": 70,
                    "unit": "kvah"
                  }
                ],
                "demands": [
                  {
                    "name": "max_amp",
                    "l1": 70,
                    "l2": 70,
                    "l3": 70,
                    "unit": "A"
                  }
                ],
                "total_demands": [
                  {
                    "name": "max_power_demand",
                    "value": 1270,
                    "unit": "A"
                  },
                  {
                    "name": "accumulated_power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "max_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "accumulated_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf_import_at_sliding_winow",
                    "value": 0.9,
                    "unit": "pf"
                  }
                ]
              }
            },
            "power_demand": {
              "dates": {
                "dates": [
                  "2020-12-26T13:53:19.525251",
                  "2020-12-26T14:06:19.525251",
                  "2020-12-26T14:21:19.525251",
                  "2020-12-26T14:35:19.525251",
                  "2020-12-26T14:47:19.525251",
                  "2020-12-26T15:01:19.525251",
                  "2020-12-26T15:14:19.525251",
                  "2020-12-26T15:26:19.525251",
                  "2020-12-26T15:41:19.525251",
                  "2020-12-26T15:54:19.525251",
                  "2020-12-26T16:06:19.525251",
                  "2020-12-26T16:18:19.525251",
                  "2020-12-26T16:30:19.525251",
                  "2020-12-26T16:42:19.525251",
                  "2020-12-26T16:57:19.525251",
                  "2020-12-26T17:10:19.525251",
                  "2020-12-26T17:22:19.525251",
                  "2020-12-26T17:34:19.525251",
                  "2020-12-26T17:48:19.525251",
                  "2020-12-26T18:03:19.525251",
                  "2020-12-26T18:16:19.525251",
                  "2020-12-26T18:31:19.525251",
                  "2020-12-26T18:46:19.525251",
                  "2020-12-26T19:00:19.525251",
                  "2020-12-26T19:14:19.525251",
                  "2020-12-26T19:28:19.525251",
                  "2020-12-26T19:43:19.525251",
                  "2020-12-26T19:55:19.525251",
                  "2020-12-26T20:10:19.525251",
                  "2020-12-26T20:22:19.525251"
                ],
                "units": ""
              },
              "power_demand_values": {
                "demand": [
                  57,
                  57,
                  68,
                  66,
                  63,
                  57,
                  60,
                  57,
                  62,
                  70,
                  65,
                  67,
                  66,
                  66,
                  61,
                  60,
                  64,
                  57,
                  69,
                  68,
                  66,
                  57,
                  67,
                  65,
                  65,
                  64,
                  61,
                  55,
                  66,
                  65
                ],
                "min": [
                  56,
                  64,
                  70,
                  62,
                  59,
                  56,
                  62,
                  62,
                  58,
                  69,
                  59,
                  68,
                  63,
                  62,
                  62,
                  59,
                  55,
                  69,
                  57,
                  63,
                  61,
                  69,
                  59,
                  60,
                  57,
                  56,
                  56,
                  69,
                  63,
                  62
                ],
                "max": [
                  66,
                  58,
                  61,
                  64,
                  70,
                  59,
                  67,
                  68,
                  66,
                  69,
                  55,
                  57,
                  63,
                  62,
                  59,
                  70,
                  66,
                  59,
                  69,
                  55,
                  67,
                  69,
                  57,
                  63,
                  60,
                  62,
                  64,
                  62,
                  65,
                  65
                ],
                "avg": [
                  56,
                  64,
                  64,
                  60,
                  63,
                  63,
                  66,
                  55,
                  55,
                  55,
                  66,
                  56,
                  59,
                  61,
                  64,
                  61,
                  56,
                  65,
                  65,
                  55,
                  55,
                  58,
                  63,
                  66,
                  62,
                  68,
                  55,
                  60,
                  56,
                  64
                ],
                "units": "kW"
              }
            },
            "time_of_use": {
              "dates": [
                "2020-12-26T13:51:08.714701",
                "2020-12-26T14:03:08.714701",
                "2020-12-26T14:18:08.714701",
                "2020-12-26T14:33:08.714701",
                "2020-12-26T14:46:08.714701",
                "2020-12-26T14:59:08.714701",
                "2020-12-26T15:13:08.714701",
                "2020-12-26T15:26:08.714701",
                "2020-12-26T15:40:08.714701",
                "2020-12-26T15:55:08.714701",
                "2020-12-26T16:09:08.714701",
                "2020-12-26T16:24:08.714701",
                "2020-12-26T16:36:08.714701",
                "2020-12-26T16:49:08.714701",
                "2020-12-26T17:04:08.714701",
                "2020-12-26T17:18:08.714701",
                "2020-12-26T17:32:08.714701",
                "2020-12-26T17:46:08.714701",
                "2020-12-26T18:00:08.714701",
                "2020-12-26T18:13:08.714701",
                "2020-12-26T18:26:08.714701",
                "2020-12-26T18:38:08.714701",
                "2020-12-26T18:50:08.714701",
                "2020-12-26T19:02:08.714701",
                "2020-12-26T19:16:08.714701",
                "2020-12-26T19:30:08.714701",
                "2020-12-26T19:43:08.714701",
                "2020-12-26T19:56:08.714701",
                "2020-12-26T20:08:08.714701",
                "2020-12-26T20:22:08.714701"
              ],
              "values": [
                10,
                8,
                7,
                8,
                6,
                7,
                5,
                9,
                10,
                9,
                7,
                10,
                8,
                5,
                6,
                6,
                6,
                5,
                7,
                9,
                5,
                5,
                5,
                7,
                8,
                6,
                10,
                10,
                8,
                8
              ]
            },
            "energy_consumption": {
              "previous": 1428,
              "current": 1820,
              "usage": 392,
              "dates": {
                "dates": [
                  "2020-12-27T14:42:33.379395",
                  "2020-12-27T14:54:33.379395",
                  "2020-12-27T15:09:33.379395",
                  "2020-12-27T15:22:33.379395",
                  "2020-12-27T15:36:33.379395",
                  "2020-12-27T15:48:33.379395",
                  "2020-12-27T16:01:33.379395",
                  "2020-12-27T16:15:33.379395",
                  "2020-12-27T16:28:33.379395",
                  "2020-12-27T16:40:33.379395",
                  "2020-12-27T16:55:33.379395",
                  "2020-12-27T17:09:33.379395",
                  "2020-12-27T17:24:33.379395",
                  "2020-12-27T17:39:33.379395",
                  "2020-12-27T17:53:33.379395",
                  "2020-12-27T18:05:33.379395",
                  "2020-12-27T18:20:33.379395",
                  "2020-12-27T18:35:33.379395",
                  "2020-12-27T18:50:33.379395",
                  "2020-12-27T19:04:33.379395",
                  "2020-12-27T19:19:33.379395",
                  "2020-12-27T19:32:33.379395",
                  "2020-12-27T19:45:33.379395",
                  "2020-12-27T19:57:33.379395",
                  "2020-12-27T20:09:33.379395",
                  "2020-12-27T20:23:33.379395",
                  "2020-12-27T20:38:33.379395",
                  "2020-12-27T20:52:33.379395",
                  "2020-12-27T21:04:33.379395",
                  "2020-12-27T21:17:33.379395"
                ],
                "units": ""
              },
              "energy_consumption_values": {
                "value": [
                  344,
                  301,
                  349,
                  352,
                  323,
                  373,
                  377,
                  379,
                  348,
                  317,
                  377,
                  319,
                  342,
                  338,
                  364,
                  367,
                  312,
                  343,
                  325,
                  364,
                  346,
                  300,
                  306,
                  322,
                  305,
                  353,
                  313,
                  338,
                  337,
                  349
                ],
                "units": "kWh"
              }
            },
            "billing": {
              "totals": {
                "previous_total": {
                  "usage_kwh": 24000,
                  "value_naira": 5000000
                },
                "present_total": {
                  "usage_kwh": 2000,
                  "value_naira": 5000000
                }
              },
              "consumption_kwh": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  311,
                  353,
                  305,
                  366,
                  328,
                  345,
                  376,
                  302,
                  344,
                  312,
                  305,
                  380,
                  327,
                  360,
                  365,
                  376,
                  318,
                  333,
                  324,
                  364,
                  359,
                  316,
                  362,
                  346,
                  379,
                  377,
                  328,
                  339,
                  303,
                  329
                ]
              },
              "consumption_naira": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  326,
                  372,
                  335,
                  361,
                  310,
                  325,
                  301,
                  326,
                  369,
                  337,
                  379,
                  330,
                  320,
                  301,
                  363,
                  301,
                  302,
                  370,
                  355,
                  305,
                  345,
                  355,
                  353,
                  328,
                  361,
                  347,
                  342,
                  369,
                  367,
                  351
                ]
              }
            },
            "id": 2
          },
          {
            "name": "Gen2",
            "dashboard": {
              "total_kwh": {
                "unit": "kWh",
                "value": 1000
              },
              "min_demand": {
                "unit": "kW",
                "value": 200
              },
              "max_demand": {
                "unit": "kW",
                "value": 300
              },
              "avg_demand": {
                "unit": "kW",
                "value": 150
              },
              "dashboard_carbon_emissions": {
                "unit": "Tons",
                "value": 34.25
              },
              "cost_of_energy": {
                "unit": "Naira",
                "value": 4000
              },
              "today": {
                "value": 150,
                "unit": "kWh"
              },
              "yesterday": {
                "value": 3000,
                "unit": "kWh"
              }
            },
            "score_card": {
              "is_generator": True,
              "baseline_energy": {
                "unit": "kWh",
                "forecast": 1000,
                "used": 500
              },
              "peak_to_avg_power_ratio": {
                "unit": "kW",
                "peak": 1000,
                "avg": 500,
                "message": "Not so efficient, Higher is better",
                "message_color": "green"
              },
              "score_card_carbon_emissions": {
                "unit": "Tons",
                "estimated_value": 110,
                "actual_value": 50,
                "message": "Equivalent to 15 Acacia trees"
              },
              "generator_size_efficiency": {
                "size": "50kva",
                "usage": 20,
                "unit": "%"
              },
              "change_over_lags": {
                "data": [
                  {
                    "date": "Jan 1 2020",
                    "lag_duration": 15,
                    "diesel_cost": 20,
                    "diesel_value": 4800,
                    "id": 1
                  },
                  {
                    "date": "Jan 2 2020",
                    "lag_duration": 14,
                    "diesel_cost": 10,
                    "diesel_value": 2400,
                    "id": 2
                  },
                  {
                    "date": "Jan 3 2020",
                    "lag_duration": 60,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 3
                  },
                  {
                    "date": "Jan 4 2020",
                    "lag_duration": 120,
                    "diesel_cost": 30,
                    "diesel_value": 7200,
                    "id": 4
                  },
                  {
                    "date": "Jan 5 2020",
                    "lag_duration": 90,
                    "diesel_cost": 15,
                    "diesel_value": 3600,
                    "id": 5
                  }
                ],
                "units": {
                  "lag_duration": "Minutes",
                  "diesel_cost": "Litres"
                }
              },
              "operating_time": {
                "chart": {
                  "dates": [
                    "Jan 1",
                    "Jan 2",
                    "Jan 3",
                    "Jan 4",
                    "Jan 5",
                    "Jan 6",
                    "Jan 7",
                    "Jan 8",
                    "Jan 9",
                    "Jan 10",
                    "Jan 11",
                    "Jan 12",
                    "Jan 13",
                    "Jan 14",
                    "Jan 15",
                    "Jan 16",
                    "Jan 17",
                    "Jan 18",
                    "Jan 19",
                    "Jan 10",
                    "Jan 21",
                    "Jan 22",
                    "Jan 23",
                    "Jan 24",
                    "Jan 25",
                    "Jan 26",
                    "Jan 27",
                    "Jan 28",
                    "Jan 29",
                    "Jan 30",
                    "Jan 31"
                  ],
                  "values": [
                    199,
                    78,
                    52,
                    140,
                    111,
                    44,
                    186,
                    122,
                    12,
                    158,
                    174,
                    51,
                    218,
                    88,
                    196,
                    154,
                    70,
                    47,
                    54,
                    189,
                    17,
                    217,
                    25,
                    164,
                    243,
                    20,
                    5,
                    39,
                    227,
                    168,
                    161
                  ]
                },
                "estimated_time_wasted": {
                  "unit": "hours",
                  "value": 12
                },
                "estimated_diesel_wasted": {
                  "unit": "Litres",
                  "value": 300
                },
                "estimated_cost": {
                  "unit": "Naira",
                  "value": 12000
                }
              },
              "fuel_consumption": {
                "size": "50kva",
                "diesel_usage": False,
                "time_used": "13",
                "hours_to_maintenance": {
                  "hours": 23,
                  "unit": "hours"
                }
              }
            },
            "power_quality": {
              "dates": {
                "dates": [
                  "2020-12-14T20:14:57.329485",
                  "2020-12-14T20:26:57.329485",
                  "2020-12-14T20:41:57.329485",
                  "2020-12-14T20:54:57.329485",
                  "2020-12-14T21:07:57.329485",
                  "2020-12-14T21:22:57.329485",
                  "2020-12-14T21:35:57.329485",
                  "2020-12-14T21:50:57.329485",
                  "2020-12-14T22:02:57.329485",
                  "2020-12-14T22:16:57.329485",
                  "2020-12-14T22:30:57.329485"
                ],
                "units": ""
              },
              "voltage": {
                "l1": [223, 238, 226, 229, 238, 235, 237, 220, 236, 233, 224],
                "l2": [221, 232, 238, 235, 230, 223, 227, 230, 221, 240, 234],
                "l3": [228, 238, 220, 238, 227, 232, 224, 236, 232, 231, 225],
                "neutral": [18, 9, 6, 10, 15, 10, 18, 5, 13, 7, 20],
                "units": "volts"
              },
              "current": {
                "l1": [180, 163, 170, 166, 168, 167, 161, 172, 155, 180, 170],
                "l2": [156, 151, 166, 180, 172, 150, 168, 155, 176, 160, 175],
                "l3": [179, 153, 162, 171, 163, 172, 158, 153, 158, 178, 156],
                "neutral": [19, 20, 20, 11, 19, 11, 13, 8, 14, 15, 14],
                "units": "amps"
              },
              "active_power": {
                "l1": [62, 69, 69, 55, 67, 62, 68, 61, 56, 56, 55],
                "l2": [62, 58, 69, 65, 69, 67, 59, 66, 60, 61, 60],
                "l3": [56, 68, 66, 57, 67, 67, 68, 56, 69, 66, 63],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kW"
              },
              "reactive_power": {
                "l1": [70, 71, 74, 63, 65, 54, 57, 78, 52, 81, 67],
                "l2": [72, 77, 79, 89, 77, 59, 50, 81, 69, 60, 50],
                "l3": [83, 50, 54, 50, 88, 78, 53, 83, 63, 65, 60],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kVar"
              },
              "energy": {
                "l1": [312, 350, 339, 376, 374, 367, 319, 370, 300, 312, 367],
                "l2": [355, 329, 306, 364, 364, 348, 328, 359, 361, 314, 357],
                "l3": [339, 367, 327, 368, 352, 337, 353, 313, 372, 314, 322],
                "neutral": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "units": "kWh"
              },
              "frequency": {
                "average": [67, 30, 44, 70, 22, 33, 69, 78, 40, 66, 45],
                "units": "hz"
              },
              "power_factor": {
                "l1_l2_l3": [
                  0.94,
                  0.99,
                  0.93,
                  0.95,
                  0.87,
                  0.91,
                  0.92,
                  0.81,
                  0.96,
                  0.9,
                  0.86
                ],
                "units": ""
              }
            },
            "last_reading": {
              "date": "2020-12-18T09:27:32.394646",
              "data": {
                "phase_measures": [
                  {
                    "name": "voltage",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "volts"
                  },
                  {
                    "name": "current",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "amps"
                  },
                  {
                    "name": "active_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "l1": 234,
                    "l2": 220,
                    "l3": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf",
                    "l1": 0.9,
                    "l2": 0.89,
                    "l3": 0.89,
                    "unit": "pf"
                  }
                ],
                "totals": [
                  {
                    "name": "active_power",
                    "value": 231,
                    "unit": "kW"
                  },
                  {
                    "name": "reactive_power",
                    "value": 231,
                    "unit": "kVAR"
                  },
                  {
                    "name": "apparent_power",
                    "value": 231,
                    "unit": "kVA"
                  },
                  {
                    "name": "frequency",
                    "value": 231,
                    "unit": "hz"
                  },
                  {
                    "name": "pf",
                    "value": 0.8,
                    "unit": "pf"
                  },
                  {
                    "name": "neutral_current",
                    "value": 70,
                    "unit": "A"
                  }
                ],
                "harmonic_distortion": [
                  {
                    "name": "voltage_thd_ln",
                    "l1": 0.7,
                    "l2": 0.7,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_thd",
                    "l1": 18,
                    "l2": 15.1,
                    "l3": 0.7,
                    "unit": "%"
                  },
                  {
                    "name": "current_tdd",
                    "l1": 36,
                    "l2": 3.6,
                    "l3": 1.6,
                    "unit": "%"
                  }
                ],
                "energy": [
                  {
                    "name": "active_energy",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "active_energy_export",
                    "value": 70,
                    "unit": "kWh"
                  },
                  {
                    "name": "reactive_energy",
                    "value": 70,
                    "unit": "kvarh"
                  },
                  {
                    "name": "apparent_energy",
                    "value": 70,
                    "unit": "kvah"
                  }
                ],
                "demands": [
                  {
                    "name": "max_amp",
                    "l1": 70,
                    "l2": 70,
                    "l3": 70,
                    "unit": "A"
                  }
                ],
                "total_demands": [
                  {
                    "name": "max_power_demand",
                    "value": 1270,
                    "unit": "A"
                  },
                  {
                    "name": "accumulated_power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "max_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "power_demand",
                    "value": 470,
                    "unit": "kW"
                  },
                  {
                    "name": "accumulated_kva_demand",
                    "value": 470,
                    "unit": "kVA"
                  },
                  {
                    "name": "pf_import_at_sliding_winow",
                    "value": 0.9,
                    "unit": "pf"
                  }
                ]
              }
            },
            "power_demand": {
              "dates": {
                "dates": [
                  "2020-12-26T13:53:19.525251",
                  "2020-12-26T14:06:19.525251",
                  "2020-12-26T14:21:19.525251",
                  "2020-12-26T14:35:19.525251",
                  "2020-12-26T14:47:19.525251",
                  "2020-12-26T15:01:19.525251",
                  "2020-12-26T15:14:19.525251",
                  "2020-12-26T15:26:19.525251",
                  "2020-12-26T15:41:19.525251",
                  "2020-12-26T15:54:19.525251",
                  "2020-12-26T16:06:19.525251",
                  "2020-12-26T16:18:19.525251",
                  "2020-12-26T16:30:19.525251",
                  "2020-12-26T16:42:19.525251",
                  "2020-12-26T16:57:19.525251",
                  "2020-12-26T17:10:19.525251",
                  "2020-12-26T17:22:19.525251",
                  "2020-12-26T17:34:19.525251",
                  "2020-12-26T17:48:19.525251",
                  "2020-12-26T18:03:19.525251",
                  "2020-12-26T18:16:19.525251",
                  "2020-12-26T18:31:19.525251",
                  "2020-12-26T18:46:19.525251",
                  "2020-12-26T19:00:19.525251",
                  "2020-12-26T19:14:19.525251",
                  "2020-12-26T19:28:19.525251",
                  "2020-12-26T19:43:19.525251",
                  "2020-12-26T19:55:19.525251",
                  "2020-12-26T20:10:19.525251",
                  "2020-12-26T20:22:19.525251"
                ],
                "units": ""
              },
              "power_demand_values": {
                "demand": [
                  59,
                  67,
                  62,
                  65,
                  63,
                  70,
                  64,
                  68,
                  61,
                  67,
                  61,
                  60,
                  64,
                  69,
                  69,
                  56,
                  55,
                  57,
                  67,
                  62,
                  65,
                  70,
                  56,
                  61,
                  62,
                  57,
                  59,
                  69,
                  57,
                  64
                ],
                "min": [
                  63,
                  67,
                  57,
                  61,
                  58,
                  70,
                  67,
                  66,
                  63,
                  67,
                  60,
                  63,
                  65,
                  60,
                  65,
                  65,
                  61,
                  67,
                  67,
                  67,
                  64,
                  69,
                  68,
                  61,
                  57,
                  59,
                  68,
                  68,
                  68,
                  62
                ],
                "max": [
                  65,
                  63,
                  61,
                  70,
                  62,
                  66,
                  60,
                  69,
                  64,
                  57,
                  63,
                  55,
                  61,
                  69,
                  60,
                  70,
                  57,
                  70,
                  70,
                  57,
                  59,
                  69,
                  56,
                  68,
                  63,
                  60,
                  64,
                  69,
                  60,
                  65
                ],
                "avg": [
                  70,
                  63,
                  62,
                  61,
                  55,
                  70,
                  57,
                  59,
                  57,
                  70,
                  60,
                  60,
                  68,
                  60,
                  61,
                  59,
                  67,
                  55,
                  65,
                  56,
                  56,
                  66,
                  70,
                  62,
                  57,
                  65,
                  70,
                  67,
                  61,
                  64
                ],
                "units": "kW"
              }
            },
            "time_of_use": {
              "dates": [
                "2020-12-26T13:52:08.714701",
                "2020-12-26T14:06:08.714701",
                "2020-12-26T14:19:08.714701",
                "2020-12-26T14:34:08.714701",
                "2020-12-26T14:47:08.714701",
                "2020-12-26T15:01:08.714701",
                "2020-12-26T15:16:08.714701",
                "2020-12-26T15:31:08.714701",
                "2020-12-26T15:44:08.714701",
                "2020-12-26T15:57:08.714701",
                "2020-12-26T16:11:08.714701",
                "2020-12-26T16:24:08.714701",
                "2020-12-26T16:37:08.714701",
                "2020-12-26T16:50:08.714701",
                "2020-12-26T17:05:08.714701",
                "2020-12-26T17:17:08.714701",
                "2020-12-26T17:29:08.714701",
                "2020-12-26T17:43:08.714701",
                "2020-12-26T17:55:08.714701",
                "2020-12-26T18:09:08.714701",
                "2020-12-26T18:23:08.714701",
                "2020-12-26T18:38:08.714701",
                "2020-12-26T18:50:08.714701",
                "2020-12-26T19:03:08.714701",
                "2020-12-26T19:18:08.714701",
                "2020-12-26T19:32:08.714701",
                "2020-12-26T19:46:08.714701",
                "2020-12-26T19:59:08.714701",
                "2020-12-26T20:13:08.714701",
                "2020-12-26T20:27:08.714701"
              ],
              "values": [
                6,
                6,
                9,
                8,
                8,
                10,
                9,
                5,
                9,
                8,
                6,
                5,
                5,
                6,
                8,
                9,
                9,
                9,
                8,
                10,
                8,
                6,
                6,
                6,
                7,
                8,
                5,
                6,
                5,
                8
              ]
            },
            "energy_consumption": {
              "previous": 1468,
              "current": 2126,
              "usage": 658,
              "dates": {
                "dates": [
                  "2020-12-27T14:42:33.379395",
                  "2020-12-27T14:54:33.379395",
                  "2020-12-27T15:09:33.379395",
                  "2020-12-27T15:22:33.379395",
                  "2020-12-27T15:36:33.379395",
                  "2020-12-27T15:48:33.379395",
                  "2020-12-27T16:01:33.379395",
                  "2020-12-27T16:15:33.379395",
                  "2020-12-27T16:28:33.379395",
                  "2020-12-27T16:40:33.379395",
                  "2020-12-27T16:55:33.379395",
                  "2020-12-27T17:09:33.379395",
                  "2020-12-27T17:24:33.379395",
                  "2020-12-27T17:39:33.379395",
                  "2020-12-27T17:53:33.379395",
                  "2020-12-27T18:05:33.379395",
                  "2020-12-27T18:20:33.379395",
                  "2020-12-27T18:35:33.379395",
                  "2020-12-27T18:50:33.379395",
                  "2020-12-27T19:04:33.379395",
                  "2020-12-27T19:19:33.379395",
                  "2020-12-27T19:32:33.379395",
                  "2020-12-27T19:45:33.379395",
                  "2020-12-27T19:57:33.379395",
                  "2020-12-27T20:09:33.379395",
                  "2020-12-27T20:23:33.379395",
                  "2020-12-27T20:38:33.379395",
                  "2020-12-27T20:52:33.379395",
                  "2020-12-27T21:04:33.379395",
                  "2020-12-27T21:17:33.379395"
                ],
                "units": ""
              },
              "energy_consumption_values": {
                "value": [
                  326,
                  305,
                  372,
                  375,
                  367,
                  329,
                  380,
                  301,
                  330,
                  303,
                  302,
                  310,
                  357,
                  349,
                  321,
                  310,
                  312,
                  329,
                  318,
                  358,
                  314,
                  317,
                  366,
                  348,
                  319,
                  328,
                  317,
                  368,
                  321,
                  348
                ],
                "units": "kWh"
              }
            },
            "billing": {
              "totals": {
                "previous_total": {
                  "usage_kwh": 24000,
                  "value_naira": 5000000
                },
                "present_total": {
                  "usage_kwh": 2000,
                  "value_naira": 5000000
                }
              },
              "consumption_kwh": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  336,
                  347,
                  320,
                  302,
                  336,
                  336,
                  331,
                  364,
                  305,
                  322,
                  304,
                  366,
                  318,
                  349,
                  311,
                  377,
                  309,
                  326,
                  322,
                  353,
                  379,
                  309,
                  342,
                  315,
                  340,
                  348,
                  358,
                  338,
                  375,
                  379
                ]
              },
              "consumption_naira": {
                "dates": [
                  "2021-01-09T08:21:27.255872",
                  "2021-01-09T08:35:27.255872",
                  "2021-01-09T08:49:27.255872",
                  "2021-01-09T09:04:27.255872",
                  "2021-01-09T09:18:27.255872",
                  "2021-01-09T09:30:27.255872",
                  "2021-01-09T09:45:27.255872",
                  "2021-01-09T09:57:27.255872",
                  "2021-01-09T10:10:27.255872",
                  "2021-01-09T10:24:27.255872",
                  "2021-01-09T10:39:27.255872",
                  "2021-01-09T10:52:27.255872",
                  "2021-01-09T11:05:27.255872",
                  "2021-01-09T11:19:27.255872",
                  "2021-01-09T11:33:27.255872",
                  "2021-01-09T11:46:27.255872",
                  "2021-01-09T11:59:27.255872",
                  "2021-01-09T12:13:27.255872",
                  "2021-01-09T12:26:27.255872",
                  "2021-01-09T12:40:27.255872",
                  "2021-01-09T12:52:27.255872",
                  "2021-01-09T13:06:27.255872",
                  "2021-01-09T13:18:27.255872",
                  "2021-01-09T13:30:27.255872",
                  "2021-01-09T13:42:27.255872",
                  "2021-01-09T13:56:27.255872",
                  "2021-01-09T14:08:27.255872",
                  "2021-01-09T14:21:27.255872",
                  "2021-01-09T14:36:27.255872",
                  "2021-01-09T14:48:27.255872"
                ],
                "values": [
                  324,
                  341,
                  344,
                  326,
                  366,
                  332,
                  301,
                  306,
                  307,
                  367,
                  335,
                  331,
                  330,
                  348,
                  311,
                  357,
                  378,
                  342,
                  308,
                  343,
                  305,
                  322,
                  340,
                  334,
                  353,
                  346,
                  363,
                  346,
                  307,
                  332
                ]
              }
            },
            "id": 3
          }
        ],
        "daily_kwh": {
          "dates": [
            "Jan 1",
            "Jan 2",
            "Jan 3",
            "Jan 4",
            "Jan 5",
            "Jan 6",
            "Jan 7",
            "Jan 8",
            "Jan 9",
            "Jan 10",
            "Jan 11",
            "Jan 12",
            "Jan 13",
            "Jan 14",
            "Jan 15",
            "Jan 16",
            "Jan 17",
            "Jan 18",
            "Jan 19",
            "Jan 10",
            "Jan 21",
            "Jan 22",
            "Jan 23",
            "Jan 24",
            "Jan 25",
            "Jan 26",
            "Jan 27",
            "Jan 28",
            "Jan 29",
            "Jan 30",
            "Jan 31"
          ],
          "Utility": [
            203,
            852,
            863,
            355,
            290,
            664,
            315,
            866,
            828,
            102,
            728,
            267,
            138,
            848,
            330,
            78,
            207,
            164,
            426,
            702,
            262,
            822,
            63,
            889,
            278,
            547,
            844,
            794,
            227,
            857,
            729
          ],
          "Gen1": [
            203,
            852,
            863,
            355,
            290,
            664,
            315,
            866,
            828,
            102,
            728,
            267,
            138,
            848,
            330,
            78,
            207,
            164,
            426,
            702,
            262,
            822,
            63,
            889,
            278,
            547,
            844,
            794,
            227,
            857,
            729
          ],
          "Gen2": [
            203,
            852,
            863,
            355,
            290,
            664,
            315,
            866,
            828,
            102,
            728,
            267,
            138,
            848,
            330,
            78,
            207,
            164,
            426,
            702,
            262,
            822,
            63,
            889,
            278,
            547,
            844,
            794,
            227,
            857,
            729
          ]
        },
        "usage_hours": {
          "devices": ["Utility", "Gen1", "Gen2"],
          "hours": [23, 10, 94]
        },
        "time_of_use_table": {
          "dates": {
            "dates": [
              "2020-12-26T13:50:08.705702",
              "2020-12-26T14:05:08.705702",
              "2020-12-26T14:18:08.705702",
              "2020-12-26T14:33:08.705702",
              "2020-12-26T14:46:08.705702",
              "2020-12-26T15:01:08.705702",
              "2020-12-26T15:14:08.705702",
              "2020-12-26T15:29:08.705702",
              "2020-12-26T15:42:08.705702",
              "2020-12-26T15:55:08.705702",
              "2020-12-26T16:08:08.705702",
              "2020-12-26T16:21:08.705702",
              "2020-12-26T16:35:08.705702",
              "2020-12-26T16:47:08.705702",
              "2020-12-26T16:59:08.705702",
              "2020-12-26T17:14:08.705702",
              "2020-12-26T17:26:08.705702",
              "2020-12-26T17:38:08.705702",
              "2020-12-26T17:53:08.705702",
              "2020-12-26T18:05:08.705702",
              "2020-12-26T18:17:08.705702",
              "2020-12-26T18:29:08.705702",
              "2020-12-26T18:43:08.705702",
              "2020-12-26T18:55:08.705702",
              "2020-12-26T19:09:08.705702",
              "2020-12-26T19:24:08.705702",
              "2020-12-26T19:38:08.705702",
              "2020-12-26T19:50:08.705702",
              "2020-12-26T20:02:08.705702",
              "2020-12-26T20:15:08.705702"
            ],
            "units": ""
          },
          "values": {
            "utility": [
              9,
              5,
              6,
              5,
              5,
              5,
              10,
              8,
              9,
              10,
              10,
              6,
              6,
              6,
              10,
              10,
              9,
              5,
              7,
              7,
              8,
              7,
              6,
              5,
              7,
              7,
              7,
              6,
              10,
              6
            ],
            "gen1": [
              8,
              10,
              5,
              7,
              10,
              7,
              10,
              10,
              6,
              7,
              8,
              6,
              9,
              6,
              7,
              5,
              7,
              9,
              8,
              9,
              10,
              10,
              8,
              8,
              7,
              9,
              7,
              9,
              5,
              7
            ],
            "gen2": [
              6,
              8,
              7,
              8,
              6,
              9,
              5,
              7,
              6,
              6,
              6,
              6,
              6,
              10,
              10,
              8,
              9,
              6,
              9,
              10,
              10,
              9,
              6,
              9,
              8,
              9,
              5,
              10,
              9,
              5
            ],
            "active_gen": [
              "utility",
              "gen1",
              "gen2",
              "utility",
              "utility",
              "utility",
              "gen2",
              "gen2",
              "gen2",
              "gen1",
              "gen2",
              "gen1",
              "gen1",
              "utility",
              "gen1",
              "gen1",
              "utility",
              "utility",
              "gen1",
              "utility",
              "gen1",
              "gen1",
              "gen1",
              "gen1",
              "utility",
              "gen1",
              "gen2",
              "gen1",
              "gen1",
              "gen2"
            ],
            "time_on": [
              "05:33 AM",
              "11:50 AM",
              "02:51 AM",
              "12:11 PM",
              "11:57 AM",
              "12:03 AM",
              "12:11 PM",
              "10:04 AM",
              "07:13 AM",
              "09:58 AM",
              "12:22 PM",
              "09:53 AM",
              "10:29 AM",
              "05:58 AM",
              "05:02 AM",
              "12:21 AM",
              "07:02 AM",
              "02:21 AM",
              "02:50 AM",
              "03:25 AM",
              "11:21 AM",
              "06:58 AM",
              "10:44 AM",
              "03:56 AM",
              "04:28 AM",
              "04:31 AM",
              "05:55 AM",
              "06:17 AM",
              "09:06 AM",
              "08:34 AM"
            ],
            "time_off": [
              "07:15 AM",
              "06:05 AM",
              "07:38 AM",
              "03:50 AM",
              "11:10 AM",
              "12:56 AM",
              "11:08 AM",
              "04:51 AM",
              "03:35 AM",
              "12:20 PM",
              "10:50 AM",
              "10:36 AM",
              "08:27 AM",
              "08:14 AM",
              "11:10 AM",
              "09:42 AM",
              "06:03 AM",
              "09:31 AM",
              "06:37 AM",
              "10:54 AM",
              "12:10 AM",
              "12:45 PM",
              "09:03 AM",
              "12:55 AM",
              "01:04 AM",
              "12:58 PM",
              "03:32 AM",
              "01:21 AM",
              "11:04 AM",
              "01:36 AM"
            ],
            "hours_of_use": [
              "11:11 AM",
              "08:07 AM",
              "07:58 AM",
              "01:04 AM",
              "03:25 AM",
              "06:26 AM",
              "10:16 AM",
              "11:32 AM",
              "09:02 AM",
              "09:15 AM",
              "12:50 PM",
              "04:43 AM",
              "06:47 AM",
              "11:21 AM",
              "06:15 AM",
              "09:58 AM",
              "10:53 AM",
              "06:57 AM",
              "10:28 AM",
              "11:51 AM",
              "12:13 AM",
              "09:22 AM",
              "12:01 AM",
              "06:40 AM",
              "10:14 AM",
              "04:56 AM",
              "11:02 AM",
              "07:32 AM",
              "09:58 AM",
              "10:01 AM"
            ]
          }
        },
        "cost_tracker_qty_of_diesel": {
          "dates": [
            "2021-01-03T10:41:11.452244",
            "2021-01-03T10:53:11.452244",
            "2021-01-03T11:06:11.452244",
            "2021-01-03T11:20:11.452244",
            "2021-01-03T11:35:11.452244",
            "2021-01-03T11:49:11.452244",
            "2021-01-03T12:01:11.452244",
            "2021-01-03T12:15:11.452244",
            "2021-01-03T12:28:11.452244",
            "2021-01-03T12:40:11.452244",
            "2021-01-03T12:52:11.452244",
            "2021-01-03T13:06:11.452244",
            "2021-01-03T13:21:11.452244",
            "2021-01-03T13:36:11.452244",
            "2021-01-03T13:51:11.452244",
            "2021-01-03T14:06:11.452244",
            "2021-01-03T14:21:11.452244",
            "2021-01-03T14:36:11.452244",
            "2021-01-03T14:51:11.452244",
            "2021-01-03T15:06:11.452244"
          ],
          "values": [
            311,
            323,
            353,
            376,
            350,
            310,
            364,
            302,
            375,
            330,
            359,
            358,
            325,
            318,
            335,
            315,
            327,
            309,
            322,
            301
          ],
          "units": "litres"
        },
        "cost_tracker_monthly_cost": {
          "dates": ["Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug"],
          "values": [
            216542,
            120255,
            451496,
            172617,
            493132,
            230306,
            120608,
            445166
          ],
          "units": "Naira"
        },
        "cost_tracker_consumption_breakdown": {
          "dates": [
            "2021-01-03T10:41:11.452244",
            "2021-01-03T10:53:11.452244",
            "2021-01-03T11:06:11.452244",
            "2021-01-03T11:20:11.452244",
            "2021-01-03T11:35:11.452244",
            "2021-01-03T11:49:11.452244",
            "2021-01-03T12:01:11.452244",
            "2021-01-03T12:15:11.452244",
            "2021-01-03T12:28:11.452244",
            "2021-01-03T12:40:11.452244",
            "2021-01-03T12:52:11.452244",
            "2021-01-03T13:06:11.452244",
            "2021-01-03T13:21:11.452244",
            "2021-01-03T13:36:11.452244",
            "2021-01-03T13:51:11.452244",
            "2021-01-03T14:06:11.452244",
            "2021-01-03T14:21:11.452244",
            "2021-01-03T14:36:11.452244",
            "2021-01-03T14:51:11.452244",
            "2021-01-03T15:06:11.452244"
          ],
          "gen1": [
            136178,
            184450,
            274607,
            464500,
            319799,
            407092,
            256008,
            301218,
            334815,
            386142,
            291507,
            487042,
            267900,
            107536,
            138745,
            325639,
            202577,
            492487,
            225815,
            186782
          ],
          "gen2": [
            445916,
            187186,
            315449,
            408653,
            403977,
            363605,
            369188,
            357796,
            417820,
            161999,
            183815,
            420018,
            146487,
            152836,
            495514,
            315529,
            312372,
            267142,
            328836,
            490496
          ],
          "units": "litres"
        },
        "billing_totals": {
          "previous_total": {
            "usage_kwh": 24000,
            "value_naira": 5000000
          },
          "present_total": {
            "usage_kwh": 24000,
            "value_naira": 5000000
          },
          "metrics": {
            "diesel_per_kwh": 70,
            "utility_per_kwh": 30,
            "blended_cost_per_kwh": 60,
            "unit": "₦"
          },
          "usage": {
            "previous_kwh": 202311,
            "present_kwh": 20311,
            "total_usage_kwh": 182000
          }
        },
        "id": 2
      }
    ],
    "id": 1
  }
}
