import pandas as pd
from datetime import timedelta

def aggregate_usage_from_readings(readings):

    df = pd.DataFrame(readings)

    # Convert 'post_datetime' to datetime
    df['post_datetime'] = pd.to_datetime(df['post_datetime'])

    # Sort the DataFrame by 'post_datetime'
    df = df.sort_values(by='post_datetime')

    # Extract date and create a new column
    df['date'] = df['post_datetime'].dt.date

    # Get the last reading of each day
    last_readings = df.groupby('date')['kwh_import'].first().reset_index()

    # Shift the last readings to align with the next day
    last_readings['next_day_import'] = last_readings['kwh_import'].shift(-1)
    last_readings['next_day_import_time'] = last_readings['date'].shift(-1)

    # Drop the last row because it doesn't have a next day reading
    last_readings = last_readings[:-1]

    # Calculate daily consumption
    last_readings['daily_kwh_import'] = last_readings['next_day_import'] - last_readings['kwh_import']

    # Select only the relevant columns
    daily_consumption = last_readings[['date', 'daily_kwh_import']]

    return daily_consumption

def aggregate_time_of_use(readings, start=8, end=17):

    df = pd.DataFrame(readings)

    # Ensure 'post_datetime' is in datetime format
    df['post_datetime'] = pd.to_datetime(df['post_datetime'])

    # Calculate total power
    df['total_power'] = df['kw_l1'] + df['kw_l2'] + df['kw_l3']

    # Filter rows where the generator was running (total power > 0)
    df_operating = df # df[df['total_power'] > 0]

    # Sort the dataframe by 'post_datetime' to calculate time differences correctly
    df_operating = df_operating.sort_values(by='post_datetime')

    # Calculate the time difference between consecutive readings in minutes
    df_operating['time_diff'] = df_operating['post_datetime'].diff().dt.total_seconds() / 60.0
    df_operating['energy_diff'] = df_operating['kwh_import'].diff()

    # Filter out the rows where the time difference is greater than 30 minutes
    df_operating = df_operating[(df_operating['time_diff'] <= 21)]

    # Extract the date and hour from 'post_datetime'
    df_operating['date'] = df_operating['post_datetime'].dt.date.astype(str)
    df_operating['hour'] = df_operating['post_datetime'].dt.hour

    # Filter for work hours (between 8 AM and 5 PM)
    df_operating = df_operating[(df_operating['hour'] >= start) & (df_operating['hour'] <= end)]

    # Group by date and count unique hours the generator was operating
    hours_operated = df_operating.groupby(df_operating.date.astype(str)).sum("time_diff").reset_index()
    hours_operated['time_diff_H'] = (hours_operated['time_diff']/60).round(1)

    hours_operated[['date', 'time_diff', 'time_diff_H', 'energy_diff']]

    def format_minutes(td):

        if pd.isnull(td):
            return "0 hrs 0 mins"
        total_seconds = int(td*60*60)
        hours, remainder = divmod(total_seconds, 3600)
        minutes, _ = divmod(remainder, 60)

        return f"{hours} hrs {minutes} mins"

    hours_operated['daily_usage_hours'] = hours_operated['time_diff_H'].apply(format_minutes)
    hours_operated = hours_operated[['date', 'time_diff_H', 'daily_usage_hours', 'energy_diff']]

    return hours_operated

def aggregate_time_of_use_from_readings(readings):

    df = pd.DataFrame(readings)

    # Convert 'post_datetime' to datetime
    df['post_datetime'] = pd.to_datetime(df['post_datetime'])

    # Sort the DataFrame by 'post_datetime'
    df = df.sort_values(by='post_datetime')

    # Extract date and create a new column
    df['date'] = df['post_datetime']

    # Get the last reading of each day
    last_readings = df.groupby('date')['kwh_import'].first().reset_index()

    # Shift the last readings to align with the next day
    last_readings['next_day_import'] = last_readings['kwh_import'].shift(-1)
    last_readings['next_day_import_time'] = last_readings['date'].shift(-1)

    # Drop the last row because it doesn't have a next day reading
    # last_readings = last_readings[:-1]

    # Calculate daily consumption
    last_readings['daily_kwh_import'] = last_readings['next_day_import'] - last_readings['kwh_import']
    last_readings['daily_import_time'] = last_readings['next_day_import_time'] - last_readings['date']

    last_readings = last_readings[last_readings.daily_import_time <= timedelta(minutes=20)]

    last_readings["dates"] = last_readings["date"].dt.date
    grouped_readings = last_readings.groupby('dates')['daily_import_time'].sum().reset_index()
    grouped_readings['daily_import_hours'] = grouped_readings['daily_import_time'].dt.total_seconds() / 3600

    def format_timedelta(td):
        if pd.isnull(td):
            return "0 hrs 0 mins"
        total_seconds = int(td.total_seconds())
        hours, remainder = divmod(total_seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        return f"{hours} hrs {minutes} mins"

    grouped_readings['daily_import_hours'] = grouped_readings['daily_import_time'].apply(format_timedelta)

    # Select only the relevant columns
    daily_consumption = grouped_readings[['dates', 'daily_import_hours']]

    return daily_consumption

def aggregate_time_of_use_from_readings_total(readings):

    df = pd.DataFrame(readings)

    # Convert 'post_datetime' to datetime
    df['post_datetime'] = pd.to_datetime(df['post_datetime'])

    # Sort the DataFrame by 'post_datetime'
    df = df.sort_values(by='post_datetime')

    # Extract date and create a new column
    df['date'] = df['post_datetime']

    # Get the last reading of each day
    last_readings = df.groupby('date')['kwh_import'].first().reset_index()

    # Shift the last readings to align with the next day
    last_readings['next_day_import'] = last_readings['kwh_import'].shift(-1)
    last_readings['next_day_import_time'] = last_readings['date'].shift(-1)

    # Drop the last row because it doesn't have a next day reading
    # last_readings = last_readings[:-1]

    # Calculate daily consumption
    last_readings['daily_kwh_import'] = last_readings['next_day_import'] - last_readings['kwh_import']
    last_readings['daily_import_time'] = last_readings['next_day_import_time'] - last_readings['date']

    last_readings = last_readings[last_readings.daily_import_time <= timedelta(minutes=20)]

    last_readings["dates"] = last_readings["date"].dt.date
    grouped_readings = last_readings.groupby('dates')['daily_import_time'].sum().reset_index()
    grouped_readings['daily_import_hours'] = grouped_readings['daily_import_time'].dt.total_seconds() / 3600

    def format_timedelta(td):
        if pd.isnull(td):
            return "0 hrs 0 mins"
        total_seconds = int(td.total_seconds())
        hours, remainder = divmod(total_seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        return f"{hours} hrs {minutes} mins"



    # Select only the relevant columns
    # daily_consumption = grouped_readings[['dates', 'daily_import_hours']]

    return grouped_readings['daily_import_hours'].sum()