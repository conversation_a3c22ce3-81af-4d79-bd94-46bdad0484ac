import pytz
import datetime, os, calendar
from dateutil.relativedelta import relativedelta
from datetime import date, timedelta
import jwt

### CONVERT FROM ONE TIME ZONE TO ANOTHER
def convert_timezone(_from,  to):

    # samples 'Africa/Lagos', 'US/Central'
    source_zone = pytz.timezone(_from)
    target_zone = pytz.timezone(to)
    curtime = source_zone.localize(datetime.datetime.now())
    curtime = curtime.astimezone(target_zone)

    return curtime


###  MAKE DATETIME AWARE OF SERVER TIMEZONE
def localize_time(time):

    target_zone = pytz.timezone(os.getenv("time_zone"))
    localized_time = target_zone.localize(time)

    return time


def convert_date(date):
    # RETURN DATE OBJECT FROM SET FORMAT DD-MM-YY HH:MM

    date_object = datetime.datetime.strptime(date, "%d-%m-%Y %H:%M")

    return date_object

def str_to_time(string:str)->datetime.time:
    
    datetime_object = datetime.datetime.strptime(string, '%H:%M').time()
    
    return datetime_object

def get_start_and_end_of_month_from_date(date):

    date_string = f"{date.year}-{date.month}-{date.day} 00:00:00"
    date_obj = datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")

    start_of_month = date_obj - datetime.timedelta(days = date.day-1)
    aux_end_date = start_of_month + datetime.timedelta(days = 33)
    end_date = aux_end_date - datetime.timedelta(days = aux_end_date.day - 1)

    return  start_of_month, end_date

def to_standard_date_str(date):

    date_string = f"{date.year}-{date.month}-{date.day} 00:00:00"
    date_obj = datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")

    start_of_month = date_obj - datetime.timedelta(days = date.day-1)
    aux_end_date = start_of_month + datetime.timedelta(days = 33)
    end_date = aux_end_date - datetime.timedelta(days = aux_end_date.day - 1)

    return  start_of_month, end_date

# THIS FUNCTION GETS THE CORRESPONDING DATE FOR WHICH SCORECARDS IS BEING CALCULATED SO THAT THIS VALUE CAN BE REGISTERED IN THE SCORECARDS PAGE
def score_cards_date(end_date):
    months_before = 0
    now = end_date
    from_datetime = now - relativedelta(months=months_before)
    modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    return modified_from_datetime.strftime("%B, %Y")

def previous_month(date):
    
    today = date
    first = today.replace(day=1)
    lastMonth = first #- datetime.timedelta(hours=1)
    print(lastMonth.strftime("%Y%m"))
    return lastMonth

def previous_month_new(end_date):
    # Get the first day of the current month
    first_day_of_month = end_date.replace(day=1)
    # Subtract one month from the current date
    prev_month = first_day_of_month - timedelta(days=1)
    # Get the first day of the previous month
    prev_month_first_day = prev_month.replace(day=1)
    return prev_month_first_day

def to_month_and_year(date):
    
    return date.strftime("%d, %B %Y")


def get_start_date_from(end_date, period):

    "PERIOD CAN BE (week, month, half-year, year)"
    
    if period == "week":
        start_date = end_date-datetime.timedelta(days = 7)

    elif period == "month":
        start_date = previous_month(end_date)

    elif period == "half-year":
        start_date = end_date

        for i in range(6):
            start_date = previous_month(start_date)    

    elif period == "year":
        start_date = end_date.replace(day=1, month=1, hour = 0)
    
    else:
        start_date = previous_month(end_date)

    return start_date


def daterange(start_date, end_date):
    for n in range(int((end_date - start_date).days)):
        yield start_date + timedelta(n)

def get_start_and_end_dates():
    today = datetime.datetime.now()
    end_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
    start_date = end_date - datetime.timedelta(days=1)

    start_date_str = start_date.strftime("%d-%m-%Y %H:%M")
    end_date_str = end_date.strftime("%d-%m-%Y %H:%M")

    return start_date_str, end_date_str

def convert_to_device_operating_hours(start_date, start_time, end_time):
    today = datetime.datetime.now()
    operating_hours_start = start_time
    operating_hours_end = end_time
    # Create new datetime object with today's date and the operating_hours_start time
    new_start_date = datetime.datetime.combine(today, operating_hours_start)

    # Create new datetime object with today's date and the operating_hours_end time
    new_end_date = datetime.datetime.combine(today, operating_hours_end)

    # Replace the time value of the datetime object with the operating_hours_start and end values
    new_date_time_obj_start = start_date.replace(hour=new_start_date.hour, minute=new_start_date.minute)
    # new_date_time_obj_end = end_date.replace(hour=new_end_date.hour, minute=new_end_date.minute)
    new_date_time_obj_end = start_date.replace(hour=new_end_date.hour, minute=new_end_date.minute)

    return new_date_time_obj_start, new_date_time_obj_end

def time_handler(time_string):

    time_object = datetime.datetime.strptime(time_string, '%H:%M').time()

    return time_object

def get_weekly_dates():
    # get today's date
    today = datetime.datetime.now()

    # calculate the date of the most recent Monday
    most_recent_monday = today - timedelta(days=today.weekday())

    # calculate the date of the previous Monday
    previous_monday = most_recent_monday - timedelta(days=7)

    # calculate the date of the previous Sunday
    previous_sunday = most_recent_monday - timedelta(days=1)

    # create a list of dates for the previous week, starting from the previous Monday
    dates = [previous_monday + timedelta(days=i) for i in range(7)]

    # format the dates as strings
    dates = [date.strftime('%Y-%m-%d') for date in dates]

    return dates

def convert_to_date_object(date_string):
    date_object = datetime.datetime.strptime(date_string, "%Y-%m-%d")
    return date_object.date()

def decimal_to_hours_minutes(decimal_hours):
    total_minutes = int(decimal_hours * 60)  # Convert hours to minutes
    hours = total_minutes // 60
    minutes = total_minutes % 60
    return hours, minutes

# to convert timedelta(seconds) to hours, minutes
def timedelta_to_hours_minutes(td):
    return td.seconds // 3600, (td.seconds // 60) % 60

def get_previous_month_start_and_end_dates_from_today():
    today = datetime.datetime.now()
    
    # Calculate the start of the last month by subtracting the number of days equal to the current day
    last_month = today - datetime.timedelta(days=today.day)
    
    # Construct the start date as the first day of the last month
    start_date = datetime.datetime(last_month.year, last_month.month, 1)
    
    # Determine the last day of the last month
    _, last_day = calendar.monthrange(last_month.year, last_month.month)
    
    # Construct the end date as the last second of the last day
    end_date = datetime.datetime(last_month.year, last_month.month, last_day, 23, 59, 59)
    
    # Get the month name and year separately
    month_name = start_date.strftime("%B")
    year = start_date.year
    
    return start_date, end_date, month_name, year

def generate_jwt_with_data():
    # Get the current date and time
    current_datetime = datetime.datetime.now()
    
    expiry = current_datetime + timedelta(days=5)

    # Calculate the Unix timestamp for the expiration time
    expiry_timestamp = int(expiry.timestamp())

    # Define the payload containing the data
    payload = {
        'exp': expiry_timestamp
    }

    # Generate the JWT token using a secret key
    secret_key = 'wnw5ps+nc$$l)*9&r+eqw*6-x+7pn'
    jwt_token = jwt.encode(payload, secret_key, algorithm='HS256')

    return jwt_token.decode('utf-8')

def check_jwt_date(token):
    # Define the secret key used to encode the JWT token
    secret_key = 'wnw5ps+nc$$l)*9&r+eqw*6-x+7pn'

    try:
        # Decode the JWT token
        decoded_token = jwt.decode(token, secret_key, algorithms=['HS256'])

        # Extract the expiration_time of the token from the token payload
        expiration_time = decoded_token.get('exp')

        # Compare the token expirydate with now
        now = datetime.datetime.now()

        if expiration_time < now.timestamp():
            return False
        else:
            print('(((((((((((( expiration_time ))))))))))))')
            print(expiration_time)
            print('(((((((((((( NOW ))))))))))))')
            print(now.timestamp())
            return True
        
    except jwt.InvalidTokenError as e:
        print('(((((((((((( JWT ERROR ))))))))))))')
        print(e)
        print('(((((((((((( JWT ERROR ))))))))))))')

        data = {
                'status'  : False,
                'message' : 'JWT token was not created on WYRE',
            }
        return data

def convert_hours_to_str(decimal_hours):
    hours = int(decimal_hours)
    minutes = int((decimal_hours - hours) * 60)
    return f"{hours} Hrs : {minutes} Mins"