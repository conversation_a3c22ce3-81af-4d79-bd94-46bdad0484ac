import pandas as pd
# from scipy.sparse import data
from sklearn.linear_model import LinearRegression
import numpy as np
import datetime, functools


# # SAMPLE KWATT DATA CLEANING
@functools.lru_cache(maxsize=None)
def predict_usage(values, cdd, test_cdd, num_of_required_months = 6, reproccess = True):# Reprocess True if the monthly kwatt-hour value is not yet determined
    #print(values, cdd)
    #DATA ARRANGEMENT


    dataset = pd.DataFrame({"x":cdd, "y":values})
    dataset['y'] = dataset['y'].replace(0,dataset['y'].mean())


    too_low_values = dataset["y"].quantile(0.2) # FILTER FOR VALUES LESS THAN 20% OF AVERAGE
    dataset = dataset[dataset["y"] > too_low_values]

    x = np.array(dataset["x"])#.reshape((-1, 1))
    y = np.array(dataset["y"])

    try:
        regressor = LinearRegression()
        regressor.fit(x.reshape((-1, 1)),y) #actually produces the linear eqn for the data
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print(x)
        print(y)
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")
        print("----------------------")

        # predicting the test set results
        y_pred = regressor.predict([[test_cdd]])
        y_pred
        print("y_pred",y_pred)
    except ValueError:
        y_pred = [0]

    return y_pred[0]