import os
import requests, wyre.settings

class Mailer:

    @staticmethod
    def send_simple_message( sender, title, message, receievers):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST

            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "text": message})

        if response.ok:
            print("Sending successful ")
            open("maillogs.txt", "a").write("Sending Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print("Sending failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": True,
                "message": response.content
            }

    @staticmethod
    def send_simple_message_with_attachment( sender, title, message, receievers, attachment: str):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST

            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
                3. WYRE-GENIUS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            files=[("attachment",("Report.pdf", open(attachment, "rb").read())) ],
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "text": message})

        if response.ok:
            print("Sending successful ")
            open("maillogs.txt", "a").write("Sending Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print("Sending failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": False,
                "message": response.content
            }

    @staticmethod
    def send_simple_message_html( sender, title, message, receievers):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST

            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "html": message})

        if response.ok:
            print("Sending successful ")
            open("maillogs.txt", "a").write("Sending Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print("Sending failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": True,
                "message": response.content
            }

    @staticmethod
    def send_new_registrant_notification(data):

        BASE_DIR = os.path.dirname(os.path.dirname(__file__))
        TEMPLATE_DIRS = (os.path.join(BASE_DIR, "templates"),)
        from django.shortcuts import render
        html_data = open(f"{TEMPLATE_DIRS[0]}/index.html", "r").read()
        name = "inyang"

        return requests.post(
            "https://api.mailgun.net/v3/mg.wyreng.com/messages",
            auth=("api", "**************************************************"),
            data={
                        "from": "Wyre Welcome Email <<EMAIL>>",
                        "to": [*data["recipients"]],
                        "subject": "Thank You for Contacting Wyre",
                        "html": """<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                    <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
                                    <head>
                                    <!--[if gte mso 9]>
                                    <xml>
                                    <o:OfficeDocumentSettings>
                                        <o:AllowPNG/>
                                        <o:PixelsPerInch>96</o:PixelsPerInch>
                                    </o:OfficeDocumentSettings>
                                    </xml>
                                    <![endif]-->
                                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                    <meta name="x-apple-disable-message-reformatting">
                                    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge"><!--<![endif]-->
                                    <title></title>

                                        <style type="text/css">
                                        @media only screen and (min-width: 620px) {
                                    .u-row {
                                        width: 600px !important;
                                    }
                                    .u-row .u-col {
                                        vertical-align: top;
                                    }

                                    .u-row .u-col-100 {
                                        width: 600px !important;
                                    }

                                    }

                                    @media (max-width: 620px) {
                                    .u-row-container {
                                        max-width: 100% !important;
                                        padding-left: 0px !important;
                                        padding-right: 0px !important;
                                    }
                                    .u-row .u-col {
                                        min-width: 320px !important;
                                        max-width: 100% !important;
                                        display: block !important;
                                    }
                                    .u-row {
                                        width: calc(100% - 40px) !important;
                                    }
                                    .u-col {
                                        width: 100% !important;
                                    }
                                    .u-col > div {
                                        margin: 0 auto;
                                    }
                                    }
                                    body {
                                    margin: 0;
                                    padding: 0;
                                    }

                                    table,
                                    tr,
                                    td {
                                    vertical-align: top;
                                    border-collapse: collapse;
                                    }

                                    p {
                                    margin: 0;
                                    }

                                    .ie-container table,
                                    .mso-container table {
                                    table-layout: fixed;
                                    }

                                    * {
                                    line-height: inherit;
                                    }

                                    a[x-apple-data-detectors='true'] {
                                    color: inherit !important;
                                    text-decoration: none !important;
                                    }

                                    table, td { color: #000000; } #u_body a { color: #cca250; text-decoration: none; } @media (max-width: 480px) { #u_content_image_5 .v-src-width { width: auto !important; } #u_content_image_5 .v-src-max-width { max-width: 100% !important; } #u_content_heading_3 .v-container-padding-padding { padding: 15px !important; } #u_content_heading_3 .v-font-size { font-size: 21px !important; } #u_content_text_3 .v-container-padding-padding { padding: 10px 22px !important; } #u_row_8.v-row-padding--vertical { padding-top: 0px !important; padding-bottom: 0px !important; } #u_content_text_7 .v-container-padding-padding { padding: 10px 30px !important; } #u_content_social_1 .v-container-padding-padding { padding: 9px !important; } }
                                        </style>



                                    <!--[if !mso]><!--><link href="https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap" rel="stylesheet" type="text/css"><!--<![endif]-->

                                    </head>

                                    <body class="clean-body u_body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #f9f9f9;color: #000000">
                                    <!--[if IE]><div class="ie-container"><![endif]-->
                                    <!--[if mso]><div class="mso-container"><![endif]-->
                                    <table id="u_body" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #f9f9f9;width:100%" cellpadding="0" cellspacing="0">
                                    <tbody>
                                    <tr style="vertical-align: top">
                                        <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #f9f9f9;"><![endif]-->


                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #111114;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #111114;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_image_5" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat',sans-serif;" align="left">

                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td style="padding-right: 0px;padding-left: 0px;" align="center">

                                        <img align="center" border="0" src="https://dashboard.wyreng.com/static/images/image-4.png" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;" width="600" class="v-src-width v-src-max-width"/>

                                        </td>
                                    </tr>
                                    </table>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #fffefe;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="background-color: #fffefe;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_heading_3" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 55px;font-family:'Montserrat',sans-serif;" align="left">

                                    <h1 class="v-font-size" style="margin: 0px; color: #5c3592; line-height: 140%; text-align: center; word-wrap: break-word; font-weight: normal; font-family: 'Montserrat',sans-serif; font-size: 33px;">
                                        <strong>Propective Client Info</strong>
                                    </h1>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <table id="u_content_text_3" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 60px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div style="color: #000000; line-height: 170%; text-align: center; word-wrap: break-word;">
                                        <p style="font-size: 14px; line-height: 170%;"><span style="font-size: 14px; line-height: 23.8px;"><span style="line-height: 23.8px; font-size: 14px;">See below for client information:</span></span></p>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div id="u_row_8" class="u-row-container v-row-padding--vertical" style="padding: 0px 100px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #fffbf2;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px 100px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #fffbf2;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_text_7" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:20px 120px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div style="color: #000000; line-height: 180%; text-align: left; word-wrap: break-word;">
                                        <p style="font-size: 14px; line-height: 180%; text-align: left;"><span style="font-size: 14px; line-height: 25.2px;"><span style="line-height: 25.2px; color: #000000; font-size: 14px;">Name :</span> <strong>""" + name + """</strong></span><br /><span style="font-size: 14px; line-height: 25.2px;"><span style="color: #7e8c8d; line-height: 25.2px; font-size: 14px;"><span style="color: #000000; line-height: 25.2px; font-size: 14px;">Email :</span><strong> </strong></span><strong>"""+data.get("email","-") + """ </strong></span><br /><span style="font-size: 14px; line-height: 25.2px;">Phone Number : <strong>""" + data.get("phone", "-") + """</strong></span></p>
                                    <p style="font-size: 14px; line-height: 180%; text-align: left;"><span style="font-size: 14px; line-height: 25.2px;">Location : <strong>"""+ data.get("location", "-") + """</strong></span><br /><span style="font-size: 14px; line-height: 25.2px;">Use Case : """+ data.get("use_case", "-") + """</span><br /><span style="font-size: 14px; line-height: 25.2px;">Facility Type :"""+ data.get("facility_type", "-") + """ </span><br /><span style="font-size: 14px; line-height: 25.2px;">No of sources :"""+ data.get("no_of_sources", "-") + """ </span><br /><span style="font-size: 14px; line-height: 25.2px;">Sources  of Energy : """+ data.get("sources_of_energy", "-") + """</span><br /><span style="font-size: 14px; line-height: 25.2px;">AVG Energy Cost :  ₦"""+ data.get("avg_energy_cost", "-") + """</span><br /><span style="font-size: 14px; line-height: 25.2px;">Units Consumed Monthly :"""+ data.get("avg_energy_cost", "-") + """</span></p>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #ffffff;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="background-color: #ffffff;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:20px 0px 0px;font-family:'Montserrat',sans-serif;" align="left">

                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td style="padding-right: 0px;padding-left: 0px;" align="center">

                                        <img align="center" border="0" src="https://dashboard.wyreng.com/static/images/image-5.gif" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;" width="600" class="v-src-width v-src-max-width"/>

                                        </td>
                                    </tr>
                                    </table>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_social_1" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:8px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div align="center">
                                    <div style="display: table; max-width:158px;">
                                    <!--[if (mso)|(IE)]><table width="158" cellpadding="0" cellspacing="0" border="0"><tr><td style="border-collapse:collapse;" align="center"><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse:collapse; mso-table-lspace: 0pt;mso-table-rspace: 0pt; width:158px;"><tr><![endif]-->


                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 21px;" valign="top"><![endif]-->
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 21px">
                                        <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                            <a href="https://twitter.com/" title="Twitter" target="_blank">
                                            <img src="https://dashboard.wyreng.com/static/images/image-2.png" alt="Twitter" title="Twitter" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                            </a>
                                        </td></tr>
                                        </tbody></table>
                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 21px;" valign="top"><![endif]-->
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 21px">
                                        <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                            <a href="https://instagram.com/" title="Instagram" target="_blank">
                                            <img src="https://dashboard.wyreng.com/static/images/image-1.png" alt="Instagram" title="Instagram" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                            </a>
                                        </td></tr>
                                        </tbody></table>
                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 0px;" valign="top"><![endif]-->
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 0px">
                                        <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                            <a href="https://linkedin.com/" title="LinkedIn" target="_blank">
                                            <img src="https://dashboard.wyreng.com/static/images/image-3.png" alt="LinkedIn" title="LinkedIn" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                            </a>
                                        </td></tr>
                                        </tbody></table>
                                        <!--[if (mso)|(IE)]></td><![endif]-->


                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                    </div>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <table style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;" align="left">

                                    <table height="0px" align="center" border="0" cellpadding="0" cellspacing="0" width="82%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 2px solid #f9cf40;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                        <tbody>
                                        <tr style="vertical-align: top">
                                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                            <span>&#160;</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <table style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px 10px 13px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div style="color: #b0b1b4; line-height: 180%; text-align: center; word-wrap: break-word;">
                                        <p style="font-size: 14px; line-height: 180%;"><span style="font-size: 12px; line-height: 21.6px;">© 2022 All Rights Reserved</span></p>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>


                                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                        </td>
                                    </tr>
                                    </tbody>
                                    </table>
                                    <!--[if mso]></div><![endif]-->
                                    <!--[if IE]></div><![endif]-->
                                    </body>

                                    </html>
                                    """
                    }
        )


    @staticmethod
    def send_new_chat_notification(data):

        BASE_DIR = os.path.dirname(os.path.dirname(__file__))
        TEMPLATE_DIRS = (os.path.join(BASE_DIR, "templates"),)
        from django.shortcuts import render
        html_data = open(f"{TEMPLATE_DIRS[0]}/index.html", "r").read()
        name = "inyang"

        return requests.post(
            "https://api.mailgun.net/v3/mg.wyreng.com/messages",
            auth=("api", f"{wyre.settings.MAILGUN_API_KEY}"),
            data={
                        "from": "Wyre Chat Recieved <<EMAIL>>",
                        "to": [*data["recipients"]],
                        "subject": "Wyre Chat Recieved",
                        "html": """<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                    <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
                                    <head>
                                    <!--[if gte mso 9]>
                                    <xml>
                                    <o:OfficeDocumentSettings>
                                        <o:AllowPNG/>
                                        <o:PixelsPerInch>96</o:PixelsPerInch>
                                    </o:OfficeDocumentSettings>
                                    </xml>
                                    <![endif]-->
                                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                    <meta name="x-apple-disable-message-reformatting">
                                    <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge"><!--<![endif]-->
                                    <title></title>

                                        <style type="text/css">
                                        @media only screen and (min-width: 620px) {
                                    .u-row {
                                        width: 600px !important;
                                    }
                                    .u-row .u-col {
                                        vertical-align: top;
                                    }

                                    .u-row .u-col-100 {
                                        width: 600px !important;
                                    }

                                    }

                                    @media (max-width: 620px) {
                                    .u-row-container {
                                        max-width: 100% !important;
                                        padding-left: 0px !important;
                                        padding-right: 0px !important;
                                    }
                                    .u-row .u-col {
                                        min-width: 320px !important;
                                        max-width: 100% !important;
                                        display: block !important;
                                    }
                                    .u-row {
                                        width: calc(100% - 40px) !important;
                                    }
                                    .u-col {
                                        width: 100% !important;
                                    }
                                    .u-col > div {
                                        margin: 0 auto;
                                    }
                                    }
                                    body {
                                    margin: 0;
                                    padding: 0;
                                    }

                                    table,
                                    tr,
                                    td {
                                    vertical-align: top;
                                    border-collapse: collapse;
                                    }

                                    p {
                                    margin: 0;
                                    }

                                    .ie-container table,
                                    .mso-container table {
                                    table-layout: fixed;
                                    }

                                    * {
                                    line-height: inherit;
                                    }

                                    a[x-apple-data-detectors='true'] {
                                    color: inherit !important;
                                    text-decoration: none !important;
                                    }

                                    table, td { color: #000000; } #u_body a { color: #cca250; text-decoration: none; } @media (max-width: 480px) { #u_content_image_5 .v-src-width { width: auto !important; } #u_content_image_5 .v-src-max-width { max-width: 100% !important; } #u_content_heading_3 .v-container-padding-padding { padding: 15px !important; } #u_content_heading_3 .v-font-size { font-size: 21px !important; } #u_content_text_3 .v-container-padding-padding { padding: 10px 22px !important; } #u_row_8.v-row-padding--vertical { padding-top: 0px !important; padding-bottom: 0px !important; } #u_content_text_7 .v-container-padding-padding { padding: 10px 30px !important; } #u_content_social_1 .v-container-padding-padding { padding: 9px !important; } }
                                        </style>



                                    <!--[if !mso]><!--><link href="https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap" rel="stylesheet" type="text/css"><!--<![endif]-->

                                    </head>

                                    <body class="clean-body u_body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #f9f9f9;color: #000000">
                                    <!--[if IE]><div class="ie-container"><![endif]-->
                                    <!--[if mso]><div class="mso-container"><![endif]-->
                                    <table id="u_body" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #f9f9f9;width:100%" cellpadding="0" cellspacing="0">
                                    <tbody>
                                    <tr style="vertical-align: top">
                                        <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #f9f9f9;"><![endif]-->


                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #111114;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #111114;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_image_5" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat',sans-serif;" align="left">

                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td style="padding-right: 0px;padding-left: 0px;" align="center">

                                        <img align="center" border="0" src="https://dashboard.wyreng.com/static/images/image-4.png" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;" width="600" class="v-src-width v-src-max-width"/>

                                        </td>
                                    </tr>
                                    </table>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #fffefe;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="background-color: #fffefe;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_heading_3" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 55px;font-family:'Montserrat',sans-serif;" align="left">

                                    <h1 class="v-font-size" style="margin: 0px; color: #5c3592; line-height: 140%; text-align: center; word-wrap: break-word; font-weight: normal; font-family: 'Montserrat',sans-serif; font-size: 33px;">
                                        <strong>Wyre Chat Request</strong>
                                    </h1>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <table id="u_content_text_3" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 60px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div style="color: #000000; line-height: 170%; text-align: center; word-wrap: break-word;">
                                        <p style="font-size: 14px; line-height: 170%;"><span style="font-size: 14px; line-height: 23.8px;"><span style="line-height: 23.8px; font-size: 14px;">Wyre Chat information:</span></span></p>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div id="u_row_8" class="u-row-container v-row-padding--vertical" style="padding: 0px 100px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #fffbf2;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px 100px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #fffbf2;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_text_7" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:20px 120px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div style="color: #000000; line-height: 180%; text-align: left; word-wrap: break-word;">
                                        <p style="font-size: 14px; line-height: 180%; text-align: left;"><span style="font-size: 14px; line-height: 25.2px;"><span style="line-height: 25.2px; color: #000000; font-size: 14px;">Name :</span> <strong>""" + name + """</strong></span><br /><span style="font-size: 14px; line-height: 25.2px;"><span style="color: #7e8c8d; line-height: 25.2px; font-size: 14px;"><span style="color: #000000; line-height: 25.2px; font-size: 14px;">Email :</span><strong> </strong></span><strong>"""+data.get("email","-") + """ </strong></span><br /><span style="font-size: 14px; line-height: 25.2px;">Phone Number : <strong>""" + data.get("phone", "-") + """</strong></span></p>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #ffffff;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="background-color: #ffffff;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:20px 0px 0px;font-family:'Montserrat',sans-serif;" align="left">

                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td style="padding-right: 0px;padding-left: 0px;" align="center">

                                        <img align="center" border="0" src="https://dashboard.wyreng.com/static/images/image-5.gif" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;" width="600" class="v-src-width v-src-max-width"/>

                                        </td>
                                    </tr>
                                    </table>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>



                                    <div class="u-row-container v-row-padding--vertical" style="padding: 0px;background-color: transparent">
                                    <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                                        <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
                                    <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                    <!--[if (!mso)&(!IE)]><!--><div style="height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->

                                    <table id="u_content_social_1" style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:8px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div align="center">
                                    <div style="display: table; max-width:158px;">
                                    <!--[if (mso)|(IE)]><table width="158" cellpadding="0" cellspacing="0" border="0"><tr><td style="border-collapse:collapse;" align="center"><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse:collapse; mso-table-lspace: 0pt;mso-table-rspace: 0pt; width:158px;"><tr><![endif]-->


                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 21px;" valign="top"><![endif]-->
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 21px">
                                        <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                            <a href="https://twitter.com/" title="Twitter" target="_blank">
                                            <img src="https://dashboard.wyreng.com/static/images/image-2.png" alt="Twitter" title="Twitter" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                            </a>
                                        </td></tr>
                                        </tbody></table>
                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 21px;" valign="top"><![endif]-->
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 21px">
                                        <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                            <a href="https://instagram.com/" title="Instagram" target="_blank">
                                            <img src="https://dashboard.wyreng.com/static/images/image-1.png" alt="Instagram" title="Instagram" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                            </a>
                                        </td></tr>
                                        </tbody></table>
                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 0px;" valign="top"><![endif]-->
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 0px">
                                        <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                            <a href="https://linkedin.com/" title="LinkedIn" target="_blank">
                                            <img src="https://dashboard.wyreng.com/static/images/image-3.png" alt="LinkedIn" title="LinkedIn" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                            </a>
                                        </td></tr>
                                        </tbody></table>
                                        <!--[if (mso)|(IE)]></td><![endif]-->


                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                    </div>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <table style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;" align="left">

                                    <table height="0px" align="center" border="0" cellpadding="0" cellspacing="0" width="82%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 2px solid #f9cf40;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                        <tbody>
                                        <tr style="vertical-align: top">
                                            <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                            <span>&#160;</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <table style="font-family:'Montserrat',sans-serif;" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                    <tbody>
                                        <tr>
                                        <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px 10px 13px;font-family:'Montserrat',sans-serif;" align="left">

                                    <div style="color: #b0b1b4; line-height: 180%; text-align: center; word-wrap: break-word;">
                                        <p style="font-size: 14px; line-height: 180%;"><span style="font-size: 12px; line-height: 21.6px;">© 2022 All Rights Reserved</span></p>
                                    </div>

                                        </td>
                                        </tr>
                                    </tbody>
                                    </table>

                                    <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
                                    </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                        </div>
                                    </div>
                                    </div>


                                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                                        </td>
                                    </tr>
                                    </tbody>
                                    </table>
                                    <!--[if mso]></div><![endif]-->
                                    <!--[if IE]></div><![endif]-->
                                    </body>

                                    </html>
                                    """
                    }
        )

    @staticmethod
    def send_daily_otd( sender, title, message, receievers, bcc):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST
            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "html":message,
                "bcc": bcc})
                # "text": message,

        print('(((((((((((((((( CHECK ))))))))))))))))')
        print(response.content)
        print('(((((((((((((((( CHECK ))))))))))))))))')

        if response.ok:
            print("Sending otd successful ")
            open("maillogs.txt", "a").write("Sending Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print("Sending otd failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": True,
                "message": response.content
            }


    @staticmethod
    def send_quote_request( sender, title, message, receievers):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST
            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "text": message})

        if response.ok:
            print("Send Quote request successful ")
            open("maillogs.txt", "a").write("Sending Quote request Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print("Sending Quote request failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": True,
                "message": response.content
            }

    @staticmethod
    def send_weekly_reminder( sender, title, message, receievers, bcc):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST
            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "html":message,
                "bcc": bcc})

        if response.ok:
            print("Reminder sent successfully ")
            open("maillogs.txt", "a").write("Sending Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print(response.content)
            print("Reminder sending failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": True,
                "message": response.content
            }

    @staticmethod
    def send_monthly_report( sender, title, message, receievers, bcc):

        """
            SEND SIMPLE MAIL REQUIRES :
            - SENDER - NUMERIC CODE
            - TITLE -> STRING
            - MESSAGE -> STRING
            - RECEIVERS -> LIST
            POSSIBLE SENDERS:
                1. WYRE-MONITOR
                2. WYRE-ALERTS
        """
        api_key = os.environ.get("api_key")
        mailgun_url = os.environ.get("mailgun_url")
        response = requests.post(
            mailgun_url,
            auth=("api", api_key),
            data={"from": f"{wyre.settings.POSSIBLE_SENDERS[sender]} <<EMAIL>>",
                "to": receievers,
                "subject": title,
                "html":message,
                "bcc": bcc})

        if response.ok:
            print("Report sent successfully ")
            open("maillogs.txt", "a").write("Sending Successful")
            return {
                "status": True,
                "message": "E-mail successfully sent"
            }

        else:
            print("Report sending failed ")
            open("maillogs.txt", "a").write(f"Sending failed ({response.content})")

            return {
                "status": True,
                "message": response.content
            }

# data = {"recipients":["<EMAIL>", "<EMAIL>"]}
# send_new_registrant_notification(data)