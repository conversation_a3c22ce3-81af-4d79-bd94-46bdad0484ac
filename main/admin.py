from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from django.db.models import Max, Count
from django.utils import timezone
from datetime import timedelta
from django.db import connection

from .models import Month_End_Diesel_Balance, <PERSON>ert_Setting, <PERSON>, Branch, Cost, Device, Equipment, Reading, Datalog, DeviceType, Subscription, ViewPermission, Tariff, Bill_Mails, Degree_Day, FuelConsumption, DieselOverviewHistory, BranchOTDHistory, AdminTariff, Target, Alert_Setting, MonthlyReport, MonthlyBranchMetrics, DevicePushData, SensorData

# Register your models here.

# Custom admin site for better performance
class WyreAdminSite(admin.AdminSite):
    site_header = 'Wyre Admin'
    site_title = 'Wyre Admin Portal'
    index_title = 'Wyre Administration'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('dashboard/', self.admin_view(self.dashboard_view), name='dashboard'),
        ]
        return custom_urls + urls

    def dashboard_view(self, request):
        # Get recent readings (last 24 hours)
        recent_date = timezone.now() - timedelta(hours=24)
        recent_readings = Reading.objects.filter(
            post_datetime__gte=recent_date
        ).order_by('-post_datetime')[:10]

        # Get recent datalogs (last 24 hours)
        recent_datalogs = Datalog.objects.filter(
            post_datetime__gte=recent_date
        ).order_by('-post_datetime')[:10]

        # Get devices with zero readings
        devices_with_zero_readings = Device.objects.filter(
            reading__zero_updated=True
        ).annotate(
            zero_count=Count('reading__zero_updated'),
            last_zero=Max('reading__post_datetime')
        ).order_by('-last_zero')[:10]

        context = {
            'recent_readings': recent_readings,
            'recent_datalogs': recent_datalogs,
            'devices_with_zero_readings': devices_with_zero_readings,
            'title': 'Wyre Dashboard',
        }
        return render(request, 'admin/dashboard.html', context)

# Uncomment the following lines to use the custom admin site
# admin_site = WyreAdminSite(name='wyre_admin')

@admin.register(Bill_Mails)
class Bill_MailsAdmin(admin.ModelAdmin):

    list_display = ['email', "client"]

@admin.register(Datalog)
class DatalogAdmin(admin.ModelAdmin):
    # Optimize for performance
    list_per_page = 50  # Limit number of records per page
    list_max_show_all = 200  # Maximum number when clicking 'show all'

    # Optimize date hierarchy for faster loading
    date_hierarchy = 'post_date'  # Use post_date instead of post_datetime for less granularity

    # Optimize search fields
    search_fields = ['branch__name', 'device__name', 'device__device_id']

    # Optimize list display - fewer fields = faster loading
    list_display = ['post_date', 'device', 'summary_energy_register_1', 'zero_updated']

    # More specific filters
    list_filter = [
        ('zero_updated', admin.BooleanFieldListFilter),
        ('post_date', admin.DateFieldListFilter),
    ]

    # Add device and branch as filter in right sidebar
    list_select_related = ['device', 'branch', 'client']

    # Add actions
    actions = ['mark_as_zero_updated']

    def mark_as_zero_updated(self, request, queryset):
        updated = queryset.update(zero_updated=True)
        self.message_user(request, f'{updated} datalogs were marked as zero updated.')
    mark_as_zero_updated.short_description = "Mark selected datalogs as zero updated"

    def get_queryset(self, request):
        # Optimize the queryset to reduce database queries
        qs = super().get_queryset(request)
        return qs.select_related('device', 'branch', 'client')

from django.contrib.admin.views.main import ChangeList

class EstimatedCountChangeList(ChangeList):
    def get_results(self, request):
        # Call the super method to let it do most of the work
        super().get_results(request)

        # Replace result_count with estimated count
        self.result_count = self.get_estimated_count()
        self.full_result_count = self.result_count
        self.can_show_all = False  # Disable "Show all" option
        self.multi_page = True     # Force pagination

    def get_estimated_count(self):
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT reltuples::bigint FROM pg_class WHERE relname = %s",
                ['main_reading'],
            )
            return int(cursor.fetchone()[0])

@admin.register(Reading)
class ReadingAdmin(admin.ModelAdmin):
    # Optimize for performance
    list_per_page = 50  # Limit number of records per page
    list_max_show_all = 200  # Maximum number when clicking 'show all'

    # Optimize date hierarchy for faster loading
    date_hierarchy = 'post_date'  # Use post_date instead of post_datetime for less granularity

    # Optimize search fields
    search_fields = ['branch__name', 'device__name', 'device__device_id']

    # Optimize list display - fewer fields = faster loading
    list_display = ['post_date', 'device', 'kwh_import', 'zero_updated']

    # More specific filters
    # list_filter = [
    #     ('zero_updated', admin.BooleanFieldListFilter),
    #     ('post_date', admin.DateFieldListFilter),
    # ]

    list_filter = [
        'device', 'zero_updated', 'post_date'
    ]

    # Add device and branch as filter in right sidebar
    list_select_related = ['device', 'branch', 'client']

    # Add actions
    actions = ['mark_as_zero_updated']

    def get_changelist(self, request, **kwargs):
        return EstimatedCountChangeList

    def mark_as_zero_updated(self, request, queryset):
        updated = queryset.update(zero_updated=True)
        self.message_user(request, f'{updated} readings were marked as zero updated.')
    mark_as_zero_updated.short_description = "Mark selected readings as zero updated"

    def get_queryset(self, request):
        # Optimize the queryset to reduce database queries
        qs = super().get_queryset(request)
        qs._force_estimate = True  # custom flag
        return qs.select_related('device', 'branch', 'client')#.only('post_date', 'device', 'kwh_import', 'zero_updated')

    def get_estimated_count(self):
        with connection.cursor() as cursor:
            # cursor.execute("SELECT reltuples::bigint FROM pg_class WHERE relname = %s", ['main_reading'])
            return 200 # int(cursor.fetchone()[0])

@admin.register(Device)
class DeviceAdmin(admin.ModelAdmin):

    search_fields = ['branch__name']
    list_display = ['name',  'branch', 'client', 'type', 'is_gen', 'gen_size', 'device_id', 'switch_state', 'last_posted', 'non_post_attention', 'created_at', 'updated_at']

@admin.register(Alert_Setting)
class Alert_SettingAdmin(admin.ModelAdmin):

    search_fields = ['branch__name']
    list_display = [field.name for field in Alert_Setting._meta.fields]

@admin.register(Degree_Day)
class Degree_dayAdmin(admin.ModelAdmin):

    list_display = ['date', 'value']

@admin.register(FuelConsumption)
class FuelConsumptionAdmin(admin.ModelAdmin):

    list_display = ['start_date', 'end_date', 'branch', 'quantity']

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):

    readonly_fields = ('start_date', 'expiry_date',)
    list_display = ['client', 'branch', 'duration']

@admin.register(DieselOverviewHistory)
class DieselOverviewHistoryAdmin(admin.ModelAdmin):

    list_display = ['start_date', 'branch', 'data']

@admin.register(BranchOTDHistory)
class BranchOTDHistoryAdmin(admin.ModelAdmin):

    list_display = ['start_date', 'end_date', 'branch', 'data']

@admin.register(AdminTariff)
class AdminTariffAdmin(admin.ModelAdmin):

    list_display = ['client', 'branch', 'device', 'amount']

@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):

    list_filter = ["client", ]
    list_display = ['name', 'client', 'address', 'email', 'is_active']

@admin.register(Target)
class TargetAdmin(admin.ModelAdmin):

    search_fields = ['client__name']
    list_display = ['blended_cost_of_energy', "diesel_usage_accuracy", "utility_usage_accuracy", "maximum_monthly_deviation_hours", "papr", "fuel_efficiency", "generator_size_efficiency_1", "generator_size_efficiency_2", "generator_size_efficiency_3"]
    list_filter = ['client__name']

@admin.register(DeviceType)
class Alert_SettingAdmin(admin.ModelAdmin):

    list_display = [field.name for field in DeviceType._meta.fields]


@admin.register(Cost)
class CostAdmin(admin.ModelAdmin):

    list_display = [field.name for field in Cost._meta.fields]


@admin.register(MonthlyReport)
class MonthlyReportAdmin(admin.ModelAdmin):
    list_display = ['branch', 'year', 'month', 'created_at']
    list_filter = ['branch', 'year', 'month']
    search_fields = ['branch__name']

@admin.register(MonthlyBranchMetrics)
class MonthlyBranchMetricsAdmin(admin.ModelAdmin):
    list_display = ('branch', 'month', 'year', 'updated_at')
    list_filter = ('branch', 'month', 'year')  # Remove 'period_start', 'period_end'

admin.site.register(Tariff)
admin.site.register(ViewPermission)
admin.site.register(Bill)
# admin.site.register(Cost)
admin.site.register(Equipment)
admin.site.register(Month_End_Diesel_Balance)


class SensorDataInline(admin.TabularInline):
    model = SensorData
    extra = 0
    readonly_fields = ('sensor_id', 'sensor_type_id', 'times', 'is_alarm', 'is_line', 're_val', 'value')


@admin.register(DevicePushData)
class DevicePushDataAdmin(admin.ModelAdmin):
    list_display = ('device', 'timestamp', 'flag', 'device_user_id', 'created_at')
    list_filter = ('device', 'timestamp', 'flag', 'created_at')
    search_fields = ('device__name', 'device__device_id', 'device_user_id', 'parent_user_id')
    readonly_fields = ('created_at',)
    inlines = [SensorDataInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('device')


@admin.register(SensorData)
class SensorDataAdmin(admin.ModelAdmin):
    list_display = ('push_data', 'sensor_id', 'sensor_type_id', 'times', 'value', 'is_alarm')
    list_filter = ('sensor_type_id', 'is_alarm', 'is_line', 'push_data__timestamp')
    search_fields = ('sensor_id', 'push_data__device__name')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('push_data__device')