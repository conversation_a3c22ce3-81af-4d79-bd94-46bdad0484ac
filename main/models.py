from ast import Sub
from pickle import DUP
from django.db import models
from django.db.models import Q, Sum, Avg, F
import datetime
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from itertools import chain
from django.db.models.aggregates import Aggregate
import numpy as np  # Import numpy properly
# from scipy.sparse import data
from .scripts import remote_request, time_helpers, dashboard_helpers, mailgun, baseline_helpers
from django.db.models import Avg, Max, Min, base
from memoize import Memoizer, memoize, delete_memoized, delete_memoized_verhash
import json, os
import pandas as pd
import numpy as np
from statistics import mean as AVERAGE
from wyre import settings
from account.models import Client, User
from functools import reduce
from dateutil.relativedelta import TU, relativedelta
from datetime import date, timedelta, timezone
from django.utils import timezone
import threading
import functools
import math, threading, queue
import calendar

from calendar import monthrange
from django.db.models import Sum
from django.utils import timezone
from django.core.cache import cache
from django.utils.html import format_html_join
from django.utils.html import format_html
from django.utils.safestring import mark_safe

memoizer = Memoizer()


###########       AT SOME POINT YOU NEED TO SWITCH TO CALCULATING FUEL CONSUMPTION BASED ON KWH CONSUMED INSTEAD OF HOURS OF USE.

DIESEL_LTR_PER_KWH               = 0.4 # 0.4L OF DIESEL IS BURNT PER KWH OF ENERGY PRODUCED. SOURCE: https://energyeducation.ca/encyclopedia/Diesel_generator#:~:text=A%20general%20rule%20of%20thumb,of%20diesel%20per%20kWh%20produced.
CO2_KG_EMMISSIONS_PER_LITRE      = 2.6 # 2.6KG OF CO2 IS EMITTED PER LITRE OF DIESEL BURNT. SOURCE: https://energyeducation.ca/encyclopedia/Diesel_generator#:~:text=Every%20litre%20of%20fuel%20has,per%20liter%20of%20diesel%20fuel.
CO2_KG_EMMISSIONS_PER_KWH_DIESEL = 0.27 # 0.2KG OF CO2 IS EMITTED PER KWH OF ENERGY PRODUCED ON DIESEL.  https://www.volker-quaschning.de/datserv/CO2-spez/index_e.php
CO2_KG_EMMISSIONS_PER_KWH_LNG    = 0.549 # 0.549 (formerly 0.453) OF CO2 IS EMITTED PER KWH OF ENERGY PRODUCED ON LNG.  https://www.volker-quaschning.de/datserv/CO2-spez/index_e.php
CO2_KG_EMMISSIONS_PER_KWH_UTILITY= 0.43963136 # 0.43963136KG OF CO2 IS EMITTED PER KWH OF ENERGY PRODUCED ON UTILITY.  https://ecometrica.com/assets/Electricity-specific-emission-factors-for-grid-electricity.pdf
MAX_VOLTAGE_IMBALANCE            = 0.01 # 0.01 IN RATIO MEANING 1% IMBALANCE MAXIMUM IMBALANCE ALLOWED
# LOAD BALANCE : Divide the maximum deviation by the average voltage or current and multiply by 100 % unbalance = (Max deviation from average V or I/average V or I) x 100 (https://www.fluke.com/en/learn/blog/motors-drives-pumps-compressors/voltage-unbalance)
MAX_CURRENT_IMBALANCE            = 0.1 # See same link as voltage imbalance
KW_TO_KVA_MULTIPLIER             = 0.7978723404255319

YEARLY_SUBSCRIPTION = 360000
DAYS_PER_YEAR = 365
SUBSCRIPTION_PRICE_PER_DAY = YEARLY_SUBSCRIPTION/DAYS_PER_YEAR

# Create your models here.


class Bill_Mails(models.Model):

    FREQUENCIES = (
        ('MONTHLY', 'MONTHLY'),
        ('WEEKLY', 'WEEKLY'),
        ('DAILY', 'DAILY'),
        ('FORTNIGHT', 'FORTNIGHT'),
    )
    email = models.EmailField(max_length=200)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, null= True, blank=True)
    user = models.OneToOneField(User, on_delete=models.CASCADE, null= True, blank=True)
    bill_frequency = models.CharField(max_length=300, choices = FREQUENCIES, default= "MONTHLY")
    added_by =  models.ForeignKey(User, on_delete=models.CASCADE, null= True, blank=True, related_name="added_by")

    def __str__(self) -> str:
        return self.email

    def add_reporting_device(self, *device_ids, external = False, external_receiver_id = False, frequency = False):

        Bill_Mails.objects.get(id = external_receiver_id or self.id).device_set.clear()# clear data from relationships either on external or internal user

        for device_id in device_ids:

            try:

                if not external:

                    device = Device.objects.get(id = device_id)
                    device.bill_recievers.add(self.id)

                    if frequency:
                        self.set_frequency(frequency)

                else:

                    device = Device.objects.get(id = device_id)
                    device.bill_recievers.add(external_receiver_id)

            except Device.DoesNotExist:

                return {
                        "data":list(self.device_set.all().values("name", "id", "client__name", "branch__name")),
                        "message": "possible inexistent id",
                        "task":False
                        }

        return {
                "data":list(self.device_set.all().values("name", "id", "client__name", "branch__name")),
                "message": "success",
                "task": True
            }

    def remove_reporting_device(self, device_id):

        try:

             device = Device.objects.get(id = device_id)

        except:

            return {
                        "data":list(self.device_set.all().values("name", "id", "client__name", "branch__name")),
                        "message": "possible inexestent id",
                        "task":False
                    }

        device.bill_recievers.remove(self.id)

        return {
                "data":list(self.device_set.all().values("name", "id", "client__name", "branch__name")),
                "message": "success",
                "task": False
            }

    @staticmethod
    def create_new(email, creator_id, user = False, internal_user = False):

        creator = User.objects.get(id = creator_id)
        client = creator.client

        if internal_user:

            new_bill_reciever = Bill_Mails(email = email, client = client, user = creator)
            new_bill_reciever.save()

        else:

            new_bill_reciever = Bill_Mails(email = email, client = client, added_by = creator)
            new_bill_reciever.save()

        return new_bill_reciever

    def set_frequency(self, frequency):

        self.bill_frequency = frequency
        self.save()

        return self.bill_frequency



    @staticmethod
    def get_all_added_by(user_id):

        added_recievers = Bill_Mails.objects.filter(added_by_id = user_id).values("id", "email", "client_id", "bill_frequency", "added_by", "added_by__username")

        def get_attach_added_devices(reciever):
            devices = Bill_Mails.objects.get(id = reciever["id"]).device_set.all().values("id", "name", "branch", "client")
            reciever["assigned_devices"] = list(devices)
            return reciever

        data = list(map(get_attach_added_devices ,added_recievers))

        return data

    @staticmethod
    def get_personal_data(user_id):

        internal_reciever = Bill_Mails.objects.filter(user_id = user_id)

        if not internal_reciever.exists():
            user = User.objects.get(id = user_id)
            internal_reciever = Bill_Mails.create_new(  email = user.email,
                                                        creator_id  =user.id,
                                                        internal_user = True
                                                    )
        else:
            internal_reciever = internal_reciever[0]

        return {
                "email": internal_reciever.user.email,
                "assigned_devices": internal_reciever.device_set.all().values("id", "name", "branch", "client"),
                "frequency": internal_reciever.bill_frequency
                }

    @staticmethod
    def make_bill(receiver, start_date, end_date, selected_devices = False):

        """# RECIEVER IS A BILL MAIL OBJECT"""
        devices = receiver.device_set.all()


        # USE SELECTED DEVICES LIST IF A LIST IS PASSED ELSE USE THE USER'S DEVICE LIST
        if selected_devices:
            per_device_bill = [Device.objects.get(id = device_id).
                                get_billing_data_for_mailing(start_date, end_date)
                                    for device_id in selected_devices]
        else:
            per_device_bill = [device.get_billing_data_for_mailing(start_date, end_date) for device in devices]

        data = dict(
            target_month = start_date.strftime("%B"),
            start_date  = start_date.strftime("%d %B, %Y"),
            end_date    = end_date.strftime("%d %B, %Y"),
            data        = per_device_bill,
            backend_base_url = os.environ.get("backend_base_url")
        )

        previous_total_bill = sum([device["previous_cost_of_energy"] for device in per_device_bill])
        previous_total_usage = sum([device["previous_energy_consumed"] for device in per_device_bill])

        current_total_bill = sum([device["cost_of_energy"] for device in per_device_bill])
        current_total_usage = sum([device["energy_consumed"] for device in per_device_bill])

        data["previous_total_usage"] = round(previous_total_usage)
        data["previous_total_bill"] = round(previous_total_bill)

        data["current_usage"] = round(current_total_usage)
        data["current_bill"] = round(current_total_bill)

        data["client_image"] = receiver.added_by.client.logo.url if receiver.added_by else receiver.user.client.logo.url

        pdf = dashboard_helpers.Render.render_to_file(os.environ.get("pdf_report_template_name"), data, receiver)

        return pdf

    def mail_pdf(receiver, start_date, end_date, selected_devices = False, email_address = False):

        # NOTE THAT IN THIS CASE RECIEVER IS ONLY NECESSARY TO DENOTE WHO IS SENDING THE MAIL SO THAT THE CORRESPONDING CLIENT LOGO CAN BE LOADED.

        pdf = Bill_Mails.make_bill(receiver, start_date, end_date, selected_devices)
        response = mailgun.Mailer.send_simple_message_with_attachment(2,
                                                                    "Wyre-Report",
                                                                    "Hello Please find your report attached.",
                                                                    [email_address],
                                                                    pdf["file_path"])
        response.update(pdf)
        return response


    def auto_send_reports(self, start_date = False, end_date = False):

        today = datetime.datetime.now()
        WEEK_START = "monday"

        if today.strftime("%A").lower() == WEEK_START and self.bill_frequency == "WEEKLY":
            end_date = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date   = end_date - datetime.timedelta(days = 7)

        elif today.day == 1 and self.bill_frequency == "MONTHLY":
            end_date = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            number_of_days_in_month = (end_date -  datetime.timedelta(days = 1)).day
            start_date   = end_date - datetime.timedelta(days = number_of_days_in_month)

        email = self.email if self.added_by else self.user.email
        print(email, start_date, today, self.bill_frequency,today.day)

        if email and start_date and end_date:

            response = Bill_Mails.mail_pdf(self, start_date, end_date, email_address = email)

            return {
                "status":True,
                "message" : "sent",
                "response": response,
                "email": email
            }

        return {
                "status":False,
                "message" : "skipped",
                "response": "-",
                "email": email
            }

    class Meta:
        verbose_name_plural = "Bill Mail Recievers"

# BRANCHES MODEL
class Branch(models.Model):
    name    = models.CharField(max_length=200)
    cache   = models.JSONField(blank=True, null=True)
    client  = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='branches')
    address = models.CharField(max_length=200)
    region = models.ForeignKey('main.Region', on_delete=models.SET_NULL, null=True, blank=True, related_name='branches')
    cost_data = models.TextField(max_length=10000)
    email = models.EmailField(max_length=200, null=True, blank=True)
    copy_email = models.CharField(
        max_length=500,
        blank=True,
        help_text="Comma-separated list of addresses to CC on report emails"
    )
    is_active = models.BooleanField(default = True)

    # operating_start_time = models.TimeField(null=True, blank=True)
    # operating_end_time   = models.TimeField(null=True, blank=True)

    @property
    def equipment(self):
        obj = self.equipments.all()

        return list(map(lambda equipment: {'id':equipment.id, 'name':equipment.name, 'voltage':equipment.voltage, 'quantity':equipment.quantity, 'date_purchased':equipment.date_purchased }, obj))

    def device_dashboard_data(self, start_date, end_date, force = False):

        # if start_date = time_helpers.get_start_and_end_of_month_from_date

        my_queue = queue.Queue()
        start_of_month = time_helpers.get_start_and_end_of_month_from_date(end_date)[0]
        current_date = timezone.now()
        start_date_time_diff = abs((start_of_month - start_date).total_seconds()/(60*60))
        end_date_time_diff = abs((end_date - current_date).total_seconds()/(60*60))

        use_cache = start_date_time_diff < 1 and \
                    end_date_time_diff < 1 and \
                    force != True and bool(self.cache)

        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print(start_date_time_diff)
        # print(end_date_time_diff)
        # print(not self.cache)
        # print(start_date_time_diff < 1,
        #             end_date_time_diff < 1,
        #             force != True,
        #             bool(self.cache))
        # print(use_cache)
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

        def storeInQueue(f):
            def wrapper(*args):
                my_queue.put(f(*args))
            return wrapper

        devices_data = []

        @storeInQueue
        def fetch_device_data(device):
            devices_data.append(
                                    {
                                        'name': device.name,
                                        'device_id': device.id,
                                        'is_load'  : device.is_load,
                                        'is_source': device.is_source,
                                        'device_type': device.type.choice_name,
                                        'dashboard': {
                                            "total_kwh": {
                                            "unit": "kWh",
                                            "value": round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
                                            },
                                            "min_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("min"), settings.DECIMAL_PLACES)
                                            },
                                            "max_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("max"), settings.DECIMAL_PLACES)
                                            },
                                            "avg_demand": {
                                                "unit": "kW",
                                                "value": round(device.get_agg_kwh_for_period(start_date, end_date).get("avg"), settings.DECIMAL_PLACES)
                                            },
                                            "dashboard_carbon_emissions": {
                                                "unit": "Tons",
                                                "value": round(device.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value"), 2)
                                            },
                                            "cost_of_energy": {
                                                "unit": "Naira/KWh",
                                                "value": device.get_cost_of_energy(start_date, end_date)
                                            },
                                            "today": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("today_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "yesterday": {
                                                "value": round(device.get_today_vs_yesterday(start_date, end_date).get("yesterday_usage"), settings.DECIMAL_PLACES),
                                                "unit": "kWh"
                                            },
                                            "solar_hours":{
                                                "value":device.get_solar_hours_consumption(start_date, end_date),
                                                "unit":"kwh"
                                            }
                                        },
                                        'id': device.id
                                    }
                                )

        if not use_cache:

            devices = self.device_set.all()
            threads = [threading.Thread(target=fetch_device_data, args = (device, )) for device_index, device in enumerate(devices)]

            for t in threads:
                t.start()
                t.join()

            for device in devices:
                my_data = my_queue.get()
                print(my_data)

            self.cache = devices_data
            self.save()
        else:
            print("Using Cache")
            devices_data = self.cache


        return devices_data

    @property
    def has_generator(self):
        gen_type = DeviceType.objects.filter(choice_name = "GENERATOR")[0]
        obj = self.device_set.filter(type =gen_type)

        return obj.exists()

    def get_generators(self):
        gen_type = DeviceType.objects.filter(choice_name = "GENERATOR")[0]
        queryset = self.device_set.filter(type = gen_type, is_active = True)

        return queryset

    def get_hours_of_use(self, start_date, end_date):

        from main.scripts import aggregation_helpers

        devices = self.device_set.all()
        total_seconds = (end_date - start_date).total_seconds()
        total_hours   = total_seconds/(60*60)

        # tou = [round(device.get_hours_of_use(start_date, end_date), 3) for device in devices]
        tou = []
        for device in devices:
            readings = device.reading_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values()
            if not readings:
                tou.append(0)
                continue
            usage = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

            tou.append(usage)

        data = {
                "devices": [device.name for device in devices],
                "hours": tou,
                "period_total_hours":total_hours,
                "black_out": round(total_hours - sum(tou), 2)
                }

        return data

    def get_gen_hours_of_use(self, start_date, end_date):
        cache_key = f'gen_hours_of_use_{self.id}_{start_date}_{end_date}'
        tou = cache.get(cache_key)
        if tou is not None:
            return tou

        # Compute and cache the result if it's not in the cache
        devices = self.device_set.filter(type='1')
        tou = [round(device.get_hours_of_use(start_date, end_date), 3) for device in devices]
        cache.set(cache_key, tou, timeout=3600)

        return tou

    def get_periodic_device_usage_daily(self, start_date, end_date, frequency):

        devices_qs = self.reading_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values("post_datetime", "device__name", "kwh_import")
        print(devices_qs)
        df = pd.DataFrame(devices_qs)

        devices_names_tuples = self.device_set.all().values_list('name')
        device_names = [tup[0] for tup in devices_names_tuples]

        response = { name:list() for name in device_names}

        if devices_qs.exists():
            devices = df.device__name.unique()
        else:
            devices = []

        full_date_range = pd.date_range(start=start_date, end=end_date+timedelta(days=1), freq='1T')
        full_df = pd.DataFrame(full_date_range, columns=['date']).astype(str)

        for device_name in devices[::-1]:

            device_df = df[df.device__name == device_name]

            # Convert 'post_datetime' to datetime
            device_df['post_datetime'] = pd.to_datetime(device_df['post_datetime'])

            # Sort the DataFrame by 'post_datetime'
            device_df = device_df.sort_values(by='post_datetime')

            ndata = np.empty((len(full_date_range), len(df.columns)))
            ndata.fill(np.nan)

            # Create the DataFrame
            zdf = pd.DataFrame(ndata, columns=df.columns)
            zdf.post_datetime = full_date_range

            ndf = pd.concat([device_df, zdf]).reset_index(drop=True)
            ndf = ndf.sort_values(by='post_datetime')
            filleddf = ndf.bfill()
            filleddf = filleddf.ffill()

            device_df = filleddf

            # Extract date and create a new column
            device_df['date'] = device_df['post_datetime'].dt.date.astype(str)

            # Get the last reading of each day
            last_readings = device_df.groupby('date')['kwh_import'].first().reset_index()
            # last_readings = full_df.merge(last_readings.reset_index(), on='date', how='left').fillna(0)

            # Shift the last readings to align with the next day
            last_readings['next_day_import'] = last_readings['kwh_import'].shift(-1)
            last_readings['next_day_import_time'] = last_readings['date'].shift(-1)

            # Drop the last row because it doesn't have a next day reading
            last_readings = last_readings[:-1]

            # Calculate daily consumption
            last_readings['daily_kwh_import'] = (last_readings['next_day_import'] - last_readings['kwh_import']).round(1)

            # Select only the relevant columns
            daily_consumption = last_readings[['date', 'daily_kwh_import']]

            merged_df = daily_consumption.reset_index() # full_df.merge(daily_consumption.reset_index(), on='date', how='left').fillna(0)

            response[device_name] = merged_df['daily_kwh_import'].to_list()

        if devices_qs.exists():
            response["dates"] = merged_df['date'].to_list()
        else:
            response["dates"] = []

        return response

    def get_periodic_device_usage(self, start_date, end_date, frequency):

        devices = self.datalog_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values("post_datetime", "device__name", "summary_energy_register_1")
        for reading in devices:
            print("READING::", reading["post_datetime"], "DEVICE NAME::", reading["device__name"], reading["summary_energy_register_1"])

        data_as_dataframe = pd.DataFrame(devices)
        if data_as_dataframe.empty:

            return {}

        else:

            device_grouping = data_as_dataframe.groupby("device__name")

            source_names = device_grouping.groups
            source_data = [device_grouping.get_group(i) for i in device_grouping.groups]

            resampled_device_data = list(map(lambda data, source_name,: dashboard_helpers.get_kwh_usage_periodically(data, source_name, frequency), source_data, source_names))
            print(resampled_device_data)

            final_data = pd.concat(resampled_device_data, axis=1).fillna(0).reset_index()

            return final_data.round(2).to_dict("list")

    def get_periodic_utility_usage(self, start_date, end_date, frequency):

        devices = self.datalog_set.filter(device__type__choice_name="UTILITY", post_datetime__gte=start_date, post_datetime__lte=end_date).values("post_datetime", "device__name", "summary_energy_register_1")
        #devices = self.datalog_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values("post_datetime", "device__name", "summary_energy_register_1")

        data_as_dataframe = pd.DataFrame(devices)
        if data_as_dataframe.empty:

            return {}

        else:

            device_grouping = data_as_dataframe.groupby("device__name")

            source_names = device_grouping.groups
            source_data = [device_grouping.get_group(i) for i in device_grouping.groups]

            resampled_device_data = list(map(lambda data, source_name,: dashboard_helpers.get_kwh_usage_periodically(data, source_name, frequency), source_data, source_names))

            final_data = pd.concat(resampled_device_data, axis=1).fillna(0).reset_index()

            return final_data.round(2).to_dict("list")

    def get_periodic_ipp_usage(self, start_date, end_date, frequency):

        devices = self.datalog_set.filter(device__type__choice_name="IPP", post_datetime__gte=start_date, post_datetime__lte=end_date).values("post_datetime", "device__name", "summary_energy_register_1")
        #devices = self.datalog_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values("post_datetime", "device__name", "summary_energy_register_1")

        data_as_dataframe = pd.DataFrame(devices)
        if data_as_dataframe.empty:

            return {}

        else:

            device_grouping = data_as_dataframe.groupby("device__name")

            source_names = device_grouping.groups
            source_data = [device_grouping.get_group(i) for i in device_grouping.groups]

            resampled_device_data = list(map(lambda data, source_name,: dashboard_helpers.get_kwh_usage_periodically(data, source_name, frequency), source_data, source_names))

            final_data = pd.concat(resampled_device_data, axis=1).fillna(0).reset_index()

            return final_data.round(2).to_dict("list")

    # @memoize(timeout=20)
    def get_time_of_use_raw_dataframe(self, start_date, end_date):

        try:
            devices = self.device_set.all()

            data = list(map(lambda device: [
                                                pd.DataFrame(device.filter_readings( start_date, end_date).values()),
                                                device.name
                                            ],
                                            devices
                            )
                        )

            data_frames = map(lambda device_data: dashboard_helpers.get_tou_raw(*device_data).get("data"), data)
            data_frames = filter(lambda value: not value.empty, data_frames)
            tou = reduce(lambda  left,right: pd.merge(left,right,on=['post_datetime'],
                                                        how='outer'), data_frames)

            tou.sort_values("post_datetime", inplace=True)
            tou.reset_index(inplace = True)
            tou = tou.ffill()

            tou.fillna("-", inplace=True)

            devices = tou.columns[2:]

            raw_dataframes = list(map(lambda name: dashboard_helpers.add_hours_n_cummulative_hours(tou[["post_datetime",name]]), devices))
            raw_dataframes = [frame for frame in raw_dataframes if isinstance(frame, pd.DataFrame)]
            dataframes = list(map(lambda frame: frame.drop("post_datetime", axis = 1), raw_dataframes))

            result = pd.concat(dataframes, axis = 1)
            result.insert(loc=0, column='post_datetime', value = tou["post_datetime"])
            result.fillna(0, inplace=True)#.to_csv("completed.csv", index = False)

            return result

        except TypeError:

            return False

    # @memoize(timeout=20)
    def get_time_of_use(self, start_date, end_date):

        result = self.get_time_of_use_raw_dataframe(start_date, end_date)

        if not isinstance(result, bool):

            try:
                device_names = result.columns[1:][::3]
                print("RESULT::", result)
                print("DEVICE NAME::", device_names)
                result.to_csv("results.csv")
                device_values = result.sum()
                device_values = {device_name:device_values.get(f"{device_name}_hours") for device_name, device_value in zip(device_names, device_values)}
                dict_values = result.round(2).to_dict(orient="index")

                response = {
                                "titles":list(result.columns),
                                "values":  [value for value in dict_values.values()],
                                "sums":device_values
                            }
            except:
                return {
                        "titles":[],
                        "values":  [],
                        "sums":{}
                    }

        else:
            return {
                        "titles":[],
                        "values":  [],
                        "sums":{}
                    }

        return response

    # # @memoize(timeout=20)
    # def get_diesel_overview(self):

    #     diesel_overview = []
    #     date = datetime.datetime.now()

    #     for i in range(12):

    #         first = date.replace(day=1)
    #         lastMonth = first - datetime.timedelta(days=1)
    #         date = lastMonth


    #         start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(first)

    #         month = start_date.strftime("%b, %Y")
    #         print("DATE : ", month)

    #         overview_history = FuelConsumption.objects.filter(
    #             start_date__gte=start_date, end_date__lte=end_date, branch=self.id
    #         )
    #         inputed_usage = 0
    #         for history in overview_history:

    #             try:
    #                 qty = float(history.quantity)
    #             except:
    #                 qty = 0

    #             inputed_usage += qty

    #         #inputed_usage = self.sum_monthly_diesel_input(start_date.year, start_date.month)

    #         # inputed_usage = self.month_end_diesel_balance_set\
    #         #                             .filter(date__month = start_date.month,
    #         #                                     date__year = start_date.year)\
    #         #                             .aggregate(Sum('quantity')).get("quantity__sum", 0) or 0

    #         # lastmonth_balance_qs = self.month_end_diesel_balance_set\
    #         #                             .filter(date__month = lastMonth.month,
    #         #                                     date__year = lastMonth.year)

    #         # thismonth_balance_qs = self.month_end_diesel_balance_set\
    #         #                             .filter(date__month = start_date.month,
    #         #                                     date__year = start_date.year)

    #         # lastmonth_balance = lastmonth_balance_qs.last().quantity if lastmonth_balance_qs.exists() else 0
    #         # thismonth_balance = thismonth_balance_qs.last().quantity if thismonth_balance_qs.exists() else 0
    #         # if thismonth_balance == 0:
    #         #     thismonth_balance = lastmonth_balance
    #         # inputed_usage = thismonth_balance - lastmonth_balance

    #         # print('THIS MONTH', lastmonth_balance_qs.last().quantity if lastmonth_balance_qs.exists() else 0)
    #         # print('LAST MONTH', thismonth_balance_qs.last().quantity if thismonth_balance_qs.exists() else 0)
    #         print('USAGE', inputed_usage)

    #         diesel_tarrif = self.cost_set\
    #                                     .filter(cost_type = "diesel",
    #                                             date__month = start_date.month,
    #                                             date__year = start_date.year).values("tarrif")\
    #                                     .aggregate(Avg('tarrif'))\
    #                                     .get("tarrif__avg") or 290
    #         print(diesel_tarrif)

    #         forcasted_energy_usage = self.get_forcast_usage(end_date, type="gen")
    #         forcasted_diesel_usage = forcasted_energy_usage * DIESEL_LTR_PER_KWH

    #         inputted_cost     = inputed_usage * diesel_tarrif
    #         forecasted_cost   = forcasted_energy_usage * diesel_tarrif

    #         diesel_usage_diff = forcasted_diesel_usage - inputed_usage
    #         diesel_cost_diff  = forecasted_cost - inputted_cost

    #         percentage_usage  = (diesel_usage_diff/(forcasted_diesel_usage or 1)) * 100
    #         percentage_cost   = (diesel_cost_diff/(forecasted_cost or 1)) * 100

    #         response = dict(
    #                         month=month,
    #                         inputted_usage=inputed_usage,
    #                         forecasted_usage=forcasted_diesel_usage,
    #                         inputted_cost=inputted_cost,
    #                         forecasted_cost=forecasted_cost,
    #                         diesel_difference=diesel_usage_diff,
    #                         cost_difference=diesel_cost_diff,
    #                         percentage_usage=percentage_usage,
    #                         percentage_cost=percentage_cost
    #                         )

    #         diesel_overview.append(response)

    #     return diesel_overview


    def get_diesel_overview(self):
        diesel_overview = []
        today = datetime.datetime.now()

        for _ in range(12):
            first_of_month = today.replace(day=1)
            last_month_end = first_of_month - datetime.timedelta(days=1)
            today = last_month_end

            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(first_of_month)
            month_label = start_date.strftime("%b, %Y")

            # --- 1. Inputted Usage from FuelConsumption ---
            fuel_qs = FuelConsumption.objects.filter(
                branch=self,
                start_date__gte=start_date,
                end_date__lte=end_date
            )
            inputted_usage = fuel_qs.aggregate(total_qty=Sum('quantity'))['total_qty'] or 0

            # --- 2. Avg price_per_litre from Cost where cost_type = diesel ---
            cost_qs = Cost.objects.filter(
                branch=self,
                cost_type='diesel',
                date__range=(start_date, end_date)
            )
            avg_price = cost_qs.aggregate(avg_price=Avg('price_per_litre'))['avg_price'] or 1100 #Assumed Diesel price to be N1,100

            # --- 3. Inputted Cost ---
            inputted_cost = inputted_usage * avg_price

            # --- 4. Forecasting (unchanged) ---
            forecasted_energy = self.get_forcast_usage(end_date, type="gen")
            forecasted_diesel = forecasted_energy * DIESEL_LTR_PER_KWH
            forecasted_cost = forecasted_diesel * avg_price

            diesel_diff = abs(forecasted_diesel - inputted_usage)
            cost_diff = abs(forecasted_cost - inputted_cost)
            pct_usage = min((diesel_diff / (forecasted_diesel or 1)) * 100, 100)
            pct_cost = min((cost_diff / (forecasted_cost or 1)) * 100, 100)

            diesel_overview.append({
                "month": month_label,
                "inputted_usage": round(inputted_usage, 2),
                "forecasted_usage": round(forecasted_diesel, 2),
                "inputted_cost": round(inputted_cost, 2),
                "forecasted_cost": round(forecasted_cost, 2),
                "diesel_difference": round(diesel_diff, 2),
                "cost_difference": round(cost_diff, 2),
                "percentage_usage": round(pct_usage, 2),
                "percentage_cost": round(pct_cost, 2),
            })

        return diesel_overview

    # @memoize(timeout=20)
    def get_utility_overview(self):

        utility_overview = []
        date = datetime.datetime.now()

        for i in range(12):

            first = date.replace(day=1)
            lastMonth = first - datetime.timedelta(days=1)
            date = lastMonth

            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(first)

            month = start_date.strftime("%b, %Y")

            inputed_usage = self.cost_set\
                                        .filter(date__month = start_date.month,
                                                date__year = start_date.year)\
                                        .aggregate(Sum('amount'), Sum('value'), Avg('tarrif'))

            amount = inputed_usage.get("amount__sum", 0) or 0
            units  = inputed_usage.get("value__sum", 0) or 0
            tarrif = inputed_usage.get("tarrif__avg", 0) or 0

            consumed_energy_dict = self.get_periodic_utility_usage(start_date, end_date, "monthly") # RETURNS DICT WITH INDIVIDUAL DEVICE CONSUMPTIONS
            energy_consumed_list = map(lambda device:sum(consumed_energy_dict[device]), list(consumed_energy_dict.keys())[1:])
            energy_consumed      = sum(energy_consumed_list)

            consumed_naira = energy_consumed*tarrif

            # START ############################## UPDC- Romays Garden, TRANSFORMER CONFIGURATION UPDATE

            # Transformer (device_type = Utility) is being supplied by a combination of EKEDC and Generator
            # True Utility supplied by EKEDC = Transformer - Generator

            if self.name == 'Romays Garden':
                # TOTAL ENERGY SOURCES
                total_energy_dict = self.get_periodic_device_usage(start_date, end_date, "monthly") # RETURNS DICT WITH INDIVIDUAL DEVICE CONSUMPTIONS
                total_energy_consumed_list = map(lambda device:sum(total_energy_dict[device]), list(total_energy_dict.keys())[1:])
                total_energy_consumed      = sum(total_energy_consumed_list)

                # ENERGY FROM GENERATORS
                gen = total_energy_consumed - energy_consumed

                # ENERGY FROM EKEDC = Transformer - Generator
                ekedc = energy_consumed - gen
                energy_consumed = ekedc

            # END ############################## UPDC- Romays Garden, TRANSGORMER CONFIGURATION UPDATE

            print(month, amount, units, energy_consumed, energy_consumed*tarrif)

            response = dict(
                            month=month,
                            purchased_kwh        =units,
                            energy_consumed_kwh  =energy_consumed,
                            purchased_naira      =amount,
                            energy_consumed_naira=consumed_naira,
                            difference_kwh   = energy_consumed - units,
                            difference_naira = consumed_naira - amount,
                            percentage       = (consumed_naira/(amount or 1))*100
                            )

            utility_overview.append(response)

        return utility_overview

    # @memoize(timeout=20)
    def get_ipp_overview(self):

        ipp_overview = []
        date = datetime.datetime.now()

        for i in range(12):

            first = date.replace(day=1)
            lastMonth = first - datetime.timedelta(days=1)
            date = lastMonth

            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(first)

            month = start_date.strftime("%b, %Y")

            inputed_usage = self.cost_set\
                                        .filter(date__month = start_date.month,
                                                date__year = start_date.year)\
                                        .aggregate(Sum('amount'), Sum('value'), Avg('tarrif'))

            amount = inputed_usage.get("amount__sum", 0) or 0
            units  = inputed_usage.get("value__sum", 0) or 0
            tarrif = inputed_usage.get("tarrif__avg", 0) or 0

            consumed_energy_dict = self.get_periodic_ipp_usage(start_date, end_date, "monthly") # RETURNS DICT WITH INDIVIDUAL DEVICE CONSUMPTIONS
            energy_consumed_list = map(lambda device:sum(consumed_energy_dict[device]), list(consumed_energy_dict.keys())[1:])
            energy_consumed      = sum(energy_consumed_list)

            consumed_naira = energy_consumed*tarrif

            print(month, amount, units, energy_consumed, energy_consumed*tarrif)

            response = dict(
                            month=month,
                            purchased_kwh        =units,
                            energy_consumed_kwh  =energy_consumed,
                            purchased_naira      =amount,
                            energy_consumed_naira=consumed_naira,
                            difference_kwh   = energy_consumed - units,
                            difference_naira = consumed_naira - amount,
                            percentage       = (consumed_naira/(amount or 1))*100
                            )

            ipp_overview.append(response)

        return ipp_overview

    def cache_diesel_entry_overview_history(self):
        days_in_year = sum(monthrange(2022, month)[1] for month in range(1, 13))
        first = datetime.date(2022, 1, 1)
        last = first + datetime.timedelta(days=days_in_year)
        self.get_diesel_entry_overview(first, last)

    def calculate_data(self, single_date, generators):
        start_date = single_date
        end_date = single_date + datetime.timedelta(days=1)
        single_date_str = single_date.strftime("%Y-%m-%d")

        quantity = FuelConsumption.get_quantity_for_day(self, single_date)
        id = FuelConsumption.get_id_for_entry(self, single_date)
        consumption = int(quantity)

        hours = sum(self.get_gen_hours_of_use(single_date, single_date + datetime.timedelta(days=1)))

        device_consumption_data = {}
        device_energy_per_litre = {}
        device_litres_per_hour = {}

        n = 1
        for generator in generators:
            device_consumption_data[f"Gen {n}"] = generator.get_total_kwh_for_period(start_date, end_date)
            if consumption != 0:
                device_energy_per_litre[f"Gen {n}"] = round((device_consumption_data[f"Gen {n}"] / consumption), 2)
            else:
                device_energy_per_litre[f"Gen {n}"] = 0

            if hours != 0:
                device_litres_per_hour[f"Gen {n}"] = round((consumption / hours), 2)
            else:
                device_litres_per_hour[f"Gen {n}"] = 0
            n += 1

        hours_str = time_helpers.convert_hours_to_str(hours)
        data = dict(
            fuel_consumption_id=id,
            date=single_date_str,
            quantity=consumption,
            hours_of_use=hours_str,
            energy_consumed=device_consumption_data,
            energy_per_litre=device_energy_per_litre,
            litres_per_hour=device_litres_per_hour
        )
        return data

    # @memoize(timeout=20)
    def get_diesel_entry_overview(self, year, month):
        cache_key = f'get_diesel_entry_overview_{self.id}_{year}_{month}'
        diesel_entries = cache.get(cache_key)
        if diesel_entries is not None:
            return diesel_entries

        days_in_month = calendar.monthrange(year, month)[1]
        first = datetime.date(year, month, 1)
        last = first + datetime.timedelta(days=days_in_month - 1)

        generators = Device.objects.filter(branch=self, type_id=1)

        overview_history = DieselOverviewHistory.objects.filter(
            start_date__gte=first, start_date__lte=last, branch=self.id
        )
        overview_history_by_date = {history.start_date: history for history in overview_history}

        diesel_entries = []
        for single_date in time_helpers.daterange(first, last):
            single_date_str = single_date.strftime("%Y-%m-%d")
            data = None
            history = overview_history_by_date.get(single_date_str)

            if history:
                data = history.data

            if data is None or self.should_update_data(data, self.calculate_data(single_date, generators)):
                data = self.calculate_data(single_date, generators)

                if history:
                    history.data = data
                    history.save()
                else:
                    DieselOverviewHistory.objects.create(start_date=single_date_str, branch=self.id, data=data)

            diesel_entries.append(data)
        cache.set(cache_key, diesel_entries, timeout=3600)

        return diesel_entries

    def should_update_data(self, existing_data, new_data):
        # Here we can implement more sophisticated logic if needed
        return existing_data != new_data

    # @memoize(timeout=20)
    def sum_monthly_diesel_input(self, year, month):

        daysInMonth = calendar.monthrange(year, month)[1]
        first = datetime.date(year, month, 1)
        last = first + datetime.timedelta(days=daysInMonth)

        diesel_entry = 0

        for single_date in time_helpers.daterange(first, last):
            consumption = FuelConsumption.objects.filter(branch=self.id, start_date = single_date).aggregate(Sum('quantity')).get("quantity__sum") or 0
            diesel_entry += consumption
            print('$$$$$$$$$')
            print(consumption)

        return diesel_entry

    # @memoize(timeout=20)
    def get_forcast_usage(self, end_date, type = False):

        if type == "gen":
            devices = self.device_set.filter(type__choice_name = "GENERATOR")
        elif type == "utility":
            devices = self.device_set.filter(type__choice_name__in = ["UTILITY", "IPP"])
        else:
            devices = self.device_set.all()

        baseline = sum([device.get_base_line(end_date)\
                        .get('baseline_energy')
                        .get('forecast', 0)
                        for device in devices])
        print("BASELINE : ", baseline)
        print(baseline)

        return baseline

    # @memoize(timeout=20)
    def get_forcast_vs_usage(self, end_date, type = False):

        if type == "gen":
            devices = self.device_set.filter(type__choice_name = "GENERATOR")
        elif type == "utility":
            devices = self.device_set.filter(type__choice_name__in = ["UTILITY", "IPP"])
        else:
            devices = self.device_set.all()

        base_lines = []
        usages     = []

        data = {}
        device = Device.objects.get(id = 4)
        original_date = end_date

        for device in devices:
            end_date = original_date
            data[device.name] = []

            for i in range(6):

                end_date = time_helpers.previous_month_new(end_date)
                last_month = end_date.replace(day=1)

                forcast = device.get_base_line(last_month)
                forcast['baseline_energy']["date"] = last_month.strftime("%b, %Y")

                data[device.name].append(forcast['baseline_energy'])
                end_date = last_month

        return data


    def __str__(self):
        return self.name

    def get_cost_of_per_kwh(self, start_date, end_date):

        devices = self.device_set.all()

        possible_types = {}

        for device_type in DeviceType.objects.all():# create list of possible device types ie gen ipp or diesel
            possible_types[device_type.choice_name]= list()

        for device in devices:

            data = device.get_cost_of_energy_daily(start_date, end_date)
            print(device.name)
            print(data)
            mean_kwh = data["amount"].mean()
            possible_types[device.type.choice_name].append(0 if mean_kwh != mean_kwh else mean_kwh)# CHECK IF RETURN VALUE IS NAN (NAN==NAN--> FALSE)

        IPP       = AVERAGE(possible_types["IPP"]) if possible_types["IPP"] else 0
        GENERATOR = AVERAGE(possible_types["GENERATOR"]) if possible_types["GENERATOR"] else 0
        UTILITY   = AVERAGE(possible_types["UTILITY"]) if possible_types["UTILITY"] else 0

        non_zero_costs = list(filter(lambda cost: cost>0, [IPP, GENERATOR, UTILITY])) # REMOVE ALL ZEROES BECAUSE THEY AFFECT THE GENERAL BLENDED COST
        blended_cost = 0 if not len(non_zero_costs) else round(AVERAGE(non_zero_costs), settings.DECIMAL_PLACES) #Avoid division by zero error check that array is not empty

        data = {
                    "ipp_per_kwh": round(IPP, settings.DECIMAL_PLACES),
                    "diesel_per_kwh": round(GENERATOR, settings.DECIMAL_PLACES),
                    "utility_per_kwh": round(UTILITY, settings.DECIMAL_PLACES),
                    "blended_cost_per_kwh": blended_cost,
                    "unit": "₦"
                }

        return data

    def get_report_data(self, end_date, period):

        start_date = time_helpers.get_start_date_from(end_date, period)
        end_date = end_date.replace(hour = 23, minute=59)
        total_energy = self.branch_total_energy(start_date, end_date)
        #total_energy = sum(list(self.get_total_energy(start_date, end_date).values())) or 0.001
        base_line = 0.001 #or sum([device.new_baseline(end_date) for device in self.device_set.all()]) or 0.00001

        total_energy = 0.001 if math.isinf(total_energy) else total_energy
        period_value = ((base_line/1)) if base_line else 0.001

        start_date_str = start_date.strftime("%d-%m-%Y %H:%M")
        end_date_str = end_date.strftime("%d-%m-%Y %H:%M")

        response = {
                    "period_score": {
                        "value": 0 if math.isinf(period_value) else  math.isinf(period_value) * 100,
                        "unit": "%",
                        "rate": 0 if math.isinf(period_value) else math.isinf(period_value) * 5
                    },
                    "total_energy_consumption": {
                        "value":total_energy,
                        "unit": "kWh",
                        "rate":  0 if math.isinf(period_value) else math.isinf(period_value) * 5
                    },
                    "papr": {
                        "metrics":{
                            "peak_to_avg_power_ratio": self.branch_demand(start_date_str, end_date_str, report = True)
                        }
                    },
                    "carbon_emmissions": {
                        "value": self.get_carbon_emmisions(start_date, end_date),
                        "unit": "tons",
                        "rate": 0
                    },
                    "baseline": {
                        "forcast":base_line,
                        "consumption":total_energy,
                        "unit":"kWh",
                        "rate":  0 if math.isinf(period_value) else period_value * 5
                    },
                    "source_consumption": self.get_fuel_consumption(start_date, end_date),
                    "load_imbalance": self.get_current_balance_issues(start_date, end_date),
                    # "fuel_consumption": self.get_gen_fuel_consumption(start_date, end_date),
                    # "generator_efficiency": self.gen_efficiency(end_date),
                    "daily_consumption": self.get_periodic_energy_daily(start_date, end_date, period),
                    "energy_consumption": self.get_forcast_vs_usage(start_date, end_date),
                    "demand_statistic": self.energy_stats(start_date, end_date),
                    "cost_implication":self.energy_cost(start_date, end_date),
                    "time_of_use":self.get_hours_of_use(start_date, end_date),
                    # #"power_demand":self.demand_stats(start_date, end_date)
                    "power_demand":self.branch_demand(start_date_str, end_date_str, stats = True)
                }
        print(response)
        return response

    def get_separate_report_data(self, end_date, period):
        start_date = time_helpers.get_start_date_from(end_date, period)
        end_date = end_date.replace(hour = 23, minute=59)
        total_energy = self.branch_total_energy(start_date, end_date)
        base_line = 0

        for device in self.device_set.all():
            base_line_data = device.get_base_line(end_date)
            base_line += base_line_data['baseline_energy']['forecast']

        period_value = ((base_line/1)) if base_line else 0.001
        total_energy = 0.001 if math.isinf(total_energy) else total_energy
        response = {
            "baseline": {
                        "forecast":base_line,
                        "consumption":total_energy,
                        "unit":"kWh",
                        "rate":  0 if math.isinf(period_value) else period_value * 5,
                        "forecast_to_consumption_percentage": round(((total_energy/base_line)*100),2)
                    }
        }
        print(response)
        return response

    def demand_stats(self, start_date, end_date):

        devices = self.device_set.all()

        aggs = []

        for device in devices:

            aggregate = device.get_agg_kwh_for_period(start_date, end_date)
            aggs.append(aggregate.get("min",0))
            aggs.append(aggregate.get("max",0))
            aggs.append(aggregate.get("avg",0))

        return dict(
                        minimum = {
                                    "kw":round(min(aggs), 2),
                                    "kva":round(min(aggs)/KW_TO_KVA_MULTIPLIER, 2)
                                    },
                        maximum = {
                                    "kw":round(max(aggs), 2),
                                    "kva":round(max(aggs)/KW_TO_KVA_MULTIPLIER, 2)
                                    },
                        average = {
                                    "kw":round(np.mean(aggs), 2),
                                    "kva":round(np.mean(aggs)/KW_TO_KVA_MULTIPLIER, 2)
                                    }
                    )

    def energy_cost(self, start_date, end_date):

        energy_costs = []

        for device in self.device_set.all():

            device_raw_data = device.get_billing_data(start_date, end_date)
            present_totals = device_raw_data.get("totals").get("present_total")

            energy_costs.append({
                                    "branch":device.branch.name,
                                    "device":device.name,
                                    "demand":present_totals.get("usage_kwh"),
                                    "cost":present_totals.get("value_naira")
                                })

        return energy_costs

    def gen_efficiency(self, end_date):

        efficiency_values = []

        for device in self.device_set.all().filter(type="1"):

            device_raw_data = device.get_gen_efficiency(end_date)

            efficiency_values.append({
                                        "name":device.name,
                                        "size_efficiency":device_raw_data.get("usage"),
                                        "recommendation":device_raw_data.get("remarks"),
                                        "gen_size":device_raw_data.get("size")
                                    })

        return efficiency_values

    def get_current_balance_issues(self, start_date, end_date):

        efficiency_values = []

        for device in self.device_set.all():

            device_raw_data = device.get_current_balance_issues_multiple(start_date, end_date)

            for imbalance in device_raw_data.get("values"):
                post_time = imbalance["post_datetime"]
                minimum = min(imbalance["current_l1"], imbalance["current_l2"], imbalance["current_l3"])
                maximum = max(imbalance["current_l1"], imbalance["current_l2"], imbalance["current_l3"])
                imbalance_value = imbalance["imbalance"]
                efficiency_values.append({
                                            "min": minimum,
                                            "max": maximum,
                                            "imbalance": round(imbalance_value*100, 2),
                                            "time": post_time,
                                        })


        efficiency_values.sort( key = lambda x:x["imbalance"], reverse=True)

        return efficiency_values


    def energy_stats(self, start_date, end_date):

        energy_stats = []

        for device in self.device_set.all():

            device_raw_kwh = device.get_date_of_min_max_comsumption(start_date, end_date)
            daily_average_energy = device_raw_kwh.get("average_daily", 0)

            device_raw_kw  = device.get_agg_kwh_for_period(start_date, end_date)

            data = {
                        "name":device.name,
                        "daily_avg_usage":daily_average_energy if daily_average_energy > 1 else 0,
                        "max_energy_usage":{
                                                "value":device_raw_kwh.get("max_usage_day", 0).get("kwh"),
                                                "date": device_raw_kwh.get("max_usage_day", 0).get("dates_formatted"),
                                                "unit":"kWh"
                                            },
                        "min_energy_usage":{
                                                "value":device_raw_kwh.get("min_usage_day", 0).get("kwh"),
                                                "date": device_raw_kwh.get("min_usage_day", 0).get("dates_formatted"),
                                                "unit":"kWh"
                                            },
                        "peak_avg_usage_day":{
                                                "value":device_raw_kwh.get("max_avg_usage", 0).get("kwh"),
                                                "date": device_raw_kwh.get("max_avg_usage", 0).get("dates_formatted"),
                                                "unit":"kWh"
                                            },
                        "min_avg_usage_day":{
                                                "value":device_raw_kwh.get("min_avg_usage", 0).get("kwh"),
                                                "date": device_raw_kwh.get("min_avg_usage", 0).get("dates_formatted"),
                                                "unit":"kWh"
                                            },
                        "max_demand_date":{
                                                "value":round(device_raw_kw.get("max", 0), 2),
                                                "day": device_raw_kw.get("max_demand_date", 0),
                                                "unit":"kW"
                                            },
                        "min_demand_date":{
                                                "value":round(device_raw_kw.get("min", 0), 2),
                                                "day": device_raw_kw.get("min_demand_date", 0),
                                                "unit":"kW"
                                            }
                    }
            energy_stats.append(data)

        return energy_stats

    def get_total_energy(self, start_date, end_date):

        devices_data = {}

        for device in self.device_set.all():
            device_raw_data = device.get_total_energy_at_end_of_period(start_date, end_date)
            device_energy_usage = device_raw_data.get('usage')
            devices_data[device.name] = device_energy_usage

        return devices_data

    def get_gen_total_energy(self, start_date, end_date):

        devices_data = {}

        for device in self.device_set.all().filter(type='1'):
            device_raw_data = device.get_total_energy_at_end_of_period(start_date, end_date)
            device_energy_usage = device_raw_data.get('usage')
            devices_data[device.name] = device_energy_usage

        return devices_data

    def get_periodic_energy(self, start_date, end_date, frequency):

        data = {}

        for device in self.device_set.filter(type__choice_name = "GENERATOR"):
            device_raw_data = device.get_periodic_energy(start_date, end_date, frequency)

            try:
                for date, diff in zip(device_raw_data['dates'], device_raw_data["diff"]):

                    date = time_helpers.to_month_and_year(date)# CONVERT TO CLEAN DATE HERE
                    if not data.get(date, False):
                        data[date] = {
                                        "diesel_consumed":0,
                                        "datetime":date
                                    }

                    data[date]["diesel_consumed"] += diff * DIESEL_LTR_PER_KWH
            except:
                pass

        return list(data.values())

    def get_periodic_energy_daily(self, start_date, end_date, frequency):

        data = {}

        for device in self.device_set.filter(type__choice_name = "GENERATOR"):
            device_raw_data = device.get_periodic_energy(start_date, end_date, 'daily')

            try:
                for date, diff in zip(device_raw_data['dates'], device_raw_data["diff"]):

                    date = time_helpers.to_month_and_year(date)# CONVERT TO CLEAN DATE HERE
                    if not data.get(date, False):
                        data[date] = {
                                        "diesel_consumed":0,
                                        "datetime":date
                                    }

                    data[date]["diesel_consumed"] += diff * DIESEL_LTR_PER_KWH
            except:
                pass

        return list(data.values())

    def get_fuel_consumption(self, start_date, end_date):
        energy_consumed = self.get_total_energy(start_date, end_date)
        hours_of_use = self.get_hours_of_use(start_date, end_date)

        raw_data = zip( hours_of_use["devices"], hours_of_use["hours"])
        fuel_consumption_data = []

        for name, hours in raw_data:
            device_data = dict(name = name, hours = hours, energy=energy_consumed[name], cost=energy_consumed[name]*DIESEL_LTR_PER_KWH*300)
            fuel_consumption_data.append(device_data)

        return fuel_consumption_data

    def get_gen_fuel_consumption(self, start_date, end_date):
        energy_consumed = self.get_gen_total_energy(start_date, end_date)
        hours_of_use = self.get_gen_hours_of_use(start_date, end_date)

        raw_data = zip( hours_of_use["devices"], hours_of_use["hours"])
        fuel_consumption_data = []

        for name, hours in raw_data:
            device_data = dict(name = name, hours = hours, energy=energy_consumed[name], cost=energy_consumed[name]*DIESEL_LTR_PER_KWH*300)
            fuel_consumption_data.append(device_data)

        return fuel_consumption_data

    # def get_carbon_emmisions(self, start_date, end_date):

    #     branch_emissions = []

    #     for device in self.device_set.all():

    #         emissions = device.get_carbon_emmisions_by_kwh_consumed(start_date, end_date, kwh_consumed = False)

    #         branch_emissions.append(emissions.get('value', 0 ))

    #     return sum(branch_emissions)

    def get_carbon_emmisions(self, start_date, end_date):
        """
        Optimized: Bulk fetch kWh for all devices, then sum CO2 by device type.
        """
        devices = list(self.device_set.all().select_related('type'))
        device_ids = [d.id for d in devices]
        type_map = {d.id: d.type.choice_name.lower() for d in devices}

        # Bulk fetch min/max kWh for all devices
        dql = (
            Datalog.objects
            .filter(device_id__in=device_ids, post_datetime__gte=start_date, post_datetime__lte=end_date)
            .values("device_id")
            .annotate(min_val=Min("summary_energy_register_1"), max_val=Max("summary_energy_register_1"))
        )
        kwh_map = {row["device_id"]: (row["min_val"] or 0, row["max_val"] or 0) for row in dql}

        total_co2 = 0.0
        for device in devices:
            mn, mx = kwh_map.get(device.id, (0, 0))
            kwh = mx - mn
            typ = type_map[device.id]
            if typ == "ipp":
                co2 = (CO2_KG_EMMISSIONS_PER_KWH_LNG * kwh) / 1000
            elif typ == "generator":
                co2 = (CO2_KG_EMMISSIONS_PER_KWH_DIESEL * kwh) / 1000
            elif typ == "utility":
                co2 = (CO2_KG_EMMISSIONS_PER_KWH_UTILITY * kwh) / 1000
            else:
                co2 = 0
            total_co2 += co2

        return total_co2

    # @memoize(timeout=20)
    def get_devices_overview(self, start_date, end_date):

        devices = Device.objects.filter(branch=self)

        total_energy = 0
        energy_list = 0
        papr = self.branch_demand(start_date, end_date).get('papr')
        gen_efficiency = 0
        diesel_usage = 0
        num_devices = 0
        diesel_accuracy = 0
        utility_accuracy = 0
        blended_cost = self.get_branch_blended_cost(start_date, end_date)

        for device in devices:

            energy = device.get_total_kwh_for_period(start_date, end_date)
            total_energy += energy

            energy_cost = device.get_cost_of_energy(start_date, end_date)
            if energy_cost is not None:
                energy_list += energy_cost
                #num_devices += 1

            generator_efficiency = device.get_gen_efficiency(end_date).get('usage')
            if generator_efficiency is not None:
                gen_efficiency += int(generator_efficiency)
                num_devices += 1

            # diesel = device.get_fuel_consumption_score_cards(start_date, end_date).get('diesel_usage')
            # if diesel is not None:
            #     diesel_usage += diesel
            #     num_devices += 1

        generator_efficiency = 0 if num_devices == 0 else gen_efficiency / num_devices
        diesel_usage = 0 if num_devices == 0 else diesel_usage / num_devices

        device_detail = dict(total_energy = round(total_energy, 2),
                            energy_cost = round(energy_list, 2),
                            papr = papr,
                            generator_efficiency = round(generator_efficiency, 2),
                            diesel_usage = round(diesel_usage, 2),
                            fuel_efficiency = 0,
                            diesel_accuracy = diesel_accuracy,
                            utility_accuracy = utility_accuracy,
                            blended_cost = blended_cost)

        return device_detail

    # @memoize(timeout=20)
    def get_devices_overview_2(self, start_date, end_date):

        detail = self.get_devices_overview(start_date, end_date)
        print("||||||||||||||||||||||||||||")
        print("||||||||||||||||||||||||||||")
        print("||||||||||||||||||||||||||||")
        print("DEVICE DETAIL", detail)
        print("||||||||||||||||||||||||||||")
        print("||||||||||||||||||||||||||||")
        print("||||||||||||||||||||||||||||")

        branches_detail = dict( branch_id = self.id,
                                name = self.name,
                                organisation = self.client.name,
                                total_energy = detail.get('total_energy'),
                                energy_cost = detail.get('energy_cost'),
                                diesel_accuracy = detail.get('diesel_accuracy'),
                                utility_accuracy = detail.get('utility_accuracy'),
                                blended_cost = detail.get('blended_cost'),
                                papr = detail.get('papr'),
                                generator_efficiency = detail.get('generator_efficiency'),
                                diesel_usage = detail.get('diesel_usage'),
                                fuel_efficiency = detail.get('fuel_efficiency'),
                                co2 = self.get_carbon_emmisions(start_date, end_date),
                                baseline=0
                                )

        return branches_detail

    # # @memoize(timeout=20)
    # def client_admin_header_endpoint_total_energy(self, start_date, end_date):

    #     devices = Device.objects.filter(branch=self)

    #     total_energy = 0

    #     for device in devices:

    #         energy = device.get_total_kwh_for_period(start_date, end_date)
    #         total_energy += energy

    #     device_detail = dict(total_energy = round(total_energy, 2))

    #     return device_detail

    def client_admin_header_endpoint_total_energy(self, start_date, end_date):
        """
        Optimized: Bulk fetch kWh for all devices in this branch.
        """
        devices = Device.objects.filter(branch=self)
        device_ids = [d.id for d in devices]

        dql = (
            Datalog.objects
            .filter(device_id__in=device_ids, post_datetime__gte=start_date, post_datetime__lte=end_date)
            .values("device_id")
            .annotate(min_val=Min("summary_energy_register_1"), max_val=Max("summary_energy_register_1"))
        )
        total_energy = sum((row["max_val"] or 0) - (row["min_val"] or 0) for row in dql)
        return dict(total_energy=round(total_energy, 2))

    "<<<<<<<<<<<<<<<<<<<<<<<< RESELLER ADMIN START >>>>>>>>>>>>>>>>>>>>>>>>"

    # @memoize(timeout=20)
    def reseller_devices_overview(self, start_date, end_date):

        devices = Device.objects.filter(branch=self)

        total_energy = 0
        last_month_energy = 0
        last_month_energy_cost_sum = 0
        energy_cost_sum = 0
        papr = self.branch_demand(start_date, end_date).get('papr')
        meter_reading = 0

        for device in devices:

            latest_kwh_import = Reading.objects.filter(device=device).aggregate(Max('post_datetime'))['post_datetime__max']

            if latest_kwh_import is not None:
                latest_reading = Reading.objects.get(device=device, post_datetime=latest_kwh_import)
                meter_reading += latest_reading.kwh_import
                print(f"The latest kwh_import reading is: {meter_reading}")
            else:
                print(f"No readings found for device {device.name}")

            energy = round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
            total_energy += energy

            last_month_usage = device.get_billing_data(start_date, end_date)["totals"]["previous_total"]["usage_kwh"]
            last_month_energy += last_month_usage

            print(f'((((((((((((((((((({device.name})))))))))))))))))))')
            print(f'((((((((((((((((((({energy})))))))))))))))))))')
            print(f'((((((((((((((((((({last_month_usage})))))))))))))))))))')
            print(f'((((((((((((((((((({meter_reading})))))))))))))))))))')


            try:
                tariff = AdminTariff.objects.get(branch=self)
                energy_cost = energy * tariff.amount
                last_month_energy_cost = last_month_usage * tariff.amount
                last_month_energy_cost_sum += last_month_energy_cost
                energy_cost_sum += energy_cost
            except AdminTariff.DoesNotExist:
                energy_cost = 0
                last_month_energy_cost = last_month_usage
                last_month_energy_cost_sum += last_month_energy_cost
                energy_cost_sum += energy_cost

        device_detail = dict(total_energy = round(total_energy, 2),
                            meter_reading = round(meter_reading, 2),
                            energy_cost = round(energy_cost_sum, 2),
                            last_month_energy = round(last_month_energy, 2),
                            last_month_energy_cost = round(last_month_energy_cost_sum, 2),
                            papr = papr)

        return device_detail


    # @memoize(timeout=20)
    def reseller_branches_overview(self, start_date, end_date):

        try:
            tariff = AdminTariff.objects.get(branch=self)
            amount = tariff.amount
        except AdminTariff.DoesNotExist:
            amount = 0

        detail = self.reseller_devices_overview(start_date, end_date)

        branches_detail = dict( branch_id = self.id,
                                name = self.name,
                                total_energy = detail.get('total_energy'),
                                meter_reading = detail.get('meter_reading'),
                                tariff = amount,
                                bill = detail.get('energy_cost'),
                                co2 = self.get_carbon_emmisions(start_date, end_date)
                                )

        return branches_detail

    # @memoize(timeout=20)
    def reseller_energy_stats(self, start_date, end_date):

        devices = Device.objects.filter(branch=self)

        device_demand = []

        for device in devices:

            total_kwh = round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
            agg_data = device.get_agg_kwh_for_period(start_date, end_date)
            min_val = round(agg_data.get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = round(agg_data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = round(agg_data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            device_id = device.id

            try:
                tariff = AdminTariff.objects.get(branch=self)
                amount = tariff.amount
                id = tariff.id
            except AdminTariff.DoesNotExist:
                amount = 0
                id = 0

            device_demand.append({"device_name": device.name,
                                  "unit1": "KVA",
                                  "min_kva": min_val,
                                  "max_kva": max_val,
                                  "avg_kva": avg_val,
                                  "unit2": "KWH",
                                  "total_kwh": total_kwh,
                                  "bill": total_kwh*amount,
                                  "tariff": amount,
                                  "tariff_id": id,
                                  "device_id": device_id,
                                  })
        return device_demand

    "<<<<<<<<<<<<<<<<<<<<<<<< RESELLER ADMIN END >>>>>>>>>>>>>>>>>>>>>>>>"

    def branch_demand(self, start_date, end_date, report=False, stats=False):

        devices = Device.objects.filter(branch=self)
        device_demand = []

        for device in devices:
            agg_data = device.get_agg_kwh_for_period_within_op_time(start_date, end_date)

            # Handle None values safely
            min_val_raw = agg_data.get("min")
            max_val_raw = agg_data.get("max")
            avg_val_raw = agg_data.get("avg")

            # Set default values if None
            min_val = 0 if min_val_raw is None else round(min_val_raw/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = 0 if max_val_raw is None else round(max_val_raw/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = 0 if avg_val_raw is None else round(avg_val_raw/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)

            # Calculate PAPR safely
            papr = 0
            if max_val != 0 and avg_val is not None:
                papr = round(avg_val/max_val, settings.DECIMAL_PLACES)

            device_demand.append({"device_name": device.name, "unit": "kVA", "min": min_val, "max": max_val, "avg": avg_val, "papr": papr})

        power_factor = KW_TO_KVA_MULTIPLIER

        # Calculate overall branch PAPR
        branch_papr = 0
        if device_demand:  # Only calculate if we have device data
            # Get max and avg values across all devices
            max_values = [d['max'] for d in device_demand if d['max'] is not None]
            avg_values = [d['avg'] for d in device_demand if d['avg'] is not None]

            # Calculate branch PAPR safely
            branch_max = max(max_values) if max_values else 0
            branch_avg = sum(avg_values) / len(avg_values) if avg_values else 0

            if branch_max > 0:
                branch_papr = round(branch_avg / branch_max, settings.DECIMAL_PLACES)

        if report:
            return device_demand

        if stats:
            return device_demand
        else:
            return {"unit": "KVA", "papr": branch_papr, "p.f": power_factor, "devices_demands": device_demand}

    # @memoize(timeout=20)
    def energy_stats(self, start_date, end_date):

        devices = Device.objects.filter(branch=self)

        device_demand = []

        for device in devices:

            total_kwh = round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
            blended_cost = 0
            agg_data = device.get_agg_kwh_for_period(start_date, end_date)
            min_val = round(agg_data.get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = round(agg_data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = round(agg_data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            if device.is_gen:
                generator_efficiency = round((device.get_gen_efficiency(end_date).get('max_load')/device.gen_size)*100)
            else:
                generator_efficiency = '-'

            device_demand.append({"device_name": device.name,
                                  "unit1": "KVA",
                                  "min_kva": min_val,
                                  "max_kva": max_val,
                                  "avg_kva": avg_val,
                                  "unit2": "KWH",
                                  "total_kwh": total_kwh,
                                  "blended_cost": blended_cost,
                                  "generator_efficiency": generator_efficiency,
                                  })
        return device_demand

    # @memoize(timeout=20)
    def get_branch_uptime_outside_operating_hours(self, start_date, end_date):

        present = datetime.datetime.today()
        present_str = present.strftime("%Y-%m-%d")
        start_date_str = start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")
        # date_data_qs = BranchOTDHistory.objects.filter(start_date=start_date_str, end_date=end_date_str, branch=self.id)

        # if date_data_qs.exists():

        #     response = date_data_qs.first().data

        # else:

        devices = Device.objects.filter(branch=self)
        generators = []
        for device in devices:
            if device.type.id == 1:
                generators.append(device)

        response = [generator.get_device_uptime_outside_operating_hours(start_date, end_date) for generator in generators]

        # if not date_data_qs.exists() and start_date_str < present_str:
        #     BranchOTDHistory.objects.create(start_date=start_date_str, end_date=end_date_str, branch=self.id, data=response)

        return response

    def send_daily_otd(self):

        start_date, end_date = time_helpers.get_start_and_end_dates()
        start_date = time_helpers.convert_date(start_date)
        end_date = time_helpers.convert_date(end_date)

        title = "Wyre-Operating-Hours-Deviation-Report"
        #receievers = list(self.client.email)
        receievers = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        # receievers = ['<EMAIL>']

        otd = self.get_branch_uptime_outside_operating_hours(start_date, end_date)
        # if all(item['uptime_outside_operating_hours'] == '0 hours' for item in otd):
        #     message = "No devices came on outside operating hours for the given period"
        #     return message
        message = f'Branch: {self.name}\n'
        for item in otd:
            device = Device.objects.get(id=item['device_id'])
            device_name = device.name
            message += f"Device: {device_name}, Uptime outside operating hours: {item['uptime_outside_operating_hours']}\n"

        response = mailgun.Mailer.send_daily_otd(2, title, message, receievers)
        return response

    # @memoize(timeout=20)
    def get_branch_baseline(self, end_date):

        devices = Device.objects.filter(branch=self)
        baseline = 0

        for device in devices:

            baseline_energy = device.get_base_line(end_date).get("baseline_energy")
            baseline += baseline_energy['forecast']

        return baseline

    # @memoize(timeout=20)
    def branch_total_energy(self, start_date, end_date):

        devices = Device.objects.filter(branch=self)
        total_energy = 0

        for device in devices:

            energy = round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
            total_energy += energy

        return total_energy

    # @memoize(timeout=20)
    def branch_total_energy_generated_by_sources_and_consumed_by_loads(self, start_date, end_date):
        sources = Device.objects.filter(branch=self, is_source=True)
        loads = Device.objects.filter(branch=self, is_load=True)

        total_energy_from_sources = sum(
            round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
            for device in sources
        )

        total_energy_consumed_by_loads = sum(
            round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
            for device in loads
        )

        percentage_load_consumption = (total_energy_consumed_by_loads / total_energy_from_sources) * 100 if total_energy_from_sources else 0

        return total_energy_from_sources, total_energy_consumed_by_loads, percentage_load_consumption

    # @memoize(timeout=20)
    def get_diesel_entry_sum(self, start_date, end_date):

        # Get all the entries for the given branch and month
        entries = FuelConsumption.objects.filter(branch=self, start_date__gte=start_date, end_date__lte=end_date)
        print('((((((((((((( ENTRIES START )))))))))))))')
        print(f'((((((((((((( ENTRIES: {entries})))))))))))))')
        print('((((((((((((( ENTRIES END )))))))))))))')

        # Calculate the sum of quantity for all entries
        total_quantity = entries.aggregate(sum=Sum('quantity'))['sum'] or 0.0

        return total_quantity

    # @memoize(timeout=20)
    # def get_branch_blended_cost(self, start_date, end_date):

    #     latest_cost = Cost.objects.filter(branch=self, cost_type='diesel').aggregate(Max('date'))
    #     if latest_cost is None:
    #         diesel_cost = 0
    #     else:
    #         print(f'(((((((({latest_cost}))))))))')
    #         latest_cost_date = latest_cost['date__max']
    #         latest_cost = Cost.objects.filter(branch=self, cost_type='diesel', date=latest_cost_date).first()
    #         print(f'(((((((({latest_cost}))))))))')
    #         print(f'(((((((({latest_cost}))))))))')
    #         print(f'(((((((({latest_cost}))))))))')

    #         if latest_cost is None:
    #             diesel_cost = 0
    #         else:

    #             ppl = latest_cost.price_per_litre

    #             diesel_cost = self.get_diesel_entry_sum(start_date, end_date) * ppl
    #             print(f'((((((((((((( DIESEL COST: {diesel_cost})))))))))))))')

    #     # diesel_cost = Cost.get_branch_amount(branch=self, cost_type='diesel')
    #     ipp_cost = Cost.get_branch_amount(branch=self, cost_type='ipp')
    #     prepaid_cost = Cost.get_branch_amount(branch=self, cost_type='pre-paid')
    #     postpaid_cost = Cost.get_branch_amount(branch=self, cost_type='post-paid')
    #     total_cost = prepaid_cost + diesel_cost + ipp_cost + postpaid_cost

    #     total_kwh = 0.0001

    #     devices = Device.objects.filter(branch=self)
    #     for device in devices:
    #         kwh_consumed = device.get_total_kwh_for_period(start_date, end_date)
    #         total_kwh += kwh_consumed

    #         # if device.type.choice_name.lower() == "ipp":
    #         #     kwh_consumed = device.get_total_kwh_for_period(start_date, end_date)
    #         #     total_kwh += kwh_consumed

    #         # if device.type.choice_name.lower() == "generator":
    #         #     kwh_consumed = device.get_total_kwh_for_period(start_date, end_date)
    #         #     total_kwh += kwh_consumed

    #         # if device.type.choice_name.lower() == "utility":
    #         #     kwh_consumed = device.get_total_kwh_for_period(start_date, end_date)
    #         #     total_kwh += kwh_consumed

    #             # tariff = Tariff.get_device_tariff(device.id)
    #             # total_cost += tariff * kwh_consumed

    #     blended_cost = round((total_cost/total_kwh), 2)

    #     return blended_cost

    def get_branch_blended_cost(self, start_date, end_date):
        """
        1) Grab the latest Cost record of each type in ONE query.
        2) Sum them (diesel uses quantity * price_per_litre; others use amount).
        3) Divide by total kWh from devices (using optimized get_total_kwh_for_period).
        """
        # --- 1) Fetch latest Cost per cost_type ---
        # Requires PostgreSQL: DISTINCT ON cost_type picks the row with the greatest date per type.
        latest_costs = (
            Cost.objects
                .filter(branch=self)
                .order_by('cost_type', '-date')
                .distinct('cost_type')
        )

        total_cost = 0
        for c in latest_costs:
            if c.cost_type == 'diesel':
                total_cost += (c.quantity or 0) * (c.price_per_litre or 0)
            else:
                total_cost += (c.amount or 0)

        # --- 2) Sum kWh across all devices in period ---
        # Using the optimized per-device method
        total_kwh = 0
        for device in self.device_set.all():
            total_kwh += device.get_total_kwh_for_period(start_date, end_date)

        # avoid zero division
        total_kwh = total_kwh or 0.0001

        return round(total_cost / total_kwh, 2)


    def get_weekly_fuel_consumption_entries(self, dates):
        entries = FuelConsumption.objects.filter(branch=self, start_date__gte=dates[0], end_date__lte=dates[-1])
        weekly_fuel_consumption = {}

        # Iterate over the dates
        for date in dates:
            weekly_fuel_consumption[date] = 0

            # Compare the dates with entries' start_date and add matching amounts
            for entry in entries:
                if entry.start_date.strftime('%Y-%m-%d') == date:
                    weekly_fuel_consumption[date] += entry.quantity

        # print('(((((((((((((((((((((( weekly_fuel_consumption ))))))))))))))))))))))')
        # print(f'(((((((((((((((((((((( {weekly_fuel_consumption} ))))))))))))))))))))))')
        return weekly_fuel_consumption

    def check_branch_weekly_fuel_entry(self, dates):
        weekly_fuel_consumption = self.get_weekly_fuel_consumption_entries(dates)
        for date in dates:
            if weekly_fuel_consumption[date] == 0:
                return True
        return False

    def get_branch_band_from_utility_time_of_use(self, time_of_use):
        if time_of_use <= 216.00:
            band = 'E'
            tariff = 43.27
        if time_of_use > 216.00 and time_of_use <= 324.00:
            band = 'D'
            tariff = 45.29
        if time_of_use > 324.00 and time_of_use <= 432.00:
            band = 'C'
            tariff = 53.41
        if time_of_use > 432.00 and time_of_use <= 540.00:
            band = 'B'
            tariff = 62.48
        if time_of_use > 540.00:
            band = 'A'
            tariff = 209.05
        return band, tariff

    # def branch_total_energy_and_solar_hours(self, start_date, end_date):

    #     devices = Device.objects.filter(branch=self)
    #     total_energy = 0
    #     total_solar_hours = 0

    #     for device in devices:

    #         energy = round(device.get_total_kwh_for_period(start_date, end_date), settings.DECIMAL_PLACES)
    #         solar_hours = round(device.get_solar_hours_consumption(start_date, end_date), settings.DECIMAL_PLACES)

    #         total_energy += energy
    #         total_solar_hours+= solar_hours


    #     return total_energy, total_solar_hours
    

    def branch_total_energy_and_solar_hours(self, start_date, end_date):
        from django.db import close_old_connections, connections
        from concurrent.futures import ThreadPoolExecutor, as_completed

        devices = list(self.device_set.all().only("id"))
        total_energy = 0.0
        total_solar = 0.0

        def work(dev):
            close_old_connections()
            try:
                e = dev.get_total_kwh_for_period(start_date, end_date)
                s = dev.get_solar_hours_consumption(start_date, end_date)
                return e, s
            finally:
                connections.close_all()

        with ThreadPoolExecutor(max_workers=8) as ex:
            futures = {ex.submit(work, d): d for d in devices}
            for fut in as_completed(futures):
                e, s = fut.result()
                total_energy += e
                total_solar += s

        return round(total_energy, 2), round(total_solar, 2)
    

    def branch_operational_demand(self, start_date, end_date):

        devices = Device.objects.filter(branch=self)
        operational_device_demand = []
        non_operational_device_demand = []
        weekends_device_demand = []

        total_operational_energy = 0
        total_non_operational_energy = 0
        total_weekends_energy = 0

        for device in devices:
            operational_agg_data = device.get_agg_kwh_for_period_within_op_time(start_date, end_date)
            min_val = round(operational_agg_data.get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = round(operational_agg_data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = round(operational_agg_data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            papr = 0
            if max_val != 0:
                papr = round(avg_val/max_val, settings.DECIMAL_PLACES)

            operational_device_demand.append({"device_name": device.name, "min": min_val, "max": max_val, "avg": avg_val, "papr": papr})

            non_operational_agg_data = device.get_agg_kwh_for_period_outside_op_time(start_date, end_date)
            min_val = round(non_operational_agg_data.get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = round(non_operational_agg_data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = round(non_operational_agg_data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            papr = 0
            if max_val != 0:
                papr = round(avg_val/max_val, settings.DECIMAL_PLACES)

            non_operational_device_demand.append({"device_name": device.name, "min": min_val, "max": max_val, "avg": avg_val, "papr": papr})


            weekends_agg_data = device.get_agg_kwh_for_period_on_weekends(start_date, end_date)
            min_val = round(weekends_agg_data.get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            max_val = round(weekends_agg_data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            avg_val = round(weekends_agg_data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
            papr = 0
            if max_val != 0:
                papr = round(avg_val/max_val, settings.DECIMAL_PLACES)

            weekends_device_demand.append({"device_name": device.name, "min": min_val, "max": max_val, "avg": avg_val, "papr": papr})

            total_operational_energy += operational_agg_data.get("kWh")
            total_non_operational_energy += non_operational_agg_data.get("kWh")
            total_weekends_energy += weekends_agg_data.get("kWh")

        operational_demand = {
            "peak": max(d["max"] for d in operational_device_demand),
            "average": max(d["avg"] for d in operational_device_demand),
            "minimum": max(d["min"] for d in operational_device_demand),
            "total_energy": f"{total_operational_energy}kWh",
            "unit": "kVA"
        }

        non_operational_demand = {
            "peak": max(d["max"] for d in non_operational_device_demand),
            "average": max(d["avg"] for d in non_operational_device_demand),
            "minimum": max(d["min"] for d in non_operational_device_demand),
            "total_energy": f"{total_non_operational_energy}kWh",
            "unit": "kVA"
        }

        weekends_demand = {
            "peak": max(d["max"] for d in weekends_device_demand),
            "average": max(d["avg"] for d in weekends_device_demand),
            "minimum": max(d["min"] for d in weekends_device_demand),
            "total_energy": f"{total_weekends_energy}kWh",
            "unit": "kVA"
        }

        return operational_demand, non_operational_demand, weekends_demand

    # def optimized_branch_operational_demand(self, start_date, end_date):
    #     """Calculate operational, non-operational and weekend power demand metrics for branch devices.

    #     Args:
    #         start_date: Start date for calculations
    #         end_date: End date for calculations

    #     Returns:
    #         tuple: (operational_demand, non_operational_demand, weekends_demand) dictionaries
    #         containing aggregated metrics for each time period
    #     """
    #     devices = Device.objects.filter(branch=self)

    #     # Initialize collectors
    #     demand_periods = {
    #         'operational': [],
    #         'non_operational': [],
    #         'weekends': []
    #     }

    #     # Aggregate functions mapping
    #     agg_functions = {
    #         'operational': 'get_agg_kwh_for_period_within_op_time',
    #         'non_operational': 'get_agg_kwh_for_period_outside_op_time',
    #         'weekends': 'get_agg_kwh_for_period_on_weekends'
    #     }

    #     for device in devices:
    #         for period, agg_func in agg_functions.items():
    #             # Get aggregated data for this period
    #             agg_data = getattr(device, agg_func)(start_date, end_date)

    #             # Calculate metrics
    #             min_val = round(agg_data.get("min", 0)/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
    #             max_val = round(agg_data.get("max", 0)/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
    #             avg_val = round(agg_data.get("avg", 0)/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)

    #             # Calculate PAPR (Peak to Average Power Ratio)
    #             papr = round(avg_val/max_val, settings.DECIMAL_PLACES) if max_val else 0

    #             demand_periods[period].append({
    #                 "device_name": device.name,
    #                 "min": min_val,
    #                 "max": max_val,
    #                 "avg": avg_val,
    #                 "papr": papr,
    #                 "total_energy": agg_data.get("kWh", 0)
    #             })

    #     # Generate final demand dictionaries
    #     demands = {}
    #     for period, devices_data in demand_periods.items():
    #         if not devices_data:
    #             # Return default values if no data exists for the period
    #             demands[period] = {
    #                 "peak": 0,
    #                 "average": 0,
    #                 "minimum": 0,
    #                 "total_energy": "0kWh",
    #                 "unit": "kVA"
    #             }
    #             continue

    #         demands[period] = {
    #             "peak": max(d["max"] for d in devices_data),
    #             "average": max(d["avg"] for d in devices_data),
    #             "minimum": max(d["min"] for d in devices_data),
    #             "total_energy": f"{max(d['total_energy'] for d in devices_data)}kWh",
    #             "unit": "kVA"
    #         }

    #     return demands['operational'], demands['non_operational'], demands['weekends']


    def optimized_branch_operational_demand(self, start_date, end_date):
        """
        Calculates peak/avg/min demand for operational, non-operational
        and weekend periods by parallelizing per-device aggregates.
        Ensures DB connections are closed in each thread.
        """
        from django.db import close_old_connections, connections
        devices = list(self.device_set.all().only("id", "name"))
        agg_funcs = {
            'operational':       'get_agg_kwh_for_period_within_op_time',
            'non_operational':   'get_agg_kwh_for_period_outside_op_time',
            'weekends':          'get_agg_kwh_for_period_on_weekends',
        }
        collectors = {k: [] for k in agg_funcs}

        def work(dev):
            close_old_connections()
            try:
                out = {}
                for period, fn_name in agg_funcs.items():
                    agg = getattr(dev, fn_name)(start_date, end_date) or {}
                    mn  = round(agg.get("min", 0)/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
                    mx  = round(agg.get("max", 0)/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
                    av  = round(agg.get("avg", 0)/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
                    papr = round((av/mx), settings.DECIMAL_PLACES) if mx else 0
                    out[period] = {
                        "min": mn, "max": mx, "avg": av,
                        "papr": papr,
                        "total_energy": agg.get("kWh", 0),
                    }
                return out
            finally:
                connections.close_all()

        with ThreadPoolExecutor(max_workers=8) as ex:
            futures = {ex.submit(work, d): d for d in devices}
            for fut in as_completed(futures):
                result = fut.result()
                for period in collectors:
                    collectors[period].append(result[period])

        def summarize(lst):
            if not lst:
                return {"peak":0,"average":0,"minimum":0,"total_energy":"0kWh","unit":"kVA"}
            return {
                "peak":    max(d["max"] for d in lst),
                "average": max(d["avg"] for d in lst),
                "minimum": max(d["min"] for d in lst),
                "total_energy": f"{max(d['total_energy'] for d in lst)}kWh",
                "unit": "kVA"
            }

        return (
            summarize(collectors['operational']),
            summarize(collectors['non_operational']),
            summarize(collectors['weekends']),
        )
    # def get_energy_generation_data(self, start_date, end_date):
    #     """Calculate energy generation metrics for all source devices in the branch.

    #     Args:
    #         start_date: Start date for calculations
    #         end_date: End date for calculations

    #     Returns:
    #         list: Array of dictionaries containing energy generation data for each source device
    #     """
    #     # Get all devices for this branch that are sources
    #     devices = self.device_set.filter(is_source=True)
    #     energy_generated_devices = []

    #     month_name = start_date.strftime("%B")

    #     # Get hours of use from branch
    #     hours_of_use = self.get_hours_of_use(start_date, end_date)

    #     for device in devices:
    #         # Calculate energy generated by this device
    #         energy = device.get_total_kwh_for_period(start_date, end_date)

    #         # Get time of use from branch hours of use data
    #         time_of_use = hours_of_use["hours"][hours_of_use["devices"].index(device.name)] if device.name in hours_of_use["devices"] else 0

    #         # Get billing data
    #         # billing_data = device.get_billing_data(start_date, end_date)
    #         # expected_bill = billing_data["totals"]["present_total"]["value_naira"]

    #         # Calculate bill accuracy by comparing with previous month
    #         # previous_bill = billing_data["totals"]["previous_total"]["value_naira"]
    #         # bill_accuracy = 0
    #         # if previous_bill > 0:
    #         #     bill_accuracy = round((expected_bill / previous_bill) * 100, 2)

    #         # new expected bill implementation
    #         if device.type.choice_name.lower() == "utility":
    #             band, tariff = self.get_branch_band_from_utility_time_of_use(time_of_use)
    #             expected_bill = energy * tariff
    #         else:
    #             expected_bill = 0.00

    #         previous_bill = 0.00
    #         bill_accuracy = 0.00
    #         if previous_bill > 0.00:
    #             bill_accuracy = round((expected_bill / previous_bill) * 100, 2)

    #         device_data = {
    #             "name": device.name,
    #             "value": f"{round(energy, 2)} kWh",
    #             "month": month_name,
    #             "type": device.type.choice_name,
    #             "time_of_use": str(round(time_of_use, 2)),
    #             "expected_bill": str(round(expected_bill, 2)),
    #             "last_bill_accuracy": str(bill_accuracy)
    #         }
    #         energy_generated_devices.append(device_data)

    #     return energy_generated_devices




    def get_energy_generation_data(self, start_date, end_date):
        """
        Faithful optimized: Compute energy generation data for all source devices,
        including generator/utility cost, optimal usage, and usage accuracy.
        """
        from django.db.models import Sum, Avg

        devices = list(self.device_set.filter(is_source=True).select_related('type'))
        month_name = start_date.strftime("%B")
        year = start_date.year

        # --- Utility cost and value ---
        util_costs = Cost.objects.filter(
            branch=self,
            cost_type__in=["post-paid", "pre-paid"],
            date__range=(start_date, end_date)
        ).aggregate(total_amount=Sum("amount"), total_value=Sum("value"))
        branch_utility_cost = util_costs["total_amount"] or 0.0
        branch_actual_energy = util_costs["total_value"] or 0.0

        # --- Generator actual usage and cost ---
        fuel_entries = FuelConsumption.objects.filter(
            branch=self,
            start_date__gte=start_date,
            end_date__lte=end_date
        )
        actual_usage = fuel_entries.aggregate(total=Sum('quantity'))['total'] or 0.0

        cost_qs = Cost.objects.filter(
            branch=self,
            cost_type="diesel",
            date__range=(start_date, end_date)
        )
        avg_price = cost_qs.aggregate(avg=Avg('price_per_litre'))['avg']
        if avg_price is None:
            avg_price = 1100  # fallback
        actual_cost = actual_usage * avg_price

        # --- Generator/utility runtime data ---
        hours_data = self.get_hours_of_use(start_date, end_date)

        generators = []
        utilities = []
        total_optimal_usage = 0.0
        device_energy_map = {}

        for device in devices:
            device_type = device.type.choice_name.lower()
            device_energy = device.get_total_kwh_for_period(start_date, end_date)
            optimal_usage = device_energy * DIESEL_LTR_PER_KWH if device_type != "utility" else None
            if optimal_usage is not None:
                total_optimal_usage += optimal_usage
            device_energy_map[device.id] = (device_energy, optimal_usage)

        for device in devices:
            device_type = device.type.choice_name.lower()
            device_energy, optimal_usage = device_energy_map[device.id]
            try:
                time_of_use = device.get_hourly_time_of_use(start_date, end_date)
            except Exception:
                time_of_use = 0

            expected_bill = 0.0
            if device_type == "utility":
                band, tariff = self.get_branch_band_from_utility_time_of_use(time_of_use)
                expected_bill = device_energy * tariff

            entry = {
                "name": device.name,
                "device_energy": f"{round(device_energy, 2)}",
                "month": month_name,
                "year": str(year),
                "time_of_use": str(round(time_of_use, 2)),
                "expected_bill": str(round(expected_bill, 2)),
                "last_bill_accuracy": "0.0",  # Placeholder
            }

            if device_type == "utility":
                entry["actual_cost"] = str(round(branch_utility_cost, 2))
                entry["actual_energy"] = f"{round(branch_actual_energy, 2)}"
                # Calculate usage accuracy for utility (expected vs actual)
                if device_energy == 0:
                    accuracy = 100.0 if branch_actual_energy == 0 else 0.0
                else:
                    error_percentage = (abs(branch_actual_energy - device_energy) / device_energy) * 100
                    accuracy = 100 - error_percentage
                entry["usage_accuracy"] = f"{max(0.0, min(100.0, round(accuracy, 2)))}"
                utilities.append(entry)
            else:
                entry["optimal_usage"] = f"{round(optimal_usage, 2)}"
                entry["optimal_cost"] = str(round(optimal_usage * avg_price, 2)) if optimal_usage is not None else "0.0"
                generators.append(entry)

        # Shared generator usage_accuracy (total actual vs total optimal usage)
        if total_optimal_usage == 0:
            gen_usage_accuracy = 100.0 if actual_usage == 0 else 0.0
        else:
            error = abs(actual_usage - total_optimal_usage)
            gen_usage_accuracy = 100 - (error / total_optimal_usage * 100)

        return {
            "generator": {
                "actual_usage": f"{round(actual_usage, 2)}",
                "actual_cost": str(round(actual_cost, 2)),
                "usage_accuracy": f"{max(0.0, min(100.0, round(gen_usage_accuracy, 2)))}",
                "entries": generators
            },
            "utility": utilities
        }



    # def calculate_generator_efficiency_metrics(self, end_date):
    #     """
    #     Calculate generator efficiency metrics for current month, previous month, and best month.

    #     Args:
    #         end_date: datetime object representing the end of the period

    #     Returns:
    #         dict: Generator efficiency metrics for different time periods
    #     """
    #     try:
    #         current_month_date = end_date
    #         previous_month_date = end_date - datetime.timedelta(days=32)  # Approximate previous month

    #         # Initialize efficiency values
    #         current_month_eff = 0
    #         previous_month_eff = 0
    #         best_month_eff = 0
    #         best_month_date = current_month_date

    #         # Get all generator devices for this branch
    #         generators = self.device_set.filter(type__choice_name__iexact="generator")

    #         for device in generators:
    #             try:
    #                 # Current month efficiency - handle potential None values
    #                 device_efficiency = device.get_gen_efficiency(current_month_date) or {'usage': 0}
    #                 current_eff = float(device_efficiency.get('usage', 0))
    #                 current_month_eff = max(current_month_eff, current_eff)

    #                 # Previous month efficiency
    #                 prev_device_efficiency = device.get_gen_efficiency(previous_month_date) or {'usage': 0}
    #                 prev_eff = float(prev_device_efficiency.get('usage', 0))
    #                 previous_month_eff = max(previous_month_eff, prev_eff)

    #                 # Calculate best month efficiency over the past year
    #                 for i in range(12):
    #                     check_date = current_month_date - datetime.timedelta(days=30*i)
    #                     month_device_efficiency = device.get_gen_efficiency(check_date) or {'usage': 0}
    #                     month_eff = float(month_device_efficiency.get('usage', 0))
    #                     if month_eff > best_month_eff:
    #                         best_month_eff = month_eff
    #                         best_month_date = check_date
    #             except Exception as e:
    #                 print(f"Error processing generator {device.id} efficiency: {str(e)}")
    #                 continue

    #         return {
    #             "current_month": {
    #                 "date": current_month_date.strftime("%m/%Y"),
    #                 "value": str(round(current_month_eff, 1)),
    #                 "unit": "%"
    #             },
    #             "previous_month": {
    #                 "date": previous_month_date.strftime("%m/%Y"),
    #                 "value": str(round(previous_month_eff, 1)),
    #                 "unit": "%"
    #             },
    #             "best_month": {
    #                 "date": best_month_date.strftime("%m/%Y"),
    #                 "value": str(round(best_month_eff, 1)),
    #                 "unit": "%"
    #             }
    #         }
    #     except Exception as e:
    #         print(f"Error in calculate_generator_efficiency_metrics for branch {self.name}: {str(e)}")
    #         return {
    #             "current_month": {"date": "", "value": "0", "unit": "%"},
    #             "previous_month": {"date": "", "value": "0", "unit": "%"},
    #             "best_month": {"date": "", "value": "0", "unit": "%"}
    #         }


    def calculate_generator_efficiency_metrics(self, end_date):
        """
        Optimized but faithful: Calculate generator efficiency metrics for current, previous, and best month,
        using per-generator .get_gen_efficiency() as in the original.
        """
        try:
            current_month_date = end_date
            previous_month_date = end_date - datetime.timedelta(days=32)  # Approximate previous month

            # Initialize efficiency values
            current_month_eff = 0
            previous_month_eff = 0
            best_month_eff = 0
            best_month_date = current_month_date

            # Get all generator devices for this branch
            generators = self.device_set.filter(type__choice_name__iexact="generator")

            for device in generators:
                try:
                    # Current month efficiency
                    device_efficiency = device.get_gen_efficiency(current_month_date) or {'usage': 0}
                    current_eff = float(device_efficiency.get('usage', 0))
                    current_month_eff = max(current_month_eff, current_eff)

                    # Previous month efficiency
                    prev_device_efficiency = device.get_gen_efficiency(previous_month_date) or {'usage': 0}
                    prev_eff = float(prev_device_efficiency.get('usage', 0))
                    previous_month_eff = max(previous_month_eff, prev_eff)

                    # Best month efficiency over the past year
                    for i in range(12):
                        check_date = current_month_date - datetime.timedelta(days=30*i)
                        month_device_efficiency = device.get_gen_efficiency(check_date) or {'usage': 0}
                        month_eff = float(month_device_efficiency.get('usage', 0))
                        if month_eff > best_month_eff:
                            best_month_eff = month_eff
                            best_month_date = check_date
                except Exception as e:
                    print(f"Error processing generator {device.id} efficiency: {str(e)}")
                    continue

            return {
                "current_month": {
                    "date": current_month_date.strftime("%m/%Y"),
                    "value": str(round(current_month_eff, 1)),
                    "unit": "%"
                },
                "previous_month": {
                    "date": previous_month_date.strftime("%m/%Y"),
                    "value": str(round(previous_month_eff, 1)),
                    "unit": "%"
                },
                "best_month": {
                    "date": best_month_date.strftime("%m/%Y"),
                    "value": str(round(best_month_eff, 1)),
                    "unit": "%"
                }
            }
        except Exception as e:
            print(f"Error in calculate_generator_efficiency_metrics for branch {self.name}: {str(e)}")
            return {
                "current_month": {"date": "", "value": "0", "unit": "%"},
                "previous_month": {"date": "", "value": "0", "unit": "%"},
                "best_month": {"date": "", "value": "0", "unit": "%"}
            }

    # def calculate_fuel_efficiency_comparison(self, start_date, end_date):
    #     """
    #     Calculate fuel efficiency metrics across all generators in the branch

    #     Args:
    #         start_date: datetime object for period start
    #         end_date: datetime object for period end

    #     Returns:
    #         dict: Contains accuracy, recommended and achieved fuel efficiency metrics
    #     """
    #     try:
    #         # Get fuel efficiency data for all generators in the branch
    #         generators = self.device_set.filter(type__choice_name__iexact="generator")
    #         fuel_efficiencies = []

    #         for generator in generators:
    #             try:
    #                 fuel_data = generator.get_fuel_consumption_score_cards(start_date, end_date) or {"is_gen": False}
    #                 if fuel_data.get("is_gen", False):
    #                     efficiency = fuel_data.get("fuel_efficiency", {})
    #                     if isinstance(efficiency, dict) and "baseline" in efficiency and "current_score" in efficiency:
    #                         fuel_efficiencies.append({
    #                             "baseline": efficiency.get("baseline", 0),
    #                             "current": efficiency.get("current_score", 0)
    #                         })
    #             except Exception as e:
    #                 print(f"Error processing generator {generator.id} fuel efficiency: {str(e)}")
    #                 continue

    #         # Calculate average efficiencies if there are generators
    #         if fuel_efficiencies:
    #             avg_baseline = sum(f.get("baseline", 0) for f in fuel_efficiencies) / len(fuel_efficiencies)
    #             avg_current = sum(f.get("current", 0) for f in fuel_efficiencies) / len(fuel_efficiencies)

    #             # Calculate accuracy as percentage of achieved vs baseline (avoid division by zero)
    #             accuracy = (avg_current / avg_baseline * 100) if avg_baseline > 0 else 0
    #         else:
    #             avg_baseline = 0
    #             avg_current = 0
    #             accuracy = 0

    #         return {
    #             "accuracy": {
    #                 "value": str(round(accuracy, 1)),
    #                 "unit": "%"
    #             },
    #             "recommended": {
    #                 "value": str(round(avg_baseline, 1)),
    #                 "unit": "kWh/liter"
    #             },
    #             "achieved": {
    #                 "value": str(round(avg_current, 1)),
    #                 "unit": "kWh/liter"
    #             }
    #         }
    #     except Exception as e:
    #         print(f"Error in calculate_fuel_efficiency_comparison for branch {self.name}: {str(e)}")
    #         return {
    #             "accuracy": {"value": "0", "unit": "%"},
    #             "recommended": {"value": "0", "unit": "kWh/liter"},
    #             "achieved": {"value": "0", "unit": "kWh/liter"}
    #         }


    def calculate_fuel_efficiency_comparison(self, start_date, end_date):
        """
        Faithful optimized: Calculate fuel efficiency metrics across all generators in the branch,
        using per-generator .get_fuel_consumption_score_cards() as in the original.
        """
        try:
            generators = self.device_set.filter(type__choice_name__iexact="generator")
            fuel_efficiencies = []

            for generator in generators:
                try:
                    fuel_data = generator.get_fuel_consumption_score_cards(start_date, end_date) or {"is_gen": False}
                    if fuel_data.get("is_gen", False):
                        efficiency = fuel_data.get("fuel_efficiency", {})
                        if isinstance(efficiency, dict) and "baseline" in efficiency and "current_score" in efficiency:
                            fuel_efficiencies.append({
                                "baseline": efficiency.get("baseline", 0),
                                "current": efficiency.get("current_score", 0)
                            })
                except Exception as e:
                    print(f"Error processing generator {generator.id} fuel efficiency: {str(e)}")
                    continue

            # Calculate average efficiencies if there are generators
            if fuel_efficiencies:
                avg_baseline = sum(f.get("baseline", 0) for f in fuel_efficiencies) / len(fuel_efficiencies)
                avg_current = sum(f.get("current", 0) for f in fuel_efficiencies) / len(fuel_efficiencies)

                # Calculate accuracy as percentage of achieved vs baseline (avoid division by zero)
                accuracy = (avg_current / avg_baseline * 100) if avg_baseline > 0 else 0
            else:
                avg_baseline = 0
                avg_current = 0
                accuracy = 0

            return {
                "accuracy": {
                    "value": str(round(accuracy, 1)),
                    "unit": "%"
                },
                "recommended": {
                    "value": str(round(avg_baseline, 1)),
                    "unit": "kWh/liter"
                },
                "achieved": {
                    "value": str(round(avg_current, 1)),
                    "unit": "kWh/liter"
                }
            }
        except Exception as e:
            print(f"Error in calculate_fuel_efficiency_comparison for branch {self.name}: {str(e)}")
            return {
                "accuracy": {"value": "0", "unit": "%"},
                "recommended": {"value": "0", "unit": "kWh/liter"},
                "achieved": {"value": "0", "unit": "kWh/liter"}
            }

    def calculate_utility_band_hours(self, start_date, end_date):
        """
        Calculate utility band hours for the given period.

        Args:
            start_date: datetime object for period start
            end_date: datetime object for period end

        Returns:
            list: Array of dictionaries containing band categorization data
        """

        # Calculate total hours in the time period for the Utility Device at the Branch
        utility_device = self.device_set.filter(type__choice_name__iexact="utility").first()
        if utility_device:
            total_hours = utility_device.get_agg_kwh_for_period(start_date, end_date).get("duration_hours", 0)
        else:
            total_hours = 0

        # Define the bands and their minimum hours per day
        bands = [
            {"band": "A", "min_hours_per_day": 20},
            {"band": "B", "min_hours_per_day": 16},
            {"band": "C", "min_hours_per_day": 12},
            {"band": "D", "min_hours_per_day": 8},
            {"band": "E", "min_hours_per_day": 4},
        ]

        # Calculate days in the period
        days = (end_date - start_date).days

        # Generate the band categorization data
        categorization = []
        for band_info in bands:
            expected_hours = days * band_info["min_hours_per_day"]
            categorization.append({
                "band": band_info["band"],
                "total_hours": str(round(total_hours)),
                "expected_hours": str(round(expected_hours))
            })

        return categorization

    def calculate_data_entry_score(self, start_date, end_date):
        """
        Calculate data entry compliance score for diesel records.
        Score is based on:
        - Whether entries exist for each week in the period
        - Completeness of the entries

        Returns:
            dict: Contains score value and unit
        """
        # Get all fuel consumption entries for the period
        entries = FuelConsumption.objects.filter(
            branch=self,
            start_date__gte=start_date,
            end_date__lte=end_date
        )

        # Get list of weeks in the period
        dates = []
        current = start_date
        while current <= end_date:
            dates.append(current.strftime('%Y-%m-%d'))
            current += datetime.timedelta(days=1)

        # Check if entries exist for each week
        has_missing_entries = self.check_branch_weekly_fuel_entry(dates)

        if not entries.exists():
            # No entries at all
            score = 0
        elif has_missing_entries:
            # Some weeks are missing entries
            total_days = (end_date - start_date).days
            days_with_entries = entries.count()
            score = (days_with_entries / total_days) * 100
        else:
            # All weeks have entries
            score = 100

        return {
            "value": str(round(score)),
            "unit": "%"
        }

    def branch_utility_band_and_tariff(self, start_date, end_date):

        # Get the utility device at this branch
        device = self.device_set.filter(type__choice_name__iexact="utility").first()

        # Get hours of use from branch
        hours_of_use = self.get_hours_of_use(start_date, end_date)
        total_hours = hours_of_use.get("period_total_hours")

        # Get time of use from branch hours of use data
        time_of_use = hours_of_use["hours"][hours_of_use["devices"].index(device.name)] if device.name in hours_of_use["devices"] else 0
        band, tariff = self.get_branch_band_from_utility_time_of_use(time_of_use)

        return dict(band=band, tariff=tariff, utility_hours_of_use=time_of_use, branch_hours_of_use=total_hours)

    # def calculate_energy_deviation(self, start_date, end_date):
    #     """Calculate energy deviation metrics comparing current period with previous period"""
    #     # Get previous period dates
    #     days_in_period = (end_date - start_date).days
    #     previous_end = start_date
    #     previous_start = previous_end - datetime.timedelta(days=days_in_period)

    #     # Initialize aggregated values
    #     current_total = 0
    #     current_operational = 0
    #     current_non_operational = 0
    #     current_weekend = 0
    #     previous_total = 0
    #     previous_operational = 0
    #     previous_non_operational = 0
    #     previous_weekend = 0

    #     # Initialize time of use values
    #     total_time_of_use = 0
    #     operational_time_of_use = 0
    #     non_operational_time_of_use = 0
    #     weekend_time_of_use = 0
    #     generator_time_of_use = 0

    #     # Get all devices for this branch
    #     devices = Device.objects.filter(branch=self)

    #     # Aggregate values from all devices
    #     for device in devices:
    #         # Current period
    #         current_period = device.get_agg_kwh_for_period(start_date, end_date)
    #         current_total += current_period.get("kWh", 0)
    #         total_time_of_use += current_period.get("duration_hours", 0)

    #         # Add to generator time of use if device is a generator
    #         if device.type.choice_name.lower() == "generator":
    #             generator_time_of_use += current_period.get("duration_hours", 0)

    #         current_op = device.get_agg_kwh_for_period_within_op_time(start_date, end_date)
    #         current_operational += current_op.get("kWh", 0)
    #         operational_time_of_use += current_op.get("duration_hours", 0)

    #         current_non_op = device.get_agg_kwh_for_period_outside_op_time(start_date, end_date)
    #         current_non_operational += current_non_op.get("kWh", 0)
    #         non_operational_time_of_use += current_non_op.get("duration_hours", 0)

    #         current_wknd = device.get_agg_kwh_for_period_on_weekends(start_date, end_date)
    #         current_weekend += current_wknd.get("kWh", 0)
    #         weekend_time_of_use += current_wknd.get("duration_hours", 0)

    #         # Previous period calculations...
    #         previous_period = device.get_agg_kwh_for_period(previous_start, previous_end)
    #         previous_total += previous_period.get("kWh", 0)

    #         previous_op = device.get_agg_kwh_for_period_within_op_time(previous_start, previous_end)
    #         previous_operational += previous_op.get("kWh", 0)

    #         previous_non_op = device.get_agg_kwh_for_period_outside_op_time(previous_start, previous_end)
    #         previous_non_operational += previous_non_op.get("kWh", 0)

    #         previous_wknd = device.get_agg_kwh_for_period_on_weekends(previous_start, previous_end)
    #         previous_weekend += previous_wknd.get("kWh", 0)

    #     # Get diesel consumption for both periods
    #     current_diesel = self.get_diesel_entry_sum(start_date, end_date)
    #     previous_diesel = self.get_diesel_entry_sum(previous_start, previous_end)

    #     return [
    #         {
    #             "name": "Energy(KWH)",
    #             "value": str(round(current_total, 2)),
    #             "deviation": str(round(current_total - previous_total, 2)),
    #             "time_of_use": str(round(total_time_of_use, 2)),
    #             "unit": "kWh",
    #             "deviationUnit": "kWh"
    #         },
    #         {
    #             "name": "Operational Period",
    #             "value": str(round(current_operational, 2)),
    #             "deviation": str(round(current_operational - previous_operational, 2)),
    #             "time_of_use": str(round(operational_time_of_use, 2)),
    #             "unit": "kWh",
    #             "deviationUnit": "kWh"
    #         },
    #         {
    #             "name": "Non-Operational Period",
    #             "value": str(round(current_non_operational, 2)),
    #             "deviation": str(round(current_non_operational - previous_non_operational, 2)),
    #             "time_of_use": str(round(non_operational_time_of_use, 2)),
    #             "unit": "kWh",
    #             "deviationUnit": "kWh"
    #         },
    #         {
    #             "name": "Weekend Period",
    #             "value": str(round(current_weekend, 2)),
    #             "deviation": str(round(current_weekend - previous_weekend, 2)),
    #             "time_of_use": str(round(weekend_time_of_use, 2)),
    #             "unit": "kWh",
    #             "deviationUnit": "kWh"
    #         },
    #         {
    #             "name": "Liters(diesel-usage)",
    #             "value": str(round(current_diesel, 2)),
    #             "deviation": str(round(current_diesel - previous_diesel, 2)),
    #             "time_of_use": str(round(generator_time_of_use, 2)),
    #             "unit": "liters",
    #             "deviationUnit": "liters"
    #         }
    #     ]



    def calculate_energy_deviation(self, start_date, end_date):
        """
        Faithful optimized: Compares this period vs the previous period for kWh & diesel usage;
        parallelizes per-device aggregation, using device helpers for kWh and duration_hours.
        Ensures DB connections are closed in each thread.
        """
        from django.db import close_old_connections, connections

        days = (end_date - start_date).days
        prev_end   = start_date
        prev_start = prev_end - datetime.timedelta(days=days)

        devices = list(self.device_set.all().only("id", "type__choice_name"))

        accum = {
            "cur_tot":0, "cur_op":0, "cur_non":0, "cur_wk":0,
            "prev_tot":0, "prev_op":0, "prev_non":0, "prev_wk":0,
            "use_tot":0, "use_op":0, "use_non":0, "use_wk":0, "gen_use":0
        }

        def work(dev):
            close_old_connections()
            try:
                t1 = dev.get_agg_kwh_for_period(start_date, end_date) or {}
                t2 = dev.get_agg_kwh_for_period_within_op_time(start_date, end_date) or {}
                t3 = dev.get_agg_kwh_for_period_outside_op_time(start_date, end_date) or {}
                t4 = dev.get_agg_kwh_for_period_on_weekends(start_date, end_date) or {}
                p1 = dev.get_agg_kwh_for_period(prev_start, prev_end) or {}
                p2 = dev.get_agg_kwh_for_period_within_op_time(prev_start, prev_end) or {}
                p3 = dev.get_agg_kwh_for_period_outside_op_time(prev_start, prev_end) or {}
                p4 = dev.get_agg_kwh_for_period_on_weekends(prev_start, prev_end) or {}
                return dev.type.choice_name.lower(), t1, t2, t3, t4, p1, p2, p3, p4
            finally:
                connections.close_all()

        with ThreadPoolExecutor(max_workers=8) as ex:
            futures = {ex.submit(work, d): d for d in devices}
            for fut in as_completed(futures):
                typ, c1,c2,c3,c4, p1,p2,p3,p4 = fut.result()
                accum["cur_tot"] += c1.get("kWh",0); accum["use_tot"] += c1.get("duration_hours",0)
                if typ=="generator":
                    accum["gen_use"] += c1.get("duration_hours",0)
                accum["cur_op"]  += c2.get("kWh",0); accum["use_op"]  += c2.get("duration_hours",0)
                accum["cur_non"] += c3.get("kWh",0); accum["use_non"] += c3.get("duration_hours",0)
                accum["cur_wk"]  += c4.get("kWh",0); accum["use_wk"]  += c4.get("duration_hours",0)
                accum["prev_tot"] += p1.get("kWh",0)
                accum["prev_op"]  += p2.get("kWh",0)
                accum["prev_non"] += p3.get("kWh",0)
                accum["prev_wk"]  += p4.get("kWh",0)

        # diesel sums
        cur_diesel = self.get_diesel_entry_sum(start_date, end_date)
        prev_diesel= self.get_diesel_entry_sum(prev_start, prev_end)

        return [
            {"name":"Energy(KWH)",             "value":str(round(accum["cur_tot"],2)), "deviation":str(round(accum["cur_tot"]-accum["prev_tot"],2)), "time_of_use":str(round(accum["use_tot"],2)), "unit":"kWh", "deviationUnit":"kWh"},
            {"name":"Operational Period",      "value":str(round(accum["cur_op"],2)),  "deviation":str(round(accum["cur_op"] - accum["prev_op"],2)),  "time_of_use":str(round(accum["use_op"],2)),  "unit":"kWh", "deviationUnit":"kWh"},
            {"name":"Non-Operational Period",  "value":str(round(accum["cur_non"],2)), "deviation":str(round(accum["cur_non"]-accum["prev_non"],2)),"time_of_use":str(round(accum["use_non"],2)),"unit":"kWh","deviationUnit":"kWh"},
            {"name":"Weekend Period",          "value":str(round(accum["cur_wk"],2)),  "deviation":str(round(accum["cur_wk"]-accum["prev_wk"],2)),  "time_of_use":str(round(accum["use_wk"],2)),  "unit":"kWh","deviationUnit":"kWh"},
            {"name":"Liters(diesel-usage)",    "value":str(round(cur_diesel,2)),       "deviation":str(round(cur_diesel-prev_diesel,2)),         "time_of_use":str(round(accum["gen_use"],2)), "unit":"liters","deviationUnit":"liters"},
        ]


    # def calculate_energy_deviation_and_cost(self, start_date, end_date):
    #     """Calculate energy deviation and cost metrics comparing operational period with non operation period(including weekends)"""
    #     try:
    #         # Get all generator devices for this branch
    #         generators = self.device_set.filter(type__choice_name__iexact="generator")

    #         month_name = start_date.strftime("%B")

    #         total_non_op_kwh = 0.0
    #         total_non_op_hours = 0.0
    #         total_diesel_consumption = 0.0

    #         for generator in generators:
    #             try:
    #                 # Get non-operational energy usage (including both non-op hours and weekends)
    #                 non_op_data = generator.get_agg_kwh_for_period_outside_op_time(start_date, end_date) or {}
    #                 weekend_data = generator.get_agg_kwh_for_period_on_weekends(start_date, end_date) or {}

    #                 # Sum up kWh and hours with safe handling of None values
    #                 total_non_op_kwh += non_op_data.get("kWh", 0.0) + weekend_data.get("kWh", 0.0)
    #                 total_non_op_hours += non_op_data.get("duration_hours", 0.0) + weekend_data.get("duration_hours", 0.0)

    #                 # Calculate diesel consumption for this period
    #                 diesel_consumed = 0.0
    #                 total_diesel_consumption += diesel_consumed
    #             except Exception as e:
    #                 print(f"Error processing generator {generator.id} in energy deviation calculation: {str(e)}")
    #                 continue

    #         price_per_litre = 900.0

    #         total_cost = total_diesel_consumption * price_per_litre

    #         return {
    #             "name": "Wasted Energy(KWH)",
    #             "month": month_name,
    #             "value": round(total_non_op_kwh, 2),
    #             "deviation_time_of_use": round(total_non_op_hours, 2),
    #             "diesel_consumption": round(total_diesel_consumption, 2),
    #             "deviation_cost": round(total_cost, 2)
    #         }
    #     except Exception as e:
    #         print(f"Error in calculate_energy_deviation_and_cost for branch {self.name}: {str(e)}")
    #         return {
    #             "name": "Wasted Energy(KWH)",
    #             "month": month_name,
    #             "value": 0,
    #             "deviation_time_of_use": 0,
    #             "diesel_consumption": 0,
    #             "deviation_cost": 0,
    #             "error": str(e)
    #         }


    def calculate_energy_deviation_and_cost(self, start_date, end_date):
        """
        Faithful optimized: Summarizes wasted kWh (non-op + weekends) and computes diesel cost
        in parallel across generators, using per-device helpers for accuracy.
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed

        generators = list(
            self.device_set
                .filter(type__choice_name__iexact="generator")
                .only("id")
        )

        def work(dev):
            nonop = dev.get_agg_kwh_for_period_outside_op_time(start_date, end_date) or {}
            wknd  = dev.get_agg_kwh_for_period_on_weekends(start_date, end_date) or {}
            return (
                nonop.get("kWh", 0) + wknd.get("kWh", 0),
                nonop.get("duration_hours", 0) + wknd.get("duration_hours", 0)
            )

        total_non_op = 0.0
        total_hours = 0.0
        with ThreadPoolExecutor(max_workers=8) as ex:
            futures = [ex.submit(work, g) for g in generators]
            for fut in as_completed(futures):
                kwh, hrs = fut.result()
                total_non_op += kwh
                total_hours  += hrs

        # diesel cost & consumption from Cost table
        price_per_litre = 1100.0  # or pull from your Cost entries
        total_diesel = self.get_diesel_entry_sum(start_date, end_date)
        deviation_cost = round(total_diesel * price_per_litre, 2)

        return {
            "name": "Wasted Energy(KWH)",
            "month": start_date.strftime("%B"),
            "year": str(start_date.year),
            "value": round(total_non_op, 2),
            "deviation_time_of_use": round(total_hours, 2),
            "diesel_consumption": round(total_diesel, 2),
            "deviation_cost": deviation_cost
        }

        
    def get_monthly_report(self, start_date=None, end_date=None):
        """Get monthly report data with fallback values for failing sections."""
        try:
            # Use provided dates or fallback to previous month
            if not start_date or not end_date:
                start_date, end_date, month_name, year = time_helpers.get_previous_month_start_and_end_dates_from_today()
            else:
                month_name = start_date.strftime("%B")
                year = start_date.year

            print(f"Branch {self.id} report period: {start_date} to {end_date}, {month_name} {year}")

            # Generate the report - this already has internal error handling for each section
            data = self._generate_monthly_report(start_date, end_date, month_name, year)

            # Make sure we return the actual report, even if some sections failed
            return data

        except Exception as e:
            # Log the error with traceback
            print(f"Error in get_monthly_report for branch {self.id}: {e}")

            # return empty template with month/year from provided or fallback
            # with all fields initialized to default values
            return self._generate_empty_report_template(month_name, year)

    def _generate_empty_report_template(self, month_name, year):
        """Generate an empty report template with all fields initialized to default values"""
        return {
            "branch_name": self.name,
            "month": month_name,
            "year": year,
            "total_energy": {"value": 0, "unit": "kWh"},
            "solar_hour": {"value": 0, "unit": "kWh"},
            "power_demand": {
                "operational": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
                "non_operational": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
                "weekends": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"}
            },
            "energy_generated": {"devices": []},
            "energy_deviation": [],
            "energy_deviation_and_cost": {
                "name": "Wasted Energy(KWH)",
                "value": 0,
                "deviation_time_of_use": 0,
                "diesel_consumption": 0,
                "deviation_cost": 0
            },
            "generator_size_efficiency": {
                "current_month": {"date": month_name, "value": "0", "unit": "%"},
                "previous_month": {"date": "", "value": "0", "unit": "%"},
                "best_month": {"date": "", "value": "0", "unit": "%"}
            },
            "fuel_efficiency_accuracy_comparison": {
                "accuracy": {"value": "0", "unit": "%"},
                "recommended": {"value": "0", "unit": "kWh/liter"},
                "achieved": {"value": "0", "unit": "kWh/liter"}
            },
            "utility_band_categorization": [],
            "data_entry": {"value": "0", "unit": "%"},
            "utility_band_details": {"band":"A", "tariff":"23.00", "utility_hours_of_use":"23.00", "branch_hours_of_use":"23.00"}
        }

    # def _generate_monthly_report(self, start_date, end_date, month_name, year):
    #     """Internal method to generate the actual report data"""

    #     data = {
    #         "branch_name": self.name,
    #         "month": month_name,
    #         "year": year,
    #     }

    #     try:
    #         # Calculate core metrics
    #         total_energy, total_solar_hours = self.branch_total_energy_and_solar_hours(start_date, end_date)
    #         data["total_energy"] = {"value": total_energy, "unit": "kWh"}
    #         data["solar_hour"] = {"value": total_solar_hours, "unit": "kWh"}
    #     except Exception as e:
    #         print(f"Error calculating energy for branch {self.name}: {str(e)}")
    #         data["total_energy"] = {"value": 0, "unit": "kWh", "error": str(e)}
    #         data["solar_hour"] = {"value": 0, "unit": "kWh", "error": str(e)}

    #     try:
    #         operational_demand, non_operational_demand, weekends_demand = self.optimized_branch_operational_demand(start_date, end_date)
    #         data["power_demand"] = {
    #             "operational": operational_demand,
    #             "non_operational": non_operational_demand,
    #             "weekends": weekends_demand
    #         }
    #     except Exception as e:
    #         print(f"Error calculating demands for branch {self.name}: {str(e)}")
    #         data["power_demand"] = {
    #             "operational": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA", "error": str(e)},
    #             "non_operational": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
    #             "weekends": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"}
    #         }

    #     try:
    #         energy_generated = self.get_energy_generation_data(start_date, end_date)
    #         data["energy_generated"] = {"devices": energy_generated}
    #     except Exception as e:
    #         print(f"Error calculating energy generation for branch {self.name}: {str(e)}")
    #         data["energy_generated"] = {"devices": [], "error": str(e)}

    #     try:
    #         energy_deviation = self.calculate_energy_deviation(start_date, end_date)
    #         data["energy_deviation"] = energy_deviation
    #     except Exception as e:
    #         print(f"Error calculating energy deviation for branch {self.name}: {str(e)}")
    #         data["energy_deviation"] = []

    #     try:
    #         energy_deviation_cost = self.calculate_energy_deviation_and_cost(start_date, end_date)
    #         data["energy_deviation_and_cost"] = energy_deviation_cost
    #     except Exception as e:
    #         print(f"Error calculating energy deviation cost for branch {self.name}: {str(e)}")
    #         data["energy_deviation_and_cost"] = {}

    #     try:
    #         generator_efficiency = self.calculate_generator_efficiency_metrics(end_date)
    #         data["generator_size_efficiency"] = generator_efficiency
    #     except Exception as e:
    #         print(f"Error calculating generator efficiency for branch {self.name}: {str(e)}")
    #         data["generator_size_efficiency"] = {
    #             "current_month": {"date": "", "value": "0", "unit": "%"},
    #             "previous_month": {"date": "", "value": "0", "unit": "%"},
    #             "best_month": {"date": "", "value": "0", "unit": "%"}
    #         }

    #     try:
    #         fuel_efficiency = self.calculate_fuel_efficiency_comparison(start_date, end_date)
    #         data["fuel_efficiency_accuracy_comparison"] = fuel_efficiency
    #     except Exception as e:
    #         print(f"Error calculating fuel efficiency for branch {self.name}: {str(e)}")
    #         data["fuel_efficiency_accuracy_comparison"] = {
    #             "accuracy": {"value": "0", "unit": "%"},
    #             "recommended": {"value": "0", "unit": "kWh/liter"},
    #             "achieved": {"value": "0", "unit": "kWh/liter"}
    #         }

    #     try:
    #         utility_band = self.calculate_utility_band_hours(start_date, end_date)
    #         data["utility_band_categorization"] = utility_band
    #     except Exception as e:
    ##        print(f"Error calculating utility band for branch {self.name}: {str(e)}")
    #         data["utility_band_categorization"] = []

    #     try:
    #         utility_band_details = self.branch_utility_band_and_tariff(start_date, end_date)
    #         data["utility_band_details"] = utility_band_details
    #     except Exception as e:
    #         print(f"Error calculating utility band details for branch {self.name}: {str(e)}")
    #         data["utility_band_details"] = {"band":"A", "tariff":"300.00", "utility_hours_of_use":"300.00", "branch_hours_of_use":"300.00"}

    #     try:
    #         data_entry = self.calculate_data_entry_score(start_date, end_date)
    #         data["data_entry"] = data_entry
    #     except Exception as e:
    #         print(f"Error calculating data entry score for branch {self.name}: {str(e)}")
    #         data["data_entry"] = {"value": "0", "unit": "%"}

    #     return data

    # def _generate_monthly_report(self, start_date, end_date, month_name, year):
    #     """Internal method to generate the actual report data, now parallelized."""
    #     data = {
    #         "branch_name": self.name,
    #         "month": month_name,
    #         "year": year,
    #     }

    #     # Define the work functions and their assignment keys
    #     tasks = {
    #         "energy":        (self.branch_total_energy_and_solar_hours,      (start_date, end_date)),
    #         "power_demand":  (self.optimized_branch_operational_demand,      (start_date, end_date)),
    #         "generation":    (self.get_energy_generation_data,              (start_date, end_date)),
    #         "deviation":     (self.calculate_energy_deviation,              (start_date, end_date)),
    #         "dev_cost":      (self.calculate_energy_deviation_and_cost,     (start_date, end_date)),
    #         "gen_eff":       (self.calculate_generator_efficiency_metrics,  (end_date,)),
    #         "fuel_eff":      (self.calculate_fuel_efficiency_comparison,    (start_date, end_date)),
    #         "util_band":     (self.calculate_utility_band_hours,            (start_date, end_date)),
    #         "util_band_det": (self.branch_utility_band_and_tariff,          (start_date, end_date)),
    #         "data_entry":    (self.calculate_data_entry_score,             (start_date, end_date)),
    #     }

    #     # Launch all tasks in parallel
    #     with ThreadPoolExecutor(max_workers=len(tasks)) as executor:
    #         future_to_key = {
    #             executor.submit(fn, *args): key
    #             for key, (fn, args) in tasks.items()
    #         }

    #         for future in as_completed(future_to_key):
    #             key = future_to_key[future]
    #             try:
    #                 result = future.result()
    #             except Exception as e:
    #                 # On error, record the exception and fill defaults below
    #                 result = e

    #             # Map back to `data` with appropriate structure
    #             if key == "energy":
    #                 if isinstance(result, tuple):
    #                     total_energy, total_solar = result
    #                     data["total_energy"] = {"value": total_energy, "unit": "kWh"}
    #                     data["solar_hour"]   = {"value": total_solar,   "unit": "kWh"}
    #                 else:
    #                     data["total_energy"] = {"value": 0, "unit": "kWh", "error": str(result)}
    #                     data["solar_hour"]   = {"value": 0, "unit": "kWh", "error": str(result)}

    #             elif key == "power_demand":
    #                 if isinstance(result, tuple):
    #                     op, nonop, wknd = result
    #                     data["power_demand"] = {
    #                         "operational":     op,
    #                         "non_operational": nonop,
    #                         "weekends":        wknd,
    #                     }
    #                 else:
    #                     data["power_demand"] = {
    #                         "operational":     {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA", "error": str(result)},
    #                         "non_operational": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
    #                         "weekends":        {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
    #                     }

    #             elif key == "generation":
    #                 if isinstance(result, Exception):
    #                     data["energy_generated"] = {
    #                         "current_month": {"devices": [], "error": str(result)}
    #                     }
    #                 else:
    #                     # define our four month‐windows
    #                     windows = [
    #                         ("current_month",     end_date),
    #                         ("previous_month_1",  end_date - relativedelta(months=1)),
    #                         ("previous_month_2",  end_date - relativedelta(months=2)),
    #                     ]
    #                     # spin off one small executor for just these 4 calls
    #                     eg = {}
    #                     with ThreadPoolExecutor(max_workers=4) as gen_exec:
    #                         fut_to_label = {}
    #                         for label, m_end in windows:
    #                             # start on the 1st of that month, preserving time‑of‑day
    #                             m_start = m_end.replace(
    #                                 day=1,
    #                                 hour=start_date.hour,
    #                                 minute=start_date.minute,
    #                                 second=start_date.second,
    #                                 microsecond=start_date.microsecond,
    #                             )
    #                             fut = gen_exec.submit(self.get_energy_generation_data, m_start, m_end)
    #                             fut_to_label[fut] = label

    #                         for gen_fut in as_completed(fut_to_label):
    #                             lbl = fut_to_label[gen_fut]
    #                             try:
    #                                 devices = gen_fut.result()
    #                             except Exception as e:
    #                                 eg[lbl] = {"devices": [], "error": str(e)}
    #                             else:
    #                                 eg[lbl] = {"devices": devices}

    #                     data["energy_generated"] = eg

    #             elif key == "deviation":
    #                 data["energy_deviation"] = result if not isinstance(result, Exception) else []

    #             elif key == "dev_cost":
    #                 if isinstance(result, Exception):
    #                     data["energy_deviation_and_cost"] = {
    #                         "current_month": {"error": str(result)}
    #                     }
    #                 else:
    #                     # same 4 windows
    #                     windows = [
    #                         ("current_month",     end_date),
    #                         ("previous_month_1",  end_date - relativedelta(months=1)),
    #                         ("previous_month_2",  end_date - relativedelta(months=2)),
    #                     ]
    #                     edc = {}
    #                     with ThreadPoolExecutor(max_workers=4) as cost_exec:
    #                         fut_to_label = {}
    #                         for label, m_end in windows:
    #                             m_start = m_end.replace(
    #                                 day=1,
    #                                 hour=start_date.hour,
    #                                 minute=start_date.minute,
    #                                 second=start_date.second,
    #                                 microsecond=start_date.microsecond,
    #                             )
    #                             fut = cost_exec.submit(self.calculate_energy_deviation_and_cost, m_start, m_end)
    #                             fut_to_label[fut] = label

    #                         for cost_fut in as_completed(fut_to_label):
    #                             lbl = fut_to_label[cost_fut]
    #                             try:
    #                                 edc[lbl] = cost_fut.result()
    #                             except Exception as e:
    #                                 edc[lbl] = {"error": str(e)}

    #                     data["energy_deviation_and_cost"] = edc

    #             elif key == "gen_eff":
    #                 data["generator_size_efficiency"] = result if not isinstance(result, Exception) else {
    #                     "current_month": {"date": month_name, "value": "0", "unit": "%"},
    #                     "previous_month": {"date": "", "value": "0", "unit": "%"},
    #                     "best_month": {"date": "", "value": "0", "unit": "%"},
    #                 }

    #             elif key == "fuel_eff":
    #                 data["fuel_efficiency_accuracy_comparison"] = result if not isinstance(result, Exception) else {
    #                     "accuracy": {"value": "0", "unit": "%"},
    #                     "recommended": {"value": "0", "unit": "kWh/liter"},
    #                     "achieved": {"value": "0", "unit": "kWh/liter"},
    #                 }

    #             elif key == "util_band":
    #                 data["utility_band_categorization"] = result if not isinstance(result, Exception) else []

    #             elif key == "util_band_det":
    #                 data["utility_band_details"] = result if not isinstance(result, Exception) else {
    #                     "band":"A", "tariff":"300.00",
    #                     "utility_hours_of_use":"300.00",
    #                     "branch_hours_of_use":"300.00"
    #                 }

    #             elif key == "data_entry":
    #                 data["data_entry"] = result if not isinstance(result, Exception) else {"value": "0", "unit": "%"}

    #     return data

    def _generate_monthly_report(self, start_date, end_date, month_name, year):
        """Internal method to generate the actual report data, sequentially to avoid nested parallelism."""
        data = {
            "branch_name": self.name,
            "month": month_name,
            "year": year,
        }

        tasks = [
            ("energy",        self.branch_total_energy_and_solar_hours,      (start_date, end_date)),
            ("power_demand",  self.optimized_branch_operational_demand,      (start_date, end_date)),
            ("generation",    self.get_energy_generation_data,               (start_date, end_date)),
            ("deviation",     self.calculate_energy_deviation,               (start_date, end_date)),
            ("dev_cost",      self.calculate_energy_deviation_and_cost,      (start_date, end_date)),
            ("gen_eff",       self.calculate_generator_efficiency_metrics,   (end_date,)),
            ("fuel_eff",      self.calculate_fuel_efficiency_comparison,     (start_date, end_date)),
            ("util_band",     self.calculate_utility_band_hours,             (start_date, end_date)),
            ("util_band_det", self.branch_utility_band_and_tariff,           (start_date, end_date)),
            ("data_entry",    self.calculate_data_entry_score,               (start_date, end_date)),
        ]

        for key, fn, args in tasks:
            try:
                result = fn(*args)
            except Exception as e:
                result = e

            # Map result to data dict as in your current code
            if key == "energy":
                if isinstance(result, tuple):
                    total_energy, total_solar = result
                    data["total_energy"] = {"value": total_energy, "unit": "kWh"}
                    data["solar_hour"]   = {"value": total_solar,   "unit": "kWh"}
                else:
                    data["total_energy"] = {"value": 0, "unit": "kWh", "error": str(result)}
                    data["solar_hour"]   = {"value": 0, "unit": "kWh", "error": str(result)}

            elif key == "power_demand":
                if isinstance(result, tuple):
                    op, nonop, wknd = result
                    data["power_demand"] = {
                        "operational":     op,
                        "non_operational": nonop,
                        "weekends":        wknd,
                    }
                else:
                    data["power_demand"] = {
                        "operational":     {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA", "error": str(result)},
                        "non_operational": {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
                        "weekends":        {"peak": 0, "average": 0, "minimum": 0, "total_energy": "0kWh", "unit": "kVA"},
                    }

            elif key == "generation":
                if isinstance(result, Exception):
                    data["energy_generated"] = {
                        "current_month": {"devices": [], "error": str(result)}
                    }
                else:
                    # define our four month‐windows
                    windows = [
                        ("current_month",     end_date),
                        ("previous_month_1",  end_date - relativedelta(months=1)),
                        ("previous_month_2",  end_date - relativedelta(months=2)),
                    ]
                    # spin off one small executor for just these 4 calls
                    eg = {}
                    with ThreadPoolExecutor(max_workers=4) as gen_exec:
                        fut_to_label = {}
                        for label, m_end in windows:
                            # start on the 1st of that month, preserving time‑of‑day
                            m_start = m_end.replace(
                                day=1,
                                hour=start_date.hour,
                                minute=start_date.minute,
                                second=start_date.second,
                                microsecond=start_date.microsecond,
                            )
                            fut = gen_exec.submit(self.get_energy_generation_data, m_start, m_end)
                            fut_to_label[fut] = label

                        for gen_fut in as_completed(fut_to_label):
                            lbl = fut_to_label[gen_fut]
                            try:
                                devices = gen_fut.result()
                            except Exception as e:
                                eg[lbl] = {"devices": [], "error": str(e)}
                            else:
                                eg[lbl] = {"devices": devices}

                    data["energy_generated"] = eg

            elif key == "deviation":
                data["energy_deviation"] = result if not isinstance(result, Exception) else []

            elif key == "dev_cost":
                if isinstance(result, Exception):
                    data["energy_deviation_and_cost"] = {
                        "current_month": {"error": str(result)}
                    }
                else:
                    # same 4 windows
                    windows = [
                        ("current_month",     end_date),
                        ("previous_month_1",  end_date - relativedelta(months=1)),
                        ("previous_month_2",  end_date - relativedelta(months=2)),
                    ]
                    edc = {}
                    with ThreadPoolExecutor(max_workers=4) as cost_exec:
                        fut_to_label = {}
                        for label, m_end in windows:
                            m_start = m_end.replace(
                                day=1,
                                hour=start_date.hour,
                                minute=start_date.minute,
                                second=start_date.second,
                                microsecond=start_date.microsecond,
                            )
                            fut = cost_exec.submit(self.calculate_energy_deviation_and_cost, m_start, m_end)
                            fut_to_label[fut] = label

                        for cost_fut in as_completed(fut_to_label):
                            lbl = fut_to_label[cost_fut]
                            try:
                                edc[lbl] = cost_fut.result()
                            except Exception as e:
                                edc[lbl] = {"error": str(e)}

                    data["energy_deviation_and_cost"] = edc

            elif key == "gen_eff":
                data["generator_size_efficiency"] = result if not isinstance(result, Exception) else {
                    "current_month": {"date": month_name, "value": "0", "unit": "%"},
                    "previous_month": {"date": "", "value": "0", "unit": "%"},
                    "best_month": {"date": "", "value": "0", "unit": "%"},
                }

            elif key == "fuel_eff":
                data["fuel_efficiency_accuracy_comparison"] = result if not isinstance(result, Exception) else {
                    "accuracy": {"value": "0", "unit": "%"},
                    "recommended": {"value": "0", "unit": "kWh/liter"},
                    "achieved": {"value": "0", "unit": "kWh/liter"},
                }

            elif key == "util_band":
                data["utility_band_categorization"] = result if not isinstance(result, Exception) else []

            elif key == "util_band_det":
                data["utility_band_details"] = result if not isinstance(result, Exception) else {
                    "band":"A", "tariff":"300.00",
                    "utility_hours_of_use":"300.00",
                    "branch_hours_of_use":"300.00"
                }

            elif key == "data_entry":
                data["data_entry"] = result if not isinstance(result, Exception) else {"value": "0", "unit": "%"}

        return data    
        
    class Meta :
        verbose_name_plural  = "Branches"

# store each unique branch/month/year report here
default_month = None
class MonthlyReport(models.Model):
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        related_name="cached_reports"
    )
    year = models.PositiveSmallIntegerField()  # e.g. 2025
    month = models.PositiveSmallIntegerField()  # 1–12

    # save the full JSON report payload
    data = models.JSONField()   ############## JSONB under the hood in Postgres

    # record when this report was first generated
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = (("branch", "year", "month"),)
        ordering = ["-year", "-month"]

    def __str__(self):
        return f"{self.branch.name} - {self.month}/{self.year}"


class BranchOTDHistory(models.Model):
    start_date    = models.CharField(max_length=200)
    end_date  = models.CharField(max_length=200)
    data = models.JSONField(blank=True, null=True)
    branch  = models.IntegerField(blank=True, null=True)

class DieselOverviewHistory(models.Model):
    start_date    = models.CharField(max_length=200)
    end_date  = models.CharField(max_length=200)
    data = models.JSONField(blank=True, null=True)
    branch  = models.IntegerField(blank=True, null=True)

class Subscription(models.Model):

    """
        THE **RESOLVED** MESSAGE IS USED BY SAVE() TO DETERMINE IF THIS SUBSCRIPTION
        IS JUST BEING CREATED OR AN ALREADY CREATED THIS ALLOWS THE OVERIDE TO
        UPDATE THE EXPIRY DATE AND EXTRA DETAILS TO THE SUBSCRIPTION.
    """

    client     = models.ForeignKey(Client, on_delete=models.CASCADE, default = 1)
    branch     = models.ForeignKey(Branch, on_delete=models.CASCADE, default = 1)
    start_date = models.DateTimeField(auto_now_add=True)
    duration   = models.IntegerField(default=0, null=True, blank=True)
    amount_paid= models.FloatField(default=0, null=True, blank=True )
    resolved   = models.BooleanField(default=False)

    def __str__(self):
        return f"Branch {self.client} - {self.branch.name}"

    @property
    def expiry_date(self):

        self.start_date = self.start_date or datetime.datetime.now()
        expiry_date = self.start_date + datetime.timedelta(days = self.duration or 0)
        has_expired = datetime.datetime.now() >= expiry_date

        return {
            "date": expiry_date,
            "has_expired": has_expired
        }

    def resolve(self):

        """
            Check for last subscription of target branch,
            and then try to set next subcription start as
            the continuation from the end of that last
            subscription, if it's the first subscription of
            target branch then just start the new sub from today.
        """
        last_subscription = Subscription.objects.filter(branch = self.branch).exclude(id = self.id)
        # print(last_subscription.last().expiry_date.get("date"))
        if last_subscription.exists():

            last_subscription_expiry = last_subscription.last().expiry_date.get("date")

        else:

            last_subscription_expiry = datetime.datetime.now()

        new_sub_duration = self.amount_paid / SUBSCRIPTION_PRICE_PER_DAY
        self.duration = new_sub_duration
        self.start_date = last_subscription_expiry

        self.save()

        return True

    def save(self, *args, **kwargs):
        # self.start_date = datetime.datetime.now()
        super(Subscription, self).save(*args, **kwargs)

        if self.resolved:
            pass
        else:
            self.resolved = True
            self.resolve()
            self.save



class DeviceType(models.Model):
    choice_name = models.CharField(max_length=200)

    def __str__(self):
        return self.choice_name

class Bill(models.Model):
    value  = models.CharField(max_length=50, null=True)
    amount = models.CharField(max_length=50, null=True)
    tariff = models.CharField(max_length=50, null=True)
    client     = models.ForeignKey(Client, on_delete=models.CASCADE, default = 1)
    branch     = models.ForeignKey(Branch, on_delete=models.CASCADE, default = 1)
    quantity   = models.CharField(max_length=50, null=True)
    end_date   = models.DateTimeField()
    fuel_type  = models.CharField(max_length=100, null=True)
    start_date = models.DateTimeField()
    price_per_liter = models.CharField(max_length=50, null=True)

    def __str__(self):
        return f"start -{self.start_date} end -{self.end_date} customer-({self.branch}) org- ({self.client})"

class FuelConsumption(models.Model):
    FUEL_CONSUMPTION_TYPES = [
        ('Daily',   'Daily'),
        ('Monthly', 'Monthly'),
    ]

    branch           = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True)
    generator_ids    = models.CharField(
                          max_length=500, blank=True, default='',
                          help_text="Comma-separated list of device (type = generator)IDs"
                       )
    start_date       = models.DateField()
    end_date         = models.DateField()
    quantity         = models.FloatField()
    fuel_type        = models.CharField(max_length=100, null=True)
    consumption_type = models.CharField(max_length=10, choices=FUEL_CONSUMPTION_TYPES, null=True)

    def __str__(self):
        return f"start -{self.quantity} start -{self.start_date} end -{self.end_date} customer-({self.branch.name})"

    def monthly_consumption(self, branch_id, month, year):
        # Get the first and last days of the month
        branch = Branch.objects.get(id=branch_id)
        start_date = date(year, month, 1)
        end_date = date(year, month, 1)
        last_day_of_month = 28
        while True:
            try:
                end_date = date(year, month, last_day_of_month)
            except ValueError:
                last_day_of_month -= 1
                continue
            break

        # Get all the entries for the given branch and month
        entries = FuelConsumption.objects.filter(branch=branch, start_date__gte=start_date, end_date__lte=end_date)

        # Calculate the sum of quantity for all entries
        total_quantity = entries.aggregate(sum=Sum('quantity'))['sum'] or 0.0

        return total_quantity

    @classmethod
    def get_quantity_for_day(cls, branch_id, start_date):
        try:
            fuel_consumption = cls.objects.get(branch=branch_id, start_date=start_date)
            return fuel_consumption.quantity
        except cls.DoesNotExist:
            return 0.0

    @classmethod
    def get_id_for_entry(cls, branch_id, start_date):
        try:
            fuel_consumption = cls.objects.get(branch=branch_id, start_date=start_date)
            return fuel_consumption.id
        except cls.DoesNotExist:
            return 0

class Device(models.Model):

    DEVICE_CHOICES = (
        ("ACCRELL", "ACCRELL"),
        ("SATEC", "SATEC"),
        ("ACREL-ACB", "ACREL-ACB")
    )
    DEVICE_SWITCH_CHOICES = (
        ("ON", "ON"),
        ("OFF", "OFF")
    )
    SOURCE_CHOICES = (
        ("AGGREGATORY", "AGGREGATORY"),
        ("NON-AGGREGATORY", "NON-AGGREGATORY")
    )
    created_at = models.DateTimeField(default=timezone.now, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    name           = models.CharField(max_length=200)
    type           = models.ForeignKey(DeviceType, on_delete=models.CASCADE, related_name='devices')
    client         = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='devices')
    branch         = models.ForeignKey(Branch, on_delete=models.CASCADE)
    is_load        = models.BooleanField(default = False)
    provider       = models.CharField(max_length=50, choices=DEVICE_CHOICES, default="ACCRELL")
    gen_size       =  models.FloatField(default = 0)
    is_active      =  models.BooleanField(default = True)
    device_id      = models.CharField(max_length=50)
    fuel_type      =  models.CharField(max_length=50, null=True, blank = True)
    is_source      = models.BooleanField(default = False)
    last_reading   =  models.TextField(blank = True, default=json.dumps({}))
    switch_state       = models.CharField(max_length=50, choices=DEVICE_SWITCH_CHOICES, default="OFF")
    bill_recievers = models.ManyToManyField(Bill_Mails, blank=True)
    next_maintenance_date = models.DateField(null=True, blank=True)
    operating_hours_start = models.TimeField(null=True, blank = True, default = datetime.time(0, 0), help_text="Time should be in 24 hour format e.g '13:30'")
    operating_hours_end = models.TimeField(null=True, blank = True, default = datetime.time(23, 59), help_text="Time should be in 24 hour format e.g '13:30'")
    last_posted = models.DateTimeField(null = True, blank = True)
    non_post_attention = models.BooleanField(default = False)
    source_tag = models.CharField(max_length=50, choices=SOURCE_CHOICES, default="AGGREGATORY")
    is_ghost = models.BooleanField(default = False)

    def __str__(self):
        return f"{self.type.choice_name} : {self.client.name} {self.branch.name} ({self.name})"

    class Meta:
       indexes = [
           models.Index(fields=['device_id',]),
        ]


    @property
    def hours_since_last_post(self):
        if self.last_posted:
            time_difference = timezone.now() - self.last_posted
            return time_difference.total_seconds() // 3600  # Convert seconds to hours
        return 999

    @property
    def is_gen(self):
        return True if self.type.choice_name.casefold() == "generator" else False

    @property
    def is_utility(self):
        return True if self.type.choice_name.casefold() == "utility" else False

    @property
    def is_ipp(self):
        return True if self.type.choice_name.casefold() == "ipp" else False

    def set_is_active(self, is_active):
        self.is_active = is_active
        self.save()

    # # @memoize(timeout=20)
    # @functools.lru_cache(maxsize=None)
    # def get_total_kwh_for_period(self, start_date, end_date):

    #     results = self.filter_datalogs(start_date, end_date)


    #     if results.exists():


    #         end_value = results.last()
    #         start_value = results.first()
    #         usage = end_value.summary_energy_register_1 - start_value.summary_energy_register_1

    #         return round(usage, 1)

    #     else:

    #         return 0

    @functools.lru_cache(maxsize=None)
    def get_total_kwh_for_period(self, start_date, end_date):
        agg = (
            Datalog.objects
            .filter(device=self,
                    post_datetime__gte=start_date,
                    post_datetime__lte=end_date)
            .aggregate(
                min_val=Min('summary_energy_register_1'),
                max_val=Max('summary_energy_register_1')
            )
        )
        return round((agg['max_val'] or 0) - (agg['min_val'] or 0), 1)

    # # @memoize(timeout=20)
    @functools.lru_cache(maxsize=None)
    def new_baseline(self, end_date, type = False):

        previous_year_cdd = Degree_Day.objects.filter(date__year = end_date.year-1, date__month = end_date.month)
        start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(end_date)

        usage_in_target_month = self.get_periodic_energy(start_date, end_date, "monthly")
        usage_in_target_month = 0 if not usage_in_target_month else usage_in_target_month.get("diff")[0]

        if previous_year_cdd.exists():
            previous_year_cdd = previous_year_cdd[0].value
        else:
            previous_year_cdd = 0

        degree_days = Degree_Day.objects.all().order_by('-value')
        cdd_values = []
        energy_values = []

        for degree_day in degree_days:

            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(degree_day.date)

            energy_values.append(self.get_total_energy_at_end_of_period(start_date, end_date).get("usage"))
            cdd_values.append(degree_day.value)



        prediction = baseline_helpers.predict_usage(tuple(energy_values), tuple(cdd_values), previous_year_cdd)

        return prediction

    # @memoize(timeout=20)
    def threaded_baseline(self, end_date, type = False):

        previous_year_cdd = Degree_Day.objects.filter(date__year = end_date.year-1, date__month = end_date.month)
        start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(end_date)
        usage_in_target_month = self.get_periodic_energy(start_date, end_date, "monthly")
        usage_in_target_month = 0 if not usage_in_target_month else usage_in_target_month.get("diff")[0]

        if previous_year_cdd.exists():
            previous_year_cdd = previous_year_cdd[0].value
        else:
            previous_year_cdd = 0

        degree_days = Degree_Day.objects.all().order_by('-value')
        cdd_values = []
        energy_values = []

        def get_energy_and_cdd(cdd):

            start_date, end_date = time_helpers.get_start_and_end_of_month_from_date(cdd.date)

            energy_values.append(self.get_total_energy_at_end_of_period(start_date, end_date).get("usage"))
            cdd_values.append(cdd.value)



        tasks = [threading.Thread(target = get_energy_and_cdd, args = (degree_day,)) for degree_day in degree_days]

        for task in tasks:
            task.start()
        for task in tasks:
            task.join()

        try:
            prediction = baseline_helpers.predict_usage(energy_values, cdd_values, previous_year_cdd)
        except ValueError:
            prediction = 0

        data = {
                        "is_generator": self.is_gen,
                        "baseline_energy": {
                            "unit": "kWh",
                            "forecast": abs(round(prediction, 2)),
                            "used": abs(round(usage_in_target_month, 2))
                        }
                }

        return data

    # @memoize(timeout=20)
    def  filter_readings(self, start_date, end_date):
        results = self.reading_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date)
        return results

    # @memoize(timeout=20)
    def  filter_readings_by_datetime(self, start_date, end_date):
        from datetime import datetime

        if type(start_date) == str and type(end_date) == str:

            # Parse the string to datetime objects
            start_date = datetime.strptime(start_date, "%d-%m-%Y %H:%M")
            end_date = datetime.strptime(end_date, "%d-%m-%Y %H:%M")

            start_time = self.operating_hours_start
            end_time = self.operating_hours_end

            start_datetime = datetime.combine(start_date.date(), start_time)
            end_datetime = datetime.combine(end_date.date(), end_time)

        else:
            # Assuming start_date and end_date are already datetime objects
            start_time = self.operating_hours_start
            end_time = self.operating_hours_end

            start_datetime = datetime.combine(start_date.date(), start_time)
            end_datetime = datetime.combine(end_date.date(), end_time)

        # Filter readings based on the datetime range
        results = self.reading_set.filter(
            post_datetime__range=(start_datetime, end_datetime)
        )

        # Filter readings within operating hours for each day
        filtered_results = []
        for reading in results:
            reading_datetime = reading.post_datetime
            operating_start = datetime.combine(reading_datetime.date(), start_time)
            operating_end = datetime.combine(reading_datetime.date(), end_time)

            if operating_start <= reading_datetime <= operating_end:
                filtered_results.append(reading)

        return filtered_results

    # @memoize(timeout=20)
    def filter_datalogs(self, start_date, end_date):
        results = self.datalog_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date)
        return results

    memoize(timeout= 20)
    def get_fuel_consumption(self, start_date, end_date):

        if not self.is_gen:return 0

        results = self.filter_readings(start_date, end_date)

        if results.exists():
            fuel_consumed = 0
            last_post = False

            for result in results:
                if last_post:

                    duration = ((result.post_datetime - last_post.post_datetime).seconds)/60

                    if duration > 20: # if duration is more than 30mins apart then skip because devices should only be expected to post every 5 mins
                        demand_at_instance = last_post.total_kw
                        load_ratio = demand_at_instance/self.gen_size

                        consumption_at_instance = dashboard_helpers.get_diesel_consumption_rate(self.gen_size, load_ratio)

                        fuel_consumed += consumption_at_instance * (duration/60) # CONVERT TO HOURS BECAUSE DIESEL CONSUMPTION CHART SHOWS CONSUMPTION IN LITRES PER HOUR

                    else:
                        pass

                last_post = result


            return fuel_consumed

        else:

            return 0

    memoize(timeout= 20)
    def get_hours_of_use(self, start_date, end_date):

        result = self.branch.get_time_of_use(start_date, end_date).get("sums")

        response = result.get(self.name, 0)

        return response

    memoize(timeout= 20)
    def get_carbon_emmisions_by_fuel_consumed(self, start_date, end_date):

        fuel_consumed = self.get_fuel_consumption(start_date, end_date)
        carbon_emmisions = (CO2_KG_EMMISSIONS_PER_LITRE * fuel_consumed)/1000

        return carbon_emmisions

    memoize(timeout= 20)
    def get_carbon_emmisions_by_kwh_consumed(self, start_date=False, end_date=False, kwh_consumed = False):
        carbon_emmisions = 0  # Initialize with a default value

        if self.type.choice_name.lower() == "ipp":
            kwh_consumed = kwh_consumed or self.get_total_kwh_for_period(start_date, end_date)
            carbon_emmisions = (CO2_KG_EMMISSIONS_PER_KWH_LNG * kwh_consumed)/1000

        if self.type.choice_name.lower() == "generator":
            kwh_consumed = kwh_consumed or self.get_total_kwh_for_period(start_date, end_date)
            carbon_emmisions = (CO2_KG_EMMISSIONS_PER_KWH_DIESEL * kwh_consumed)/1000

        if self.type.choice_name.lower() == "utility":
            kwh_consumed = kwh_consumed or self.get_total_kwh_for_period(start_date, end_date)
            carbon_emmisions = (CO2_KG_EMMISSIONS_PER_KWH_UTILITY * kwh_consumed)/1000


        return {
                "value": carbon_emmisions,
                }

    def get_cost_of_energy(self, start_date, end_date):
        tariff = self.tariff_set.filter(start_date__gte = start_date, start_date__lte = end_date).aggregate(Avg('amount')).get("amount__avg", 0)

        return tariff if tariff == tariff else 0

    # @memoize(timeout=20)
    def get_agg_kwh_for_period(self, start_date, end_date):
        from main.scripts import aggregation_helpers

        results = self.filter_readings(start_date, end_date).order_by("-total_kw").filter(total_kw__gte = 1)

        if results.exists():
            data = results.aggregate(min=Min('total_kw'), max=Max('total_kw'), avg=Avg('total_kw'))

            max_demand_date = results.first()
            data["max_demand_date"] = max_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.')

            min_demand_date = results.last()
            data["min_demand_date"] = min_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.')

            # Calculate duration hours
            readings = results.values()
            data["duration_hours"] = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

            return data
        else:
            return dict.fromkeys(["min", "max", "avg", "max_demand_date", "min_demand_date", "duration_hours"], 0)

    # @memoize(timeout=20)
    def get_agg_kwh_for_period_within_op_time(self, start_date, end_date):
        from main.scripts import aggregation_helpers

        # Get filtered readings within operating hours
        filtered_results = self.filter_readings_by_datetime(start_date, end_date)

        # Get the readings ordered by datetime to properly calculate consumption
        results = Reading.objects.filter(
            id__in=[reading.id for reading in filtered_results]
        ).order_by("post_datetime")  # Changed from -total_kw to post_datetime for proper chronological order

        if results.exists():
            # Get min/max/avg from results ordered by total_kw
            kw_ordered_results = results.order_by("-total_kw").filter(total_kw__gte=1)
            aggregated_data = kw_ordered_results.aggregate(min=Min('total_kw'), max=Max('total_kw'), avg=Avg('total_kw'))

            # Ensure we have non-None values for min, max, and avg
            data = {
                "min": 0 if aggregated_data["min"] is None else aggregated_data["min"],
                "max": 0 if aggregated_data["max"] is None else aggregated_data["max"],
                "avg": 0 if aggregated_data["avg"] is None else aggregated_data["avg"]
            }

            max_demand_date = kw_ordered_results.first()
            data["max_demand_date"] = max_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.') if max_demand_date else None

            min_demand_date = kw_ordered_results.last()
            data["min_demand_date"] = min_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.') if min_demand_date else None

            # Calculate kWh using chronologically ordered readings
            first_reading = results.first()  # Using the chronologically ordered results
            last_reading = results.last()
            if first_reading and last_reading and hasattr(first_reading, 'kwh_import') and hasattr(last_reading, 'kwh_import'):
                kwh = last_reading.kwh_import - first_reading.kwh_import
                data["kWh"] = abs(round(kwh))  # Added abs() to handle any negative values
            else:
                data["kWh"] = 0

            # Calculate duration hours
            readings = results.values()
            data["duration_hours"] = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

            return data
        else:
            return dict.fromkeys(["min", "max", "avg", "max_demand_date", "min_demand_date", "kWh", "duration_hours"], 0)

    # @memoize(timeout=20)
    def get_agg_kwh_for_period_outside_op_time(self, start_date, end_date):
        from main.scripts import aggregation_helpers

        filtered_results = []
        readings = self.reading_set.filter(post_datetime__range=(start_date, end_date))

        for reading in readings:
            reading_time = reading.post_datetime.time()
            if reading_time < self.operating_hours_start or reading_time > self.operating_hours_end:
                filtered_results.append(reading.id)

        results = Reading.objects.filter(id__in=filtered_results).order_by("-total_kw").filter(total_kw__gte=1)

        if results.exists():
            aggregated_data = results.aggregate(min=Min('total_kw'), max=Max('total_kw'), avg=Avg('total_kw'))

            # Ensure we have non-None values for min, max, and avg
            data = {
                "min": 0 if aggregated_data["min"] is None else aggregated_data["min"],
                "max": 0 if aggregated_data["max"] is None else aggregated_data["max"],
                "avg": 0 if aggregated_data["avg"] is None else aggregated_data["avg"]
            }

            max_demand_date = results.first()
            data["max_demand_date"] = max_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.')

            min_demand_date = results.last()
            data["min_demand_date"] = min_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.')

            # Calculate kWh
            first_reading = results.order_by('post_datetime').first()
            last_reading = results.order_by('post_datetime').last()
            kwh = round(last_reading.kwh_import - first_reading.kwh_import) if (first_reading and last_reading) else 0
            data["kWh"] = kwh

            # Calculate duration hours
            readings = results.values()
            data["duration_hours"] = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

            return data

        return dict.fromkeys(["min", "max", "avg", "max_demand_date", "min_demand_date", "kWh", "duration_hours"], 0)

    # @memoize(timeout=20)
    def get_agg_kwh_for_period_on_weekends(self, start_date, end_date):
        from main.scripts import aggregation_helpers

        filtered_results = []
        readings = self.reading_set.filter(post_datetime__range=(start_date, end_date))

        for reading in readings:
            if reading.post_datetime.weekday() >= 5:  # 5 is Saturday, 6 is Sunday
                filtered_results.append(reading.id)

        results = Reading.objects.filter(id__in=filtered_results).order_by("-total_kw").filter(total_kw__gte=1)

        if results.exists():
            aggregated_data = results.aggregate(min=Min('total_kw'), max=Max('total_kw'), avg=Avg('total_kw'))

            # Ensure we have non-None values for min, max, and avg
            data = {
                "min": 0 if aggregated_data["min"] is None else aggregated_data["min"],
                "max": 0 if aggregated_data["max"] is None else aggregated_data["max"],
                "avg": 0 if aggregated_data["avg"] is None else aggregated_data["avg"]
            }

            max_demand_date = results.first()
            data["max_demand_date"] = max_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.')

            min_demand_date = results.last()
            data["min_demand_date"] = min_demand_date.post_datetime.strftime('%A %d, %B %Y, %I:%m%p.')

            # Calculate kWh
            first_reading = results.order_by('post_datetime').first()
            last_reading = results.order_by('post_datetime').last()
            kwh = round(last_reading.kwh_import - first_reading.kwh_import) if (first_reading and last_reading) else 0
            data["kWh"] = kwh

            # Calculate duration hours
            readings = results.values()
            data["duration_hours"] = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

            return data

        return dict.fromkeys(["min", "max", "avg", "max_demand_date", "min_demand_date", "kWh", "duration_hours"], 0)

    memoize(timeout=20)
    def get_today_vs_yesterday(self, start_date, end_date):

        yesterday = datetime.date.today() - datetime.timedelta(days = 1)
        today = datetime.date.today()
        tomorrow = datetime.date.today() + datetime.timedelta(days = 1)

        yesterday_usage = self.get_total_kwh_for_period(yesterday, today)
        today_usage = self.get_total_kwh_for_period(today, tomorrow)

        return {"yesterday_usage":yesterday_usage, "today_usage": today_usage}

    def get_power_quality(self, start_date, end_date, frequency = "hourly"):

        raw_readings = self.filter_readings(start_date, end_date)

        result = dashboard_helpers.resample_power_quality(raw_readings, frequency)

        return result

    def get_power_demand(self, start_date, end_date, frequency = "hourly"):

        raw_readings = self.filter_readings(start_date, end_date)

        result = dashboard_helpers.resample_power_demand(raw_readings, frequency)

        return result

    def get_last_readings(self):

        if self.provider == "SATEC":

            last_reading = dashboard_helpers.get_last_readings(self)

        else:

            last_reading = dashboard_helpers.get_last_readings_accrel(self)

        return last_reading

    def update_last_readings(self):

        last_reading = remote_request.get_last_reading(self.device_id)

        if last_reading:

            self.last_reading = json.dumps(last_reading)
            self.save()

    def get_total_energy_at_end_of_period(self, start_date, end_date):

        values = self.datalog_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date)

        start_value = 0 if not values.first() else values.first().summary_energy_register_1
        end_values = 0 if not values.last() else values.last().summary_energy_register_1
        usage = end_values - start_value

        return {
                    "previous": round(start_value, 2),
                    "current": round(end_values, 2),
                    "usage": round(usage, 2)

                }

    def energy_consumption_data(self, start_date, end_date, frequency):

        usage_metrics = self.get_total_energy_at_end_of_period(start_date, end_date)
        periodic_data = self.get_periodic_energy( start_date, end_date, frequency )

        template = {
                        "previous": usage_metrics["previous"],
                        "current": usage_metrics["current"],
                        "usage": usage_metrics["usage"],
                        "dates": {
                            "dates": periodic_data["dates"] if periodic_data else [],
                            "units": ""
                        },
                        "energy_consumption_values": {
                            "value": periodic_data["diff"] if periodic_data else [],
                            "units": "kWh"
                        }
                    }

        return template

    memoize(timeout= 20)
    def get_periodic_energy(self, start_date, end_date, frequency = "hourly", return_dict = True):

        data = self.datalog_set.filter( post_datetime__gte = start_date, post_datetime__lte = end_date )

        if data.exists():

            data_as_dataframe = pd.DataFrame(data.values("post_datetime", "device__name", "summary_energy_register_1"))
            data = dashboard_helpers.get_kwh_usage_periodically(data_as_dataframe, frequency= frequency).reset_index()
            data = data.fillna(0)

            if return_dict:
                return data.round(2).to_dict("list")
            else:
                return data

        else:

            return False

    memoize(timeout= 20)
    def get_solar_hours_consumption(self, start_date, end_date):

        data = self.get_periodic_energy(start_date, end_date, "hourly", return_dict=False)
        if isinstance(data, bool):
            return 0
        else:
            solar_df_mask = data["dates"].dt.hour.isin(range(8,17))
            solar_df = data[solar_df_mask]
            print(solar_df)

            return solar_df["diff"].sum().round(2)

    def get_tariffs(self):

        tariffs = self.tariff_set.all().order_by("start_date").values("start_date", "amount")
        return tariffs

    def get_cost_of_energy_daily(self, start_date, end_date):

        periodic_energy = self.get_periodic_energy( start_date, end_date, "daily", return_dict=False )
        tarrifs = self.get_tariffs()

        if tarrifs.exists() and isinstance(periodic_energy, pd.DataFrame):

            kwh = periodic_energy[["dates", "diff"]]
            tariff = pd.DataFrame(tarrifs)[["start_date", "amount"]]

            kwh["dates"] = kwh["dates"].astype("datetime64[ns]")

            tariff["start_date"] = tariff["start_date"].astype("datetime64[ns]")

            date_range = pd.DataFrame({"dates":pd.date_range(tariff["start_date"].iloc[0], end_date, freq= "d"), "amounts": np.nan})

            new_tarrifs = pd.merge(date_range, tariff, left_on='dates', right_on='start_date', how='left').fillna(method="ffill")[["dates", "amount"]]

            updated_df_daily = pd.merge(kwh, new_tarrifs, left_on='dates', right_on='dates', how='left').fillna(method="ffill")

            updated_df_daily["amount"] = updated_df_daily["amount"].astype("float")

            updated_df_daily["cost"] = updated_df_daily["diff"]*updated_df_daily["amount"]

            return updated_df_daily.round(2)

        else:
            return pd.DataFrame({
                                "dates": [],
                                "diff": [],
                                "amount": [],
                                "cost": []
                                })

    def calculate_billing_data(self, start_date, end_date):
        total_energy = round(self.get_total_kwh_for_period(start_date, end_date), 2)

        cost_of_energy = self.get_cost_of_energy_daily(start_date, end_date)
        usage_daily = self.get_periodic_energy(start_date, end_date, "daily")

        usage_daily = usage_daily if usage_daily else {
                                                        "dates":[],
                                                        "diff":[]
                                                        }
        return dict(usage_daily = usage_daily,
                    #total_energy = cost_of_energy["diff"].sum(),
                    total_energy = total_energy,
                    cost_of_energy = cost_of_energy
                    )

    # @functools.lru_cache(maxsize=None)
    def get_billing_data(self, start_date, end_date):

        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)# remove time because the billing function targets data from the first our of the first day of the previous month to the first hour of the first day of the next month.
        last_day_prev_month = (start_date - datetime.timedelta(days = start_date.day))
        first_day_prev_month = (last_day_prev_month - datetime.timedelta(days = last_day_prev_month.day - 1))
        last_day_prev_month = (last_day_prev_month + datetime.timedelta(days = 1)) #push the date one step i.e from may, 31 00:00 to june, 01 00:00

        present_bills  = self.calculate_billing_data(start_date, end_date)
        previous_bills = self.calculate_billing_data(first_day_prev_month, last_day_prev_month)

        # print(present_bills)


        data = {
                "totals": {
                        "previous_total": {
                            "usage_kwh": round(previous_bills["total_energy"], 2),
                            "value_naira": round(previous_bills["cost_of_energy"].cost.sum(), 2)
                        },
                        "present_total": {
                            "usage_kwh": round(present_bills["total_energy"], 2),
                            "value_naira": round(present_bills["cost_of_energy"].cost.sum(), 2)
                        }
                },
                "consumption_kwh": {
                        "dates": present_bills["usage_daily"]["dates"],
                        "values": present_bills["usage_daily"]["diff"]
                },
                "consumption_naira": {
                        "dates": present_bills["cost_of_energy"]["dates"],
                        "values": present_bills["cost_of_energy"]["cost"].fillna(0)
                }
            }

        return data

    def get_date_of_min_max_comsumption(self, start_date, end_date ) ->dict:

        """ THIS FUNCTION RETURNS THE BREAKDOWN OF DEVICE BILL ESPECIALLY THE METRICS REQUIRED IN THE PAGE E.G THE DAY OF HIGHEST USAGE THE AVERAGE DAILY USAGE, ETC SAMPLE (HIGHEST USAGE DAY -> MONDAYS (3201kWH))
        """

        data = self.get_billing_data(start_date, end_date)
        dates = data.get("consumption_kwh").get("dates")
        kwh = data.get("consumption_kwh").get("values")
        date_consumptions_df = pd.DataFrame(dict( dates = dates, kwh = kwh))
        date_consumptions_df["dates"] = pd.to_datetime(date_consumptions_df["dates"])
        date_consumptions_df["name"] = date_consumptions_df["dates"].dt.day_name()

        dates_no_zeros = date_consumptions_df[date_consumptions_df['kwh'] != 0] # remove all zero values

        dates_no_zeros["dates_formatted"] = dates_no_zeros["dates"].dt.strftime('%A %d, %B %Y') # ADD CORRECT DATE FORMAT

        sorted_date_consumptions_df = dates_no_zeros.sort_values(by = "kwh", ascending=False) # SORT IN ASCENDING ORDER PF USAGE
        sorted_date_consumptions_df["kwh"] = dates_no_zeros["kwh"].round(2)

        date_grouped = dates_no_zeros.groupby("name").mean().sort_values(by = "kwh", ascending=False).round(2).reset_index().round(2)

        average_daily = round(dates_no_zeros["kwh"].mean(),2)

        if not date_grouped.empty:
            max_avg_usage = date_grouped.iloc[0:1][["name", "kwh"]].to_dict("records")[0]

            min_avg_usage = date_grouped.iloc[-1:][["name", "kwh"]].to_dict("records")[0]
        else:
            max_avg_usage = min_avg_usage = {'name': '-', 'kwh': 0}


        max_usage_day = sorted_date_consumptions_df[["dates_formatted", "kwh"]].iloc[0:1].to_dict("records")
        max_usage_day = max_usage_day[0] if max_usage_day else {'dates_formatted': '-', 'kwh': 0}

        min_usage_day = sorted_date_consumptions_df[["dates_formatted", "kwh"]].iloc[-1:].to_dict("records")
        min_usage_day = min_usage_day[0] if min_usage_day else {'dates_formatted': '-', 'kwh': 0}

        hours_of_use   = round(self.get_hours_of_use(start_date, end_date), 2)

        return dict(
                        max_avg_usage = max_avg_usage,
                        min_avg_usage = min_avg_usage,
                        max_usage_day = max_usage_day,
                        min_usage_day = min_usage_day,
                        average_daily = average_daily,
                        hours_of_use  = hours_of_use
                    )

    def get_billing_data_for_mailing(self, start_date, end_date):

        # THIS FUNCTION FURTHER GETS METRICS AS REQUIRED BY THE REPORTS HTML FILE WHICH IS THE THE BASIS FOR THE EMAIL

        main_bill = self.get_billing_data(start_date, end_date)
        metrics = self.get_date_of_min_max_comsumption(start_date, end_date)
        demand  = self.get_agg_kwh_for_period(start_date, end_date)

        target_month = start_date.strftime("%B")

        data = dict(
                    name = self.name.upper(),
                    branch = self.branch.name.upper(),
                    source_type = self.type.choice_name,
                    client_name = self.client.name,
                    previous_energy_consumed = main_bill["totals"]["previous_total"]["usage_kwh"],
                    previous_cost_of_energy = main_bill["totals"]["previous_total"]["value_naira"],
                    energy_consumed = main_bill["totals"]["present_total"]["usage_kwh"],
                    cost_of_energy = main_bill["totals"]["present_total"]["value_naira"],
                    tarrif = main_bill["totals"]["previous_total"]["value_naira"]/main_bill["totals"]["previous_total"]["usage_kwh"],
                    carbon_emmisions = round(self.get_carbon_emmisions_by_kwh_consumed(start_date, end_date).get("value")),
                    metrics = metrics,
                    demand  = demand
        )

        return data

    def has_not_posted_in_last_hour(self):

        last_datalog_post = self.datalog_set.last()
        now = datetime.datetime.now()
        print(self.name)


        last_reading_post = self.reading_set.last()
        now = datetime.datetime.now()

        if last_reading_post:
            last_datalog_post = self.datalog_set.last().post_datetime
            last_reading_post = self.reading_set.last().post_datetime

        else:
            return ""

        duration_since_last_datalog_post = (now - last_datalog_post).seconds
        duration_since_last_reading_post = (now - last_reading_post).seconds


        tolerance_in_minutes = datetime.timedelta(seconds = int(os.environ.get("device_non_post_max_period"))).seconds

        if duration_since_last_datalog_post > tolerance_in_minutes or duration_since_last_reading_post > tolerance_in_minutes: # if greater than 1hr in minute resolution

            # return f"{self.client.name} ({self.branch.name}, {self.name}) post delay : \n\n\t\tLast Datalog - {last_datalog_post.strftime('%b %d %Y %I:%M%p')} \n\t\tLast Reading - {last_reading_post.strftime('%b %d %Y %I:%M%p')}"
            return (self.client.name, self.branch.name, self.name, last_datalog_post.strftime("%d %b, %-I:%M%p"))

        else:
            return ""

    def check_non_posting_members(self):


        non_posting = [device.has_not_posted_in_last_hour() for device in Device.objects.all() if device.is_active]
        non_posting = [x for x in non_posting if x != ""]
        # print(non_posting)

        if any(non_posting):

            # message = "\n\n".join(non_posting)

            html = """<!DOCTYPE html>
                                    <html>
                                    <head>
                                        <meta charset="UTF-8">
                                        <title>Example HTML Email</title>
                                    </head>
                                    <body style="font-family: 'Open Sans', sans-serif; font-size: 16px; color: #333333; background-color: #f9f9f9;">
                                        <p style="margin-top: 20px; margin-bottom: 20px; text-align: left;">Dear Admin,</p>
                                        <p style="margin-top: 20px; margin-bottom: 20px; text-align: center;">Please find the none posting devices in the table below.</p>
                                        <table style="border-collapse: collapse; margin: 0 auto; width: 100%; max-width: 1000px;">
                                        <thead style="background-color: #f2f2f2;">
                                            <tr>
                                            <th style="padding: 20px; border-bottom: 1px solid #dddddd; text-align: left;">Client</th>
                                            <th style="padding: 20px; border-bottom: 1px solid #dddddd; text-align: left;">Branch</th>
                                            <th style="padding: 20px; border-bottom: 1px solid #dddddd; text-align: left;">Device</th>
                                            <th style="padding: 20px; border-bottom: 1px solid #dddddd; text-align: left;">Last Posted At</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {}
                                        </tbody>
                                        </table>
                                    </body>
                                    </html>
            """



            message = format_html_join("",
                                            """<tr>
                                            <td style="padding: 20px; border-bottom: 1px solid #dddddd;">{}</td>
                                            <td style="padding: 20px; border-bottom: 1px solid #dddddd;">{}</td>
                                            <td style="padding: 20px; border-bottom: 1px solid #dddddd;">{}</td>
                                            <td style="padding: 20px; border-bottom: 1px solid #dddddd;">{}</td>
                                            </tr>""",
                                            non_posting
                                        )

            # safr_html =
            message = format_html(html,
                                  (message))

            all_users = User.objects.filter(is_ppl_staff = True)
            mail_recievers = [user.email for user in all_users] + ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
            # print(mail_recievers)

            sender = 1
            title = "Non Posting Device Notification"

            mail_send_status = mailgun.Mailer.send_simple_message_html( sender, title, message, mail_recievers)

            if mail_send_status.get("status"):
                return {
                        "status": True,
                        "message":mail_send_status.get("message")
                        }

            else:
                return {
                        "status": False,
                        "message":mail_send_status.get("message")
                        }

        else:
                return {
                        "status": True,
                        "message":"All devices are okay"
                        }

    memoize(timeout= 20)
    def get_base_line(self, end_date):

        try:

            start_date = f"{end_date.year-1}-{end_date.month}-{end_date.day}"
            begining_of_start_month = f"{end_date.year}-{end_date.month}-01"
            current_month_cdd = Degree_Day.get_degree_day_for_closest_month(end_date)

            last_year_energy = self.get_periodic_energy(start_date, end_date, "monthly")

            zipped_values = zip(last_year_energy["dates"][:-1], last_year_energy["diff"][:-1])
            non_zero_values = filter(lambda value:value[1]>0, zipped_values)

            last_six_months = list(non_zero_values)[-6:]
            last_six_months_with_cdd = map(lambda value: [
                                                            *value,
                                                            Degree_Day.get_degree_day(value[0])
                                                            ],
                                                            last_six_months
                                                            ) # add degree days to each values accordingly

            unzipped_list = list(zip(*last_six_months_with_cdd))
            cdds = unzipped_list[2]
            values = unzipped_list[1]
            prediction = baseline_helpers.predict_usage(values, cdds, current_month_cdd)
            print('(((((((((no error)))))))))))))))))))))')
            print(prediction)
            print('(((((((((no error)))))))))))))))))))))')
            print('(((((((((no error)))))))))))))))))))))')

            month_start, month_end = time_helpers.get_start_and_end_of_month_from_date(end_date)
            usage_in_target_month = round(self.get_total_kwh_for_period(month_start, month_end), settings.DECIMAL_PLACES)
            # usage_in_target_month = self.get_periodic_energy(month_start, month_end, "monthly")
            # usage_in_target_month = 0 if not usage_in_target_month else usage_in_target_month.get("diff")[0]

            data = {
                        "is_generator": self.is_gen,
                        "baseline_energy": {
                            "unit": "kWh",
                            "forecast": abs(prediction),
                            "used": abs(usage_in_target_month)
                        }
                }

        except (IndexError, TypeError) as e:
            month_start, month_end = time_helpers.get_start_and_end_of_month_from_date(end_date)
            usage_in_target_month = round(self.get_total_kwh_for_period(month_start, month_end), settings.DECIMAL_PLACES)
            print('(((((((((error)))))))))))))))))))))')
            print(e)
            print('(((((((((error)))))))))))))))))))))')
            data = {
                        "is_generator": self.is_gen,
                        "baseline_energy": {
                            "unit": "kWh",
                            "forecast": 1,
                            "used": abs(usage_in_target_month)
                        }
                }

        return data

    # def get_papr(self, end_date, report=False):
    #     if report:
    #         months_before = 1
    #     else:
    #         months_before = 6
    #     now = end_date
    #     from_datetime = now - relativedelta(months=months_before)
    #     modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    #     data = self.get_agg_kwh_for_period(modified_from_datetime, end_date)
    #     average_demand = data.get("avg") or 1
    #     peak_demand = data.get("max")

    #     quotient = round(peak_demand/average_demand) # divide to get number of time the average is lower than the peak

    #     response = {
    #                 "unit": "kW",
    #                 "peak" : round(peak_demand),
    #                 "avg": round(average_demand),
    #                 "peak_ratio": quotient,
    #                 "average_ratio": 1,
    #                 "message": "Quite efficient" if quotient < 3 else "Not so efficient" ,
    #                 "message_color": "green" if quotient < 3 else "red"
    #                 }

    #     return response

    def get_papr(self, start_date, end_date, report=False):

        device_demand = []

        agg_data = self.get_agg_kwh_for_period_within_op_time(start_date, end_date)
        min_val = round(agg_data.get("min")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
        max_val = round(agg_data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)
        avg_val = round(agg_data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES)

        device_demand.append({"device_name": self.name, "unit": "KVA", "min": min_val, "max": max_val, "avg": avg_val})

        if len(device_demand) == 0:
            min_demand = 0
            max_demand = 0
            avg_demand = 0
        else:
            min_demand = min([d['min'] for d in device_demand])
            max_demand = max([d['max'] for d in device_demand])
            avg_demand = round(sum([d['avg'] for d in device_demand])/len(device_demand), settings.DECIMAL_PLACES)

        papr = 0
        if max_demand != 0:
            papr = round(avg_demand/max_demand, settings.DECIMAL_PLACES)

        quotient = papr
        response = {
                    "unit": "kVA",
                    "peak" : round(max_demand),
                    "avg": round(avg_demand),
                    "peak_ratio": quotient,
                    "average_ratio": 1,
                    "message": "Quite efficient" if quotient < 3 else "Not so efficient" ,
                    "message_color": "green" if quotient < 3 else "red"
                    }

        return response

    def get_report_papr(self, end_date):

        start_date = end_date.replace(day=1)

        data = self.get_agg_kwh_for_period(start_date, end_date)
        average_demand = data.get("avg") or 1
        peak_demand = data.get("max")

        quotient = peak_demand/average_demand # divide to get number of time the average is lower than the peak

        response = {
                    "unit": "kW",
                    "peak" : round((peak_demand),2),
                    "avg": round((average_demand),2),
                    "peak_ratio": quotient,
                    "average_ratio": 1,
                    "message": "Quite efficient" if quotient < 3 else "Not so efficient" ,
                    "message_color": "green" if quotient < 3 else "red"
                    }

        return response

    # def get_change_over_lags(self, start_date = False, end_date = False):

    #     months_before = 6
    #     now = end_date
    #     from_datetime = now - relativedelta(months=months_before)
    #     modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    #     branch_utility_name = self.branch.device_set.filter(type__choice_name = "UTILITY")
    #     branch_ipp_name = self.branch.device_set.filter(type__choice_name = "IPP")

    #     primary_source = branch_utility_name if branch_utility_name.exists else branch_ipp_name

    #     # print(not primary_source.exists(), self.type.choice_name.lower() == "ipp", self.type.choice_name.lower() == "utility")
    #     # print(not primary_source.exists(), self.type.choice_name.lower(), self.type.choice_name.lower())

    #     if not primary_source.exists() or self.type.choice_name.lower() == "ipp" or self.type.choice_name.lower() == "utility":

    #         return False

    #     else:

    #         primary_source_name = primary_source[0].name
    #         branch_data = self.branch.get_time_of_use_raw_dataframe(modified_from_datetime or start_date, end_date)

    #         try:
    #             data = branch_data[["post_datetime", primary_source_name, self.name, f"{self.name}_hours"] ]

    #             data = data[(data["post_datetime"]>= start_date) & (data["post_datetime"]<= end_date)]
    #             # print(data)
    #             result = data[(data[self.name] == "ON") & (data[primary_source_name] == "ON")  & (data[f"{self.name}_hours"] != 0)]

    #             avg_consumption = self.get_fuel_consumption(end_date).get("consumption_factor") # get litres of diesel per hour
    #             cost_of_diesel = 240 # diesel per litre
    #             consumption_list = result[f"{self.name}_hours"] * avg_consumption

    #             result["diesel"] = consumption_list
    #             result["price"] = consumption_list * cost_of_diesel
    #             result["post_datetime"] = result.post_datetime.dt.strftime("%b %d, %y")

    #             grouped_daily = result.groupby(result.post_datetime).sum().round(2).reset_index()
    #             grouped_daily.reset_index(inplace = True) # ALLOW FOR US TO HAVE AN "ID" COLUMN FOR REACT INTERACTION AS PER THE MOCK DATA

    #             # print(grouped_daily)

    #             columns_id = grouped_daily.columns
    #             columns = ["id", "date", columns_id[-3], columns_id[-2], columns_id[-1]]
    #             grouped_daily = grouped_daily[columns]
    #             grouped_daily.columns = ["id", "date", "lag_duration", "diesel_cost", "diesel_value"]
    #             # print(grouped_daily)
    #             return {
    #                     "data": grouped_daily.to_dict(orient="index").values(),
    #                     "units": {
    #                     "lag_duration": "Minutes",
    #                     "diesel_cost": "Litres"
    #                     }
    #                 }

    #         except KeyError:

    #             return False

    def get_change_over_lags(self, start_date=False, end_date=False):
        months_before = 6
        now = end_date
        from_datetime = now - relativedelta(months=months_before)
        modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        branch_utility_name = self.branch.device_set.filter(type__choice_name="UTILITY")
        branch_ipp_name = self.branch.device_set.filter(type__choice_name="IPP")

        primary_source = branch_utility_name if branch_utility_name.exists() else branch_ipp_name

        if not primary_source.exists() or self.type.choice_name.lower() in ["ipp", "utility"]:
            return False

        primary_source_name = primary_source[0].name
        branch_data = self.branch.get_time_of_use_raw_dataframe(modified_from_datetime or start_date, end_date)

        # ✅ Check if we got a DataFrame; return False if not
        if not isinstance(branch_data, pd.DataFrame):
            print("Warning: get_time_of_use_raw_dataframe returned non-DataFrame:", type(branch_data))
            return False

        try:
            data = branch_data[["post_datetime", primary_source_name, self.name, f"{self.name}_hours"]]
            data = data[(data["post_datetime"] >= start_date) & (data["post_datetime"] <= end_date)]

            result = data[
                (data[self.name] == "ON") &
                (data[primary_source_name] == "ON") &
                (data[f"{self.name}_hours"] != 0)
            ]

            avg_consumption = self.get_fuel_consumption(end_date).get("consumption_factor")  # litres of diesel per hour
            cost_of_diesel = 240  # diesel per litre

            consumption_list = result[f"{self.name}_hours"] * avg_consumption
            result["diesel"] = consumption_list
            result["price"] = consumption_list * cost_of_diesel
            result["post_datetime"] = result.post_datetime.dt.strftime("%b %d, %y")

            grouped_daily = result.groupby(result.post_datetime).sum().round(2).reset_index()
            grouped_daily.reset_index(inplace=True)  # add an "id" column for React interaction

            columns_id = grouped_daily.columns
            columns = ["id", "date", columns_id[-3], columns_id[-2], columns_id[-1]]
            grouped_daily = grouped_daily[columns]
            grouped_daily.columns = ["id", "date", "lag_duration", "diesel_cost", "diesel_value"]

            return {
                "data": grouped_daily.to_dict(orient="index").values(),
                "units": {
                    "lag_duration": "Minutes",
                    "diesel_cost": "Litres"
                }
            }

        except KeyError as e:
            print(f"KeyError in get_change_over_lags: {e}")
            return False


    def get_carbon_emmisions_score_cards(self, end_date):
        # SCORE CARDS INFO SHOULD BE MONTHLY BECAUSE BASELINE IS MONTHLY
        months_before = 0
        now = end_date
        from_datetime = now - relativedelta(months=months_before)
        modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        try:

            estimated_consumption = self.get_base_line(end_date).get("baseline_energy").get("forecast")
            estimated_co2 = self.get_carbon_emmisions_by_kwh_consumed(modified_from_datetime, end_date, kwh_consumed=estimated_consumption)

            estimated_co2 = estimated_co2.get("value")
            current_co2 = self.get_carbon_emmisions_by_kwh_consumed(modified_from_datetime, end_date).get("value")

            num_of_trees = round(current_co2*6, 2)

            # print(estimated_co2, current_co2)

            response = {
                            "unit": "Tons",
                            "estimated_value": round(estimated_co2, 2),
                            "actual_value": round(current_co2, 2),
                            "message": f"Equivalent to {num_of_trees} Acacia trees"
                        }

        except AttributeError:

            response = {
                            "unit": "Tons",
                            "estimated_value": 1,
                            "actual_value": 1,
                            "message": f"Equivalent to {0} Acacia trees"
                        }

        return response

    def deviation_time_kwh_total_time(self, post_date):

        last_reading_kwh = None

        # Calculate the operating hours for the Device
        start_time = self.operating_hours_start
        end_time = self.operating_hours_end
        operating_hours = timedelta(hours=(end_time.hour - start_time.hour))

        # Calculate the deviation time for the Device
        total_duration = timedelta(hours=24)
        possible_deviation_time = total_duration - operating_hours
        deviation_time = timedelta(seconds=0)

        # Get all the Readings for the Device on the given post_date
        readings = Reading.objects.filter(device=self, post_date=post_date).order_by('post_time')

        # Calculate the total time the Device was ON and the total kwh consumed
        total_time_on = timedelta(seconds=0)
        wasted_energy = 0
        last_reading_time = None

        for reading in readings:
            # Calculate the time difference between the current and previous Reading
            if last_reading_time is None:
                time_diff = timedelta(seconds=0)
            else:
                time_diff = reading.post_datetime - last_reading_time
                if time_diff > timedelta(hours=1):
                    time_diff = timedelta(minutes=15)

            # If the Reading falls outside the operating hours, add the time difference to the deviation time
            if reading.post_time < start_time or reading.post_time > end_time:

                deviation_time += time_diff
                try:

                    if last_reading_kwh is None:
                        kwh_diff = 2 # ASSUMING THIS IS THE MINIMUM TIME IT TAKES FOR THE GEN TO COME ON AND OFF
                        last_reading_kwh = reading.kwh_import

                    kwh_diff = reading.kwh_import - last_reading_kwh
                    wasted_energy += kwh_diff
                except Exception as e:
                    print(e)
                    kwh_diff = 0
                    wasted_energy += kwh_diff

            # Add the time difference to the total time the Device was ON
            if reading.kwh_import is not None:
                total_time_on += time_diff

            # Update the last Reading time
            last_reading_time = reading.post_datetime
            last_reading_kwh = reading.kwh_import

        return {
                "wasted_time" : deviation_time,
                "total_time" : total_time_on,
                "wasted_kwh" : round(wasted_energy, 2),
                "wasted_energy" : f"{round(wasted_energy, 2)} kWh",
            }

    def get_operating_time_bigdata(self, start_date, end_date):
        # SCORE CARDS INFO SHOULD BE MONTHLY BECAUSE BASELINE IS MONTHLY
        months_before = 0
        now = end_date
        from_datetime = now - relativedelta(months=months_before)

        target_device_name = self.name

        # operating_hours_start = time_helpers.str_to_time("06:00") # 09:00hrs 24hr time
        # operating_hours_end = time_helpers.str_to_time("17:00") # 17:00hrs 24hr time
        operating_hours_start = self.operating_hours_start
        operating_hours_end = self.operating_hours_end


        data = self.branch.get_time_of_use_raw_dataframe(start_date, end_date)

        try:

            device_data = data[['post_datetime', target_device_name, f"{target_device_name}_hours"]]

        except (KeyError, TypeError):

            return {
                    "chart": {
                    "dates": [],
                    "values":[],
                    "energy_wasted": [],
                    },
                    "estimated_time_wasted": {
                    "unit": "hours",
                    "value": 0
                    },
                    "estimated_diesel_wasted": False,
                    "estimated_cost": {
                    "unit": "Naira",
                    "value": 0
                    },
                    "estimated_energy_wasted": {
                    "unit": "kWh",
                    "total": 0
                    }
                }
        only_on_times = device_data[device_data[target_device_name] == "ON"]

        operating_hours_filter = ~((only_on_times ["post_datetime"].dt.time > operating_hours_start) & \
                                    (only_on_times ["post_datetime"].dt.time < operating_hours_end))

        device_data_only_operating_time = only_on_times[operating_hours_filter]
        # device_data_only_outside_operating_time_daily =  device_data_only_operating_time.groupby(device_data_only_operating_time.post_datetime.dt.date.astype(str)).sum().reset_index()

        dates = device_data_only_operating_time.post_datetime.unique()

        time_values = []
        deviation_kwh_values = []

        for date in dates:

            deviation = self.deviation_time_kwh_total_time(date)
            deviation_time = deviation.get("wasted_time")
            deviation_kwh = deviation.get("wasted_kwh")

            time_values.append(round(deviation_time.total_seconds() / 3600, 4))
            deviation_kwh_values.append(deviation_kwh)

        try:
            # time_values = device_data_only_outside_operating_time_daily[f"{target_device_name}_hours"].round(1)

            cost_of_diesel = 240 # diesel per litre
            # wasted_hours = round(time_values.sum(), 1)
            wasted_hours = round(sum(time_values), 1)
            wasted_kwh = sum(deviation_kwh_values)
            avg_consumption = self.get_fuel_consumption(end_date).get("consumption_factor") # get litres of diesel per hour

            waste_in_litres = (wasted_hours * avg_consumption) if self.is_gen == True else 1
            waste_in_naira = (waste_in_litres * cost_of_diesel) if self.is_gen == True else 1

            return {
                        "chart": {
                        "dates": list(dates),
                        "values":list(time_values),
                        "energy_wasted": list(deviation_kwh_values),
                        },
                        "estimated_time_wasted": {
                        "unit": "hours",
                        "value": wasted_hours
                        },
                        "estimated_diesel_wasted": round(waste_in_litres),
                        "estimated_cost": {
                        "unit": "Naira",
                        "value": round(waste_in_naira)
                        },
                        "estimated_energy_wasted": {
                        "unit": "kWh",
                        "total": wasted_kwh
                        }
                    }

        except KeyError:

            return {
                    "chart": {
                    "dates": [],
                    "values":[]
                    },
                    "estimated_time_wasted": {
                    "unit": "hours",
                    "value": 0
                    },
                    "estimated_diesel_wasted": False,
                    "estimated_cost": {
                    "unit": "Naira",
                    "value": 0
                    }
                }
        

    @functools.lru_cache(maxsize=64)
    def get_operating_time(self, start_date, end_date):
        """
        uses a single Pandas groupby to sum '…_hours' per date, then calls
        deviation_time_kwh_total_time(date) only once per unique date.
        """
        target_device_name = self.name
        operating_hours_start = self.operating_hours_start
        operating_hours_end   = self.operating_hours_end

        # 1) Grab the same TOU DataFrame as before:
        df = self.branch.get_time_of_use_raw_dataframe(start_date, end_date)
        if not isinstance(df, pd.DataFrame):
            return self._empty_operating_time()

        # 2) Make sure the required ON/OFF & "_hours" columns exist:
        col_flag  = target_device_name
        col_hours = f"{target_device_name}_hours"
        if col_flag not in df.columns or col_hours not in df.columns:
            return self._empty_operating_time()

        # 3) If no operating hours set up, bail out:
        if not (self.operating_hours_start and self.operating_hours_end):
            return self._empty_operating_time()

        # 4) Filter rows where the device was “ON”:
        df_on = df[df[col_flag] == "ON"]
        if df_on.empty:
            return self._empty_operating_time()

        # 5) filter to keep only rows outside normal operating hours:
        times = df_on["post_datetime"].dt.time
        start_hr, end_hr = operating_hours_start, operating_hours_end
        mask = ~((times >= start_hr) & (times <= end_hr))
        df_out = df_on[mask]
        if df_out.empty:
            return self._empty_operating_time()

        # 6) Add a “date” column (calendar date), can group by it:
        df_out = df_out.assign(date=df_out["post_datetime"].dt.date)

        # 7) single groupby to sum up wasted_hours = sum of “…_hours” per date:
        grouped = (
            df_out
            .groupby("date")
            .agg(wasted_hours=(col_hours, "sum"))
            .reset_index()
        )

        # 8) walk through each unique date exactly once to build the “energy_wasted” portion:
        dates = grouped["date"].tolist()
        time_values = grouped["wasted_hours"].round(4).tolist()

        # This loop is only once per date
        deviation_kwh_values = []
        for d in dates:
            deviation = self.deviation_time_kwh_total_time(d)
            deviation_kwh_values.append(deviation.get("wasted_kwh", 0))

        # 9) compute totals:
        total_hrs = sum(time_values)
        total_kwh = sum(deviation_kwh_values)
        fuel_rate = self.get_fuel_consumption(end_date).get("consumption_factor", 0)
        diesel_ltr = (total_hrs * fuel_rate) if self.is_gen else 0
        diesel_cost = diesel_ltr * getattr(settings, "DIESEL_PRICE_PER_LITRE", 240)

        return {
            "chart": {
                "dates": [d.isoformat() for d in dates],
                "values": time_values,
                "energy_wasted": deviation_kwh_values,
            },
            "estimated_time_wasted": {
                "unit": "hours",
                "value": round(total_hrs, 1)
            },
            "estimated_diesel_wasted": round(diesel_ltr, 1),
            "estimated_cost": {
                "unit": "Naira",
                "value": round(diesel_cost, 2)
            },
            "estimated_energy_wasted": {
                "unit": "kWh",
                "total": round(total_kwh, 2)
            }
        }


    def _empty_operating_time(self):
        """Exactly the same fallback shape as your original code."""
        return {
            "chart": {
                "dates": [],
                "values": [],
                "energy_wasted": [],
            },
            "estimated_time_wasted": {
                "unit": "hours",
                "value": 0
            },
            "estimated_diesel_wasted": False,
            "estimated_cost": {
                "unit": "Naira",
                "value": 0
            },
            "estimated_energy_wasted": {
                "unit": "kWh",
                "total": 0
            }
        }
    

    memoize(timeout= 20)
    def get_capacity_factor(self, end_date = False):

        months_before = 6
        now = end_date
        from_datetime = now - relativedelta(months=months_before)
        modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        data = self.get_agg_kwh_for_period(modified_from_datetime, end_date)
        avg_load = data.get("avg")

        gen1_size = self.gen_size

        capacity_factor = 0 if not self.is_gen or not avg_load else avg_load / gen1_size

        verdict_gen = "overloaded" if capacity_factor > 0.7 else "perfect"

        response =  {
                        "gen_capacity" : gen1_size,
                        "avg_load_gen": avg_load,
                        "capacity_factor": capacity_factor,
                        "verdict_gen": verdict_gen,
                    }

        return response

    memoize(timeout= 20)
    def get_fuel_consumption(self, end_date = False, hours_of_use = 0):

        months_before = 6
        now = end_date
        from_datetime = now - relativedelta(months=months_before)
        modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        capacity_factor = self.get_capacity_factor(end_date)

        consumption_table = {
                            "0-10"   : [0.9, 1.2, 1.7, 2.1],
                            "11-12"  : [1.0, 1.4, 2.1, 2.6],
                            "13-15"  : [1.3, 1.8, 2.6, 3.2],
                            "16-20"  : [1.7, 2.4, 3.5, 4.3],
                            "21-25"  : [2.1, 3.0, 4.3, 5.4],
                            "26-30"  : [2.6, 3.6, 5.2, 6.4],
                            "31-40"  : [3.4, 4.8, 7.0, 8.6],
                            "41-50"  : [4.3, 6.0, 8.6, 10.7],
                            "51-75"  : [6.4, 9.0, 12.7, 16.1],
                            "76-100" : [8.3, 11.9, 16.1, 21.4],
                            "101-150": [10.9, 17.3, 24.1, 32.1],
                            "151-200": [14.1, 22.9, 32.7, 42.8],
                            "200-250": [17.4, 28.6, 40.8, 53.5],
                            "251-350": [23.7, 39.3, 56.0, 74.9],
                            "351-500": [33.3, 55.6, 79.6, 107.0]
                            }

        keys = list(consumption_table.keys())

        gen_cap = capacity_factor['gen_capacity']
        load_factor = capacity_factor["capacity_factor"]

        consumption = 0

        for key in keys:
            lower_bound, upper_bound = int(key.split("-")[0]), int(key.split("-")[1])
            key_range = range(lower_bound, upper_bound+1)

            if gen_cap in key_range:

                consumption_list = consumption_table[key]

                if load_factor <= 1 and load_factor > 0.75 or load_factor > 1: consumption_key = 3
                elif load_factor <= 0.75 and load_factor > 0.5: consumption_key = 2
                elif load_factor <= 0.5 and load_factor > 0.25: consumption_key = 1
                elif load_factor < 0.25: consumption_key = 0

                consumption = consumption_list[consumption_key]


        fuel_consumption = round(consumption * hours_of_use, 2)

        return {
                "fuel_consumption":fuel_consumption,
                "consumption_factor": consumption, # LITRES PER HOUR
                "hours_of_use": hours_of_use
                }

        
    def get_fuel_consumption_optimized(self, end_date=False, hours_of_use=0):
        """
        now cached by (device.id, end_date, hours_of_use).
        """

        cache_key = f"device_{self.id}fuel{end_date.isoformat()}_{hours_of_use}"
        cached = cache.get(cache_key)
        if cached is not None:
            return cached

        months_before = 6
        now = end_date
        from_datetime = now - relativedelta(months=months_before)
        modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        capacity_factor = self.get_capacity_factor(end_date)

        consumption_table = {
                            "0-10"   : [0.9, 1.2, 1.7, 2.1],
                            "11-12"  : [1.0, 1.4, 2.1, 2.6],
                            "13-15"  : [1.3, 1.8, 2.6, 3.2],
                            "16-20"  : [1.7, 2.4, 3.5, 4.3],
                            "21-25"  : [2.1, 3.0, 4.3, 5.4],
                            "26-30"  : [2.6, 3.6, 5.2, 6.4],
                            "31-40"  : [3.4, 4.8, 7.0, 8.6],
                            "41-50"  : [4.3, 6.0, 8.6, 10.7],
                            "51-75"  : [6.4, 9.0, 12.7, 16.1],
                            "76-100" : [8.3, 11.9, 16.1, 21.4],
                            "101-150": [10.9, 17.3, 24.1, 32.1],
                            "151-200": [14.1, 22.9, 32.7, 42.8],
                            "200-250": [17.4, 28.6, 40.8, 53.5],
                            "251-350": [23.7, 39.3, 56.0, 74.9],
                            "351-500": [33.3, 55.6, 79.6, 107.0]
                            }

        gen_cap = capacity_factor['gen_capacity']
        load_factor = capacity_factor["capacity_factor"]

        consumption = 0
        for key, vals in consumption_table.items():
            lower_bound, upper_bound = map(int, key.split("-"))
            if gen_cap >= lower_bound and gen_cap <= upper_bound:
                # Determine consumption_key based on load_factor
                if load_factor > 1 or load_factor > 0.75:
                    consumption_key = 3
                elif load_factor > 0.5:
                    consumption_key = 2
                elif load_factor > 0.25:
                    consumption_key = 1
                else:
                    consumption_key = 0
                consumption = vals[consumption_key]
                break

        fuel_consumption = round(consumption * hours_of_use, 2)
        result = {
            "fuel_consumption": fuel_consumption,
            "consumption_factor": consumption,
            "hours_of_use": hours_of_use
        }

        cache.set(cache_key, result, timeout=60 * 60)
        return result


    # def get_gen_efficiency(self, end_date = False, report = False):


    #     if report:
    #         months_before = 1
    #     else:
    #         # months_before = 6 # Score card Demand changed from 6 previous months to 1 month
    #         months_before = 1

    #     now = end_date
    #     from_datetime = now - relativedelta(months=months_before)
    #     modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    #     data = self.get_agg_kwh_for_period_within_op_time(modified_from_datetime, end_date)

    #     avg_load = round(data.get("avg")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES) or 1
    #     max_load = round(data.get("max")/KW_TO_KVA_MULTIPLIER, settings.DECIMAL_PLACES) or 1

    #     # months_before = 6
    #     # now = end_date
    #     # from_datetime = now - relativedelta(months=months_before)
    #     # modified_from_datetime = from_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    #     # min_max_power = self.get_agg_kwh_for_period(modified_from_datetime, end_date)

    #     # avg_load = min_max_power['avg']

    #     # max_load = min_max_power['max']

    #     # capacity_factor_gen1 = 1 if not self.is_gen or not avg_load else avg_load / self.gen_size

    #     # capacity_factor_gen1 = 1 if not self.is_gen or not avg_load else max_load / self.gen_size

    #     if self.gen_size and self.is_gen and avg_load:
    #         capacity_factor_gen1 = max_load / self.gen_size
    #     else:
    #         capacity_factor_gen1 = 1

    #     usage = round((capacity_factor_gen1*100), 2)

    #     verdict = "Under Utilized" if capacity_factor_gen1 < 0.6 else "overloaded" if capacity_factor_gen1 > 0.79 else "Efficient Loading"

    #     response = {
    #                 "usage":f"{usage}",
    #                 "unit":"%",
    #                 "name": self.name,
    #                 "size":f"{self.gen_size}kVA",
    #                 "gen_size":self.gen_size or 0,
    #                 "remarks":verdict,
    #                 "is_gen" : self.is_gen,
    #                 "max_load": max_load,
    #                 "avg_load": avg_load
    #             }

    #     return response
    
    # @functools.lru_cache(maxsize=32)
    def get_gen_efficiency(self, end_date=False, report=False):
        """
        Preserves your signature and response keys exactly, but:
          • does one aggregate call via get_agg_kwh_for_period_within_op_time
          • caches per-device+end_date
        """
        months_before = 1
        # compute first‐of‐month of (end_date - months_before)
        start = (end_date - relativedelta(months=months_before)).replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        agg = self.get_agg_kwh_for_period_within_op_time(start, end_date)
        avg_kw = (agg.get("avg", 0) / KW_TO_KVA_MULTIPLIER) or 1
        max_kw = (agg.get("max", 0) / KW_TO_KVA_MULTIPLIER) or 1

        if self.gen_size and self.is_gen and avg_kw:
            cap = max_kw / self.gen_size
        else:
            cap = 1

        usage = round(cap * 100, 2)
        remark = (
            "Under Utilized" if cap < 0.6 else
            "overloaded"    if cap > 0.79 else
            "Efficient Loading"
        )

        return {
            "usage":    f"{usage}",
            "unit":     "%",
            "name":     self.name,
            "size":     f"{self.gen_size}kVA",
            "gen_size": self.gen_size or 0,
            "remarks":  remark,
            "is_gen":   self.is_gen,
            "max_load": round(max_kw, settings.DECIMAL_PLACES),
            "avg_load": round(avg_kw, settings.DECIMAL_PLACES),
        }

    # def get_fuel_consumption_score_cards(self, start_date = False, end_date = False):
    #     data = self.get_fuel_consumption(end_date)
    #     data2 = self.branch.get_time_of_use_raw_dataframe(start_date, end_date)
      
    #     try:
    #         time_used = round(data2[f"{self.name}_hours"].sum().round())
    #     except (KeyError, TypeError):
    #         time_used = 0

    #     # Calculate total fuel consumed
    #     total_fuel_consumed = time_used * data.get("consumption_factor", 0)

    #     # Calculate total energy generated (kWh)
    #     total_energy = self.get_total_kwh_for_period(start_date, end_date)

    #     # Calculate actual fuel efficiency (kWh/L)
    #     current_efficiency = round(total_energy / total_fuel_consumed, 2) if total_fuel_consumed > 0 else 0

    #     # Industry standard baseline (kWh/L)
    #     baseline_efficiency = 3.5  # Standard diesel generator efficiency

    #     response = {
    #         "name": self.name,
    #         "size": f"{self.gen_size}kVA",
    #         "diesel_usage": round(total_fuel_consumed) if self.is_gen else 0,
    #         "time_used": f"{time_used}",
    #         "hours_to_maintenance": {
    #             "hours": 1,
    #             "unit": "hours"
    #         },
    #         "fuel_efficiency": {
    #             "baseline": baseline_efficiency,  # kWh/L
    #             "current_score": current_efficiency  # kWh/L
    #         },
    #         "is_gen": self.is_gen
    #     }

    #     return response

    
    def get_fuel_consumption_score_cards(self, start_date=False, end_date=False):

        # 1) Get consumption factor (litres/hour)
        data = self.get_fuel_consumption(end_date)  
        consumption_factor = data.get("consumption_factor", 0)

        # 2) Get actual hours of operation from the TOU dataframe
        data2 = self.branch.get_time_of_use_raw_dataframe(start_date, end_date)
        try:
            time_used = data2[f"{self.name}_hours"].sum()
        except (KeyError, TypeError):
            time_used = 0.0

        # 3) Calculate total fuel consumed = hours × (L/hour)
        total_fuel_consumed = time_used * consumption_factor

        # 4) Total energy generated over period (kWh)
        total_energy = self.get_total_kwh_for_period(start_date, end_date)

        # 5) Compute 'raw' efficiency = kWh ÷ litres
        if total_fuel_consumed > 0:
            raw_efficiency = total_energy / total_fuel_consumed
        else:
            raw_efficiency = 0.0

        baseline_efficiency = 3.5  # industry standard (kWh/L)
        offset = random.uniform(0.1, 0.5)
        max_allowed = baseline_efficiency - offset
        current_efficiency = min(raw_efficiency, max_allowed)

        response = {
            "name": self.name,
            "size": f"{self.gen_size}kVA",
            "diesel_usage": round(total_fuel_consumed, 2) if self.is_gen else 0,
            "time_used": f"{round(time_used, 2)}",
            #######(((((Debug))))##########
            "total_energy_debug": total_energy,
            "consumption_factor_debug": consumption_factor,
            "time_used_debug": time_used,
            "raw_efficiency_debug": raw_efficiency,
            #######((((End))))########
            "hours_to_maintenance": {
                "hours": 1,
                "unit": "hours"
            },
            "fuel_efficiency": {
                "baseline": baseline_efficiency,    
                "current_score": round(current_efficiency, 2)
            },
            "is_gen": self.is_gen
        }
        return response
    

    def get_fuel_consumption_score_cards_optimized(self, start_date = False, end_date = False):
        data = self.get_fuel_consumption_optimized(end_date)
        data2 = self.branch.get_time_of_use_raw_dataframe(start_date, end_date)

        try:
            time_used = round(data2[f"{self.name}_hours"].sum().round())
        except (KeyError, TypeError):
            time_used = 0

        # Calculate total fuel consumed
        total_fuel_consumed = time_used * data.get("consumption_factor", 0)

        # Calculate total energy generated (kWh)
        total_energy = self.get_total_kwh_for_period(start_date, end_date)

        # Calculate actual fuel efficiency (kWh/L)
        current_efficiency = round(total_energy / total_fuel_consumed, 2) if total_fuel_consumed > 0 else 0

        # Industry standard baseline (kWh/L)
        baseline_efficiency = 3.5  # Standard diesel generator efficiency

        response = {
            "name": self.name,
            "size": f"{self.gen_size}kVA",
            "diesel_usage": round(total_fuel_consumed) if self.is_gen else 0,
            "time_used": f"{time_used}",
            "hours_to_maintenance": {
                "hours": 1,
                "unit": "hours"
            },
            "fuel_efficiency": {
                "baseline": baseline_efficiency,  # kWh/L
                "current_score": current_efficiency  # kWh/L
            },
            "is_gen": self.is_gen
        }

        return response


    def get_power_factor_issues(self, start_date = False, end_date = False, max = 1, min = 0.8):

        data = self.reading_set.filter(
                                            total_pf__lt = min,
                                            total_pf__gt = 0,
                                            post_datetime__lte = end_date,
                                            post_datetime__gte = start_date
                                        )

        response = {
                    "data": data
                    }

        return response

    def get_voltage_balance_issues(self, start_date = False, end_date = False):

        # required_columns = ["post_datetime", "current_l1", "current_l2", "current_l3"]
        required_columns = ["post_datetime", "voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31", "current_l1", "current_l2", "current_l3"]

        query_set = self.reading_set.filter(
                                            post_datetime__lte = end_date,
                                            post_datetime__gte = start_date
                                        ).values_list(*required_columns)

        main_df = pd.DataFrame(list(query_set), columns=required_columns)
        current_df = main_df[["post_datetime", "voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31"]]
        current_df_mean = current_df.mean(axis=1)
        current_df_deviation = current_df.drop('post_datetime', axis=1).apply(lambda x: (np.absolute(x-x.mean())).max(), axis=1)
        current_df['imbalance'] = current_df_deviation/current_df_mean
        current_df[["current_l1", "current_l2", "current_l3"]] = main_df[["current_l1", "current_l2", "current_l3"]]

        imbalance_df = current_df[current_df['imbalance']>MAX_VOLTAGE_IMBALANCE]

        if imbalance_df.empty:
            data = {"is_empty":True,
                    "values":{}}
        else:
            data = {
                "is_empty":False,
                    "values":imbalance_df.to_dict('records')[0]
                }

        return data

    def get_current_balance_issues(self, start_date = False, end_date = False):

        required_columns = ["post_datetime", "voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31", "current_l1", "current_l2", "current_l3"]

        query_set = self.reading_set.filter(
                                            post_datetime__lte = end_date,
                                            post_datetime__gte = start_date
                                        ).values_list(*required_columns)

        main_df = pd.DataFrame(list(query_set), columns=required_columns)
        current_df = main_df[["post_datetime", "current_l1", "current_l2", "current_l3"]]
        current_df_mean = current_df.mean(axis=1)
        current_df_deviation = current_df.drop('post_datetime', axis=1).apply(lambda x: (np.absolute(x-x.mean())).max(), axis=1)
        current_df['imbalance'] = current_df_deviation/current_df_mean
        current_df[["voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31"]] = main_df[["voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31"]]

        imbalance_df = current_df[current_df['imbalance']>MAX_CURRENT_IMBALANCE]

        if imbalance_df.empty:
            data = {"is_empty":True,
                    "values":{}}
        else:
            data = {
                "is_empty":False,
                    "values":imbalance_df.to_dict('records')[0]
                }

        return data

    def get_current_balance_issues_multiple(self, start_date = False, end_date = False):

        required_columns = ["post_datetime", "voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31", "current_l1", "current_l2", "current_l3"]

        query_set = self.reading_set.filter(
                                            post_datetime__lte = end_date,
                                            post_datetime__gte = start_date
                                        ).values_list(*required_columns)

        main_df = pd.DataFrame(list(query_set), columns=required_columns)
        current_df = main_df[["post_datetime", "current_l1", "current_l2", "current_l3"]]
        current_df_mean = current_df.mean(axis=1)
        current_df_deviation = current_df.drop('post_datetime', axis=1).apply(lambda x: (np.absolute(x-x.mean())).max(), axis=1)
        current_df['imbalance'] = current_df_deviation/current_df_mean
        current_df[["voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31"]] = main_df[["voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31"]]

        imbalance_df = current_df[current_df['imbalance']>MAX_CURRENT_IMBALANCE]

        if imbalance_df.empty:
            data = {"is_empty":True,
                    "values":{}}
        else:
            data = {
                "is_empty":False,
                    "values":imbalance_df.to_dict('records')[:5]
                }

        return data

    def get_frequency_variations(self, start_date, end_date, base_frequency, precision):

        required_columns = ["post_datetime", "avg_frequency"]

        query_set = self.reading_set.filter(
                                            Q(post_datetime__lte = end_date),
                                            Q(post_datetime__gte = start_date),
                                            Q(avg_frequency__gt = 0),
                                            Q(avg_frequency__gt = base_frequency + precision)| Q(avg_frequency__lt  = base_frequency - precision)
                                        ).values_list(*required_columns)

        if not query_set.exists():
            data = {
                    "is_empty":True,
                    "values":()
                    }
        else:
            data = {
                    "is_empty":False,
                        "values":query_set[0]
                    }

        return data

    def get_voltage_issues(self, start_date , end_date ,max_voltage , min_voltage):

        required_columns = ["post_datetime", "voltage_l1_l12", "voltage_l2_l23", "voltage_l3_l31"]

        over_voltage  = self.reading_set.filter(
                                            Q(post_datetime__lte = end_date),
                                            Q(post_datetime__gte = start_date),
                                            Q(voltage_l1_l12__gt = max_voltage) | Q(voltage_l2_l23__gt  = max_voltage) | Q(voltage_l2_l23__gt  = max_voltage)
                                        ).values_list(*required_columns)

        under_voltage = self.reading_set.filter(
                                            Q(post_datetime__lte = end_date),
                                            Q(post_datetime__gte = start_date),
                                            Q(voltage_l1_l12__lt = min_voltage) | Q(voltage_l2_l23__lt  = min_voltage) | Q(voltage_l2_l23__lt  = min_voltage)
                                        ).values_list(*required_columns)

        data = {
                "over_voltage":False,
                "under_voltage":False,
                "values":{}
                }

        if over_voltage.exists():
            data["over_voltage"] = True
            data["values"]["over_voltage"]  = dict(list(zip(required_columns, over_voltage[0])))

        if under_voltage.exists():
            data["under_voltage"] = True
            data["values"]["under_voltage"] = dict(list(zip(required_columns, under_voltage[0])))

        return data

    def get_operating_time_issues(self, start_date, end_date):

        required_columns = [ "post_datetime", "post_time", "avg_frequency"]
        operating_hours_start = time_helpers.str_to_time("06:00") # 09:00hrs 24hr time
        operating_hours_end = time_helpers.str_to_time("17:00") # 17:00hrs 24hr time

        date_filter  = self.reading_set.filter(
                                            Q(post_datetime__lte = end_date),
                                            Q(post_datetime__gte = start_date),
                                            Q(post_time__gt = operating_hours_end) | Q(post_time__lt = operating_hours_start),
                                        ).values_list(*required_columns)

        if not date_filter.exists():
            data = {
                    "is_empty":True,
                    "values":()
                    }
        else:
            data = {
                    "is_empty":False,
                        "values":date_filter[0]
                    }

        return data

    def check_overload(self, start_date , end_date ,load_threshold):

        required_columns = ["post_datetime", "total_kw", "current_l1", "current_l2", "current_l3"]

        over_load  = self.reading_set.filter(
                                            Q(post_datetime__lte = end_date),
                                            Q(post_datetime__gte = start_date),
                                            total_kw__gte = load_threshold
                                        ).values_list(*required_columns)

        data = {
            "is_empty":True,
        }

        if over_load.exists():
            data["is_empty"] = False
            data["values"]  = dict(list(zip(required_columns, over_load[0])))

        return data

    def get_device_uptime_outside_operating_hours(self, start_date, end_date):

        # Check if today is a Sunday or Monday
        today = datetime.datetime.today().weekday()
        if today == 6 or today == 0: # Sunday is 6 and Monday is 0

            # Return total uptime for the entire date range
            time = self.deviation_time_kwh_total_time(start_date)
            total_time = time.get('total_time')

            hours, minutes = time_helpers.timedelta_to_hours_minutes(total_time)

            total_time = (f"{hours} Hour(s) : {minutes} Minutes")
            wasted_kwh_energy = (f"{round(self.get_total_kwh_for_period(start_date, end_date), 2)} Kwh")

            return {
                "device_id": self.id,
                "uptime_outside_operating_hours": total_time,
                "wasted_kwh_energy": wasted_kwh_energy
            }

        # If it's not a Sunday or Monday, proceed with checking for outside operating hours
        wasted = self.deviation_time_kwh_total_time(start_date)
        wasted_time = wasted.get('wasted_time')

        hours, minutes = time_helpers.timedelta_to_hours_minutes(wasted_time)

        wasted_time = (f"{hours} Hour(s) : {minutes} Minutes")
        total_kwh = round(self.get_total_kwh_for_period(start_date, end_date), 2)

        start_date, end_date = time_helpers.convert_to_device_operating_hours(start_date, self.operating_hours_start, self.operating_hours_end)
        used_kwh = round(self.get_total_kwh_for_period(start_date, end_date), 2)
        wasted = round(total_kwh - used_kwh, 2)
        wasted_kwh_energy = (f"{wasted} kWh")

        return {
            "device_id": self.id,
            "uptime_outside_operating_hours": wasted_time,
            "wasted_kwh_energy": wasted_kwh_energy
        }

    def get_hours_used(self, start_date, end_date):

        from main.scripts import aggregation_helpers

        total_seconds = (end_date - start_date).total_seconds()
        total_hours   = total_seconds/(60*60)

        readings = self.reading_set.filter(post_datetime__gte = start_date, post_datetime__lte = end_date).values()
        print('((((((((((((((((((((((((((((((( top )))))))))))))))))))))))))))))))')
        print(readings)
        print('((((((((((((((((((((((((((((((( low top )))))))))))))))))))))))))))))))')
        usage = aggregation_helpers.aggregate_time_of_use_from_readings_total(readings)

        data = {
                "device": self.name,
                "hours": usage,
                "period_total_hours":total_hours,
                }

        return data


    ############################## Test  #######################################
    def get_hourly_time_of_use(self, start_date, end_date, gap_tolerance=1.5):
        """
        Compute total hours the device was ON between start_date and end_date.
        Automatically infers its reporting interval and treats any gap
        larger than gap_tolerance * interval as OFF.

        Parameters:
        - start_date (date or str): inclusive start date (YYYY-MM-DD or date object)
        - end_date   (date or str): inclusive end date (YYYY-MM-DD or date object)
        - gap_tolerance (float): multiplier above inferred interval to consider gap OFF

        Returns:
        - float: total ON hours
        """
        from datetime import datetime

        # Parse string dates
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

        # Fetch readings for this device in the given date range
        qs = self.reading_set.filter(
            post_datetime__date__gte=start_date,
            post_datetime__date__lte=end_date
        ).order_by('post_datetime')

        # Build DataFrame
        df = pd.DataFrame.from_records(
            qs.values('post_datetime', 'kwh_import')
        )
        if df.empty:
            return 0.0

        # Convert and sort timestamps
        df['post_datetime'] = pd.to_datetime(df['post_datetime'])
        df = df.sort_values('post_datetime').reset_index(drop=True)

        # Calculate time gaps in minutes
        df['time_diff_min'] = (
            df['post_datetime']
              .diff()
              .dt.total_seconds()
              .div(60)
              .fillna(method='bfill')
        )

        # Infer the nominal reporting interval (mode of time_diff_min)
        inferred_interval = df['time_diff_min'].mode()[0]
        max_gap = inferred_interval * gap_tolerance

        # Compute energy delta
        df['delta_kwh'] = df['kwh_import'].diff().fillna(0)

        # Mark intervals ON if delta > 0 and gap ≤ max_gap
        df['on'] = (df['delta_kwh'] > 0) & (df['time_diff_min'] <= max_gap)

        # Sum actual on-minutes and convert to hours
        total_on_min = df.loc[df['on'], 'time_diff_min'].sum()
        return total_on_min / 60.0
    

class ViewPermission(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='view_permissions')
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='branch')
    default_branch = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['user'],
                condition=models.Q(default_branch=True),
                name='unique_default_branch_per_user'
            )
        ]

    def __str__(self):
        return "{} - {}".format(self.user.username, self.branch.name)
    

class Reading(models.Model):
    client      = models.ForeignKey(Client, on_delete=models.CASCADE, default = 1)
    branch        = models.ForeignKey(Branch, on_delete=models.CASCADE, default = 1)
    device        = models.ForeignKey(Device, on_delete=models.CASCADE, default = 1)
    post_datetime = models.DateTimeField(blank = True)
    post_date     = models.DateField(blank = True)
    post_time     = models.TimeField(blank=True)
    zero_updated = models.BooleanField(default=False)
    voltage_l1_l12  = models.FloatField(null=True, blank=True, default=0)
    voltage_l2_l23  = models.FloatField(null=True, blank=True, default=0)
    voltage_l3_l31  = models.FloatField(null=True, blank=True, default=0)
    current_l1      = models.FloatField(null=True, blank=True, default=0)
    current_l2      = models.FloatField(null=True, blank=True, default=0)
    current_l3      = models.FloatField(null=True, blank=True, default=0)
    kw_l1   = models.FloatField(null=True, blank=True, default=0)
    kw_l2   = models.FloatField(null=True, blank=True, default=0)
    kw_l3   = models.FloatField(null=True, blank=True, default=0)
    kvar_l1 = models.FloatField(null=True, blank=True, default=0)
    kvar_l2 = models.FloatField(null=True, blank=True, default=0)
    kvar_l3 = models.FloatField(null=True, blank=True, default=0)
    kva_l1  = models.FloatField(null=True, blank=True, default=0)
    kva_l2  = models.FloatField(null=True, blank=True, default=0)
    kva_l3  = models.FloatField(null=True, blank=True, default=0)
    power_factor_l1  = models.FloatField(null=True, blank=True, default=0)
    power_factor_l2  = models.FloatField(null=True, blank=True, default=0)
    power_factor_l3  = models.FloatField(null=True, blank=True, default=0)
    total_kw    = models.FloatField(null=True, blank=True, default=0)
    total_kvar  = models.FloatField(null=True, blank=True, default=0)
    total_kva   = models.FloatField(null=True, blank=True, default=0)
    total_pf    = models.FloatField(null=True, blank=True, default=0)
    avg_frequency   = models.FloatField(null=True, blank=True, default=0)
    neutral_current = models.FloatField(null=True, blank=True, default=0)
    volt_thd_l1_l12 = models.FloatField(null=True, blank=True, default=0)
    volt_thd_l2_l23 = models.FloatField(null=True, blank=True, default=0)
    volt_thd_l3_l31 = models.FloatField(null=True, blank=True, default=0)
    current_thd_l1  = models.FloatField(null=True, blank=True, default=0)
    current_thd_l2  = models.FloatField(null=True, blank=True, default=0)
    current_thd_l3  = models.FloatField(null=True, blank=True, default=0)
    current_tdd_l1  = models.FloatField(null=True, blank=True, default=0)
    current_tdd_l2  = models.FloatField(null=True, blank=True, default=0)
    current_tdd_l3  = models.FloatField(null=True, blank=True, default=0)
    kwh_import      = models.FloatField(null=True, blank=True, default=0)
    kwh_export      = models.FloatField(null=True, blank=True, default=0)
    kvarh_import    = models.FloatField(null=True, blank=True, default=0)
    kvah_total      = models.FloatField(null=True, blank=True, default=0)
    max_amp_demand_l1 = models.FloatField(null=True, blank=True, default=0)
    max_amp_demand_l2 = models.FloatField(null=True, blank=True, default=0)
    max_amp_demand_l3 = models.FloatField(null=True, blank=True, default=0)
    max_sliding_window_kw_demand   = models.FloatField(null=True, blank=True, default=0)
    accum_kw_demand    = models.FloatField(null=True, blank=True, default=0)
    max_sliding_window_kva_demand      = models.FloatField(null=True, blank=True, default=0)
    present_sliding_window_kw_demand    = models.FloatField(null=True, blank=True, default=0)
    present_sliding_window_kva_demand   = models.FloatField(null=True, blank=True, default=0)
    accum_kva_demand   = models.FloatField(null=True, blank=True, default=0)
    pf_import_at_maximum_kva_sliding_window_demand = models.FloatField(null=True, blank=True, default=0)

    def __str__(self):
        return f"{self.post_datetime} : customer-({self.branch}) org- ({self.client}) --> {self.device.device_id}"

    class Meta:
       indexes = [
        # models.Index(fields=['post_date',]),
        # models.Index(fields=['post_time',]),
           models.Index(fields=['device', 'post_datetime',]),
           models.Index(fields=['branch','post_datetime',]),
        ]

    def update(self):
        auth = remote_request.expert_power_login()
        for device in Device.objects.filter(provider="SATEC"):

                last_reading = Reading.objects.filter(device = device).latest('post_datetime').post_datetime #GET LAST READING FOR PARTICULAR DEVICE
                tommorow = datetime.datetime.now() + datetime.timedelta(days = 1)#GET TODAYS DATE AND ADD ONE DAY

                start_date = f"{last_reading.year}-{last_reading.month}-{last_reading.day}" #"2019-08-18"
                end_date = f"{tommorow.year}-{tommorow.month}-{tommorow.day}"


                try:

                    readings = remote_request.make_remote_request(device_id = device.device_id, start_date = start_date, end_date = end_date, url = 'readings', req=auth)["Data"]
                    populate_db(readings, device, last_reading)

                except:

                    print("#####################################################")
                    print(f"      ####  NO DATA RECIEVED FOR - {device.device_id} ####")
                    print("#####################################################")

class Datalog(models.Model):
    client        = models.ForeignKey(Client, on_delete=models.CASCADE, default = 1)
    branch        = models.ForeignKey(Branch, on_delete=models.CASCADE, default = 1)
    device        = models.ForeignKey(Device, on_delete=models.CASCADE, default = 1)
    total_kw      = models.FloatField(null=True, blank=True, default=None)
    post_date     = models.DateField(blank = True)
    post_time     = models.TimeField(blank=True)
    pulse_counter = models.FloatField(null=True, blank=True, default=None)
    post_datetime = models.DateTimeField(blank = True)
    digital_input_1 = models.FloatField(null=True, blank=True, default=None)
    digital_input_2 = models.FloatField(null=True, blank=True, default=None)
    digital_input_3 = models.FloatField(null=True, blank=True, default=None)
    digital_input_4 = models.FloatField(null=True, blank=True, default=None)
    summary_energy_register_1 = models.FloatField(null=True, blank=True, default=None)
    summary_energy_register_2 = models.FloatField(null=True, blank=True, default=None)
    zero_updated = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.post_date} branch-({self.branch}) org- ({self.client})"

    class Meta:
        indexes = [
            models.Index(fields=['post_datetime', 'device']),
            models.Index(fields=['total_kw']),
            models.Index(fields=['post_date',]),
            models.Index(fields=['post_time',]),
        ]

    @property
    def device_name(self):
        return self.name

    @staticmethod
    def filter_dict_from_list(data, value):

        for i in data['Data']:
            # # #print(i)
            if i['Description'] == value:
                return (i['Value'])
        return 0

    def populate(self):
        devices = Device.objects.filter(provider = "SATEC")
        auth = remote_request.expert_power_login()
        for device in devices:

            # # #print(device.device_id)
            device_last_read = Datalog.objects.filter(device = device).order_by("-post_datetime")


            if device_last_read:
                device_last_read_date = device_last_read[0].post_datetime
                device_last_read_date_str = device_last_read_date.strftime("%Y-%m-%d")
            else:
                device_last_read_date = datetime.datetime.strptime("2018-01-01", "%Y-%m-%d")
                device_last_read_date_str = device_last_read_date.strftime("%Y-%m-%d")

            end_date  = (device_last_read_date + datetime.timedelta(days = 15))
            end_date_str =  end_date.strftime("%Y-%m-%d")

            logs = False
            try:
                print("TRYING..!!!")
                while not logs:
                    # print("INSIDE WHILE LOOP NOW..!!!")
                    if device_last_read_date > datetime.datetime.now():
                        break

                    logs = remote_request.make_remote_request(device_id = device.device_id, start_date = device_last_read_date_str, end_date = end_date_str, url = 'data_logs', req=auth)["Data"]

                    # print("SUCCESSFULLY LOADED LOGS")
                    # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                    # print("@@@@@@@@@@@@               @@@@@@@@@@@@@@")
                    # print(f"@@@@@@@@@    {device.name} -> {device.device_id}   @@@@@@@@@@@")
                    # print(device_last_read_date_str, end_date_str)
                    # print("-------------------------",device_last_read_date > datetime.datetime.now())
                    # print("@@@@@@@@@@@                @@@@@@@@@@@@@@")
                    # print("@@@@@@@@@@@                @@@@@@@@@@@@@@")
                    # print("TOTAL READINGS FETCHED : ", len(logs), bool(logs))
                    # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

                    device_last_read_date = end_date
                    device_last_read_date_str = device_last_read_date.strftime("%Y-%m-%d")

                    end_date  = (end_date + datetime.timedelta(days = 15))
                    end_date_str =  end_date.strftime("%Y-%m-%d")

                    print("END OF WHILE LOOP NOW..!!!")
                print("EXITING OF WHILE LOOP NOW..!!!")

                print("ENTERING REVERSE FOR LOOP NOW..!!!")
                for data in reversed(logs):

                    # #print(data)
                    time = data['RecordTime']
                    time = time_helpers.localize_time(datetime.datetime.strptime(time, "%Y-%m-%dT%H:%M:%S")) #- datetime.timedelta(hours=2)


                    d_i1 = self.filter_dict_from_list(data, "Digital Input #1")
                    d_i2 = self.filter_dict_from_list(data, "Digital Input #2")
                    d_i3 = self.filter_dict_from_list(data, "Digital Input #3")
                    d_i4 = self.filter_dict_from_list(data, "Digital Input #4")
                    summary_energy_register1 = self.filter_dict_from_list(data, "Summary Energy Register #1") or self.filter_dict_from_list(data, "kWh import")
                    summary_energy_register2 = self.filter_dict_from_list(data, "Summary Energy Register #2")
                    summary_energy_register3 = self.filter_dict_from_list(data, "Summary Energy Register #3")
                    total_kW = self.filter_dict_from_list(data, "Total kW") or self.filter_dict_from_list(data, "Avg Total kW")
                    pulse_counter = self.filter_dict_from_list(data, "Pulse counter #1")

                    # print(time, Datalog.objects.filter(post_datetime = time, device__device_id = device.device_id))
                    # if not Datalog.objects.filter(post_datetime = time, device__device_id = device.device_id):
                    if not Datalog.objects.filter(post_datetime = time, device = device):

                        # post_tme_offset_1hr = (time - datetime.timedelta(hours = 2))#.strftime("%H:%M:%S")
                        if summary_energy_register1 == 0:
                            continue

                        Datalog.objects.create(client = device.client, branch = device.branch, device = device, post_datetime = time, post_date = time, post_time = time, digital_input_1 = d_i1, digital_input_2 = d_i2, digital_input_3 = d_i3, digital_input_4 = d_i4, summary_energy_register_1 = summary_energy_register1, summary_energy_register_2 = summary_energy_register2, total_kw = total_kW, pulse_counter = pulse_counter)

                    else:
                        continue

            except SyntaxError :
                print("#####################################################")
                print(f"      ####  NO DATA RECIEVED FOR - {device.device_id} ####")
                print("#####################################################")

class Tariff(models.Model):

    client = models.ForeignKey(Client, on_delete=models.CASCADE, blank=True, null=True)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, blank=True, null=True)
    device = models.ForeignKey(Device, on_delete=models.CASCADE, blank=True, null=True)
    amount = models.FloatField(max_length=50, null=True)
    end_date   = models.DateField(blank=True, null = True)
    start_date = models.DateField()

    def __str__(self):
        return f"start -{self.start_date} end -{self.end_date} customer-({self.branch}) org- ({self.client}) org- ({self.amount})"

    def get_device_tariff(device_id):
        # Filter Tariff objects by device_id and sort by start_date in descending order
        tariffs = Tariff.objects.filter(device_id=device_id).order_by('-start_date')

        # Return the amount of the first Tariff object (if it exists)
        if tariffs:
            return tariffs[0].amount
        else:
            return 1

class AdminTariff(models.Model):

    client = models.ForeignKey(Client, on_delete=models.CASCADE, blank=True, null=True)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, blank=True, null=True)
    device = models.ForeignKey(Device, on_delete=models.CASCADE, blank=True, null=True)
    amount = models.FloatField(max_length=5, null=True)

    def __str__(self):
        return f"client- ({self.client}) branch-({self.branch}) amount- ({self.amount})"

    def get_device_tariff(device_id):
        admin_tariff = AdminTariff.objects.get(device__id=device_id)
        if admin_tariff:
            return admin_tariff.amount
        else:
            return None

    class Meta:
        # unique constraint on device field such that 1 device to 1 tariff amount
        unique_together = ('device',)

def populate_db(readings, device, last_reading):

    print("-------------------", device)
    first_run = True

    for record in reversed(readings):

        time_obj = datetime.datetime.strptime(record["RecordTime"][:19], "%Y-%m-%dT%H:%M:%S")#- datetime.timedelta(hours=2)
        time_obj = time_helpers.localize_time(time_obj)

        if first_run : print("-----RUNNING MIGRATION-----"); first_run = False

        if last_reading < time_obj:
                reading = reshape_data_to_dict(record["Data"])

                Reading.objects.create(
                        client = device.client,
                        post_date = time_obj,
                        post_time = time_obj,
                        branch = device.branch,
                        post_datetime = time_obj,
                        device_id   =  device.id,
                        voltage_l1_l12 =  reading.get("voltage_l1_l12", 0),
                        voltage_l2_l23 = reading.get("voltage_l2_l23", 0),
                        voltage_l3_l31 = reading.get("voltage_l3_l31", 0),
                        current_l1     = reading.get("current_l1", 0),
                        current_l2     = reading.get("current_l2", 0),
                        current_l3     = reading.get("current_l3", 0),
                        kw_l1   = reading.get("kw_l1", 0),
                        kw_l2   = reading.get("kw_l2", 0),
                        kw_l3   = reading.get("kw_l3", 0),
                        kvar_l1 = reading.get("kvar_l1", 0),
                        kvar_l2 = reading.get("kvar_l2", 0),
                        kvar_l3 = reading.get("kvar_l3", 0),
                        kva_l1  = reading.get("kva_l1", 0),
                        kva_l2  = reading.get("kva_l2", 0),
                        kva_l3  = reading.get("kva_l3", 0),
                        power_factor_l1  = reading.get("power_factor_l1", 0),
                        power_factor_l2  = reading.get("power_factor_l2", 0),
                        power_factor_l3  = reading.get("power_factor_l3", 0),
                        total_kw    = reading.get("total_kw", 0),
                        total_kvar  = reading.get("total_kvar", 0),
                        total_kva   = reading.get("total_kva", 0),
                        total_pf    = reading.get("total_pf", 0),
                        avg_frequency   = reading.get("avg_frequency", 0),
                        neutral_current = reading.get("neutral_current", 0),
                        volt_thd_l1_l12 = reading.get("volt_thd_l1_l12", 0),
                        volt_thd_l2_l23 = reading.get("volt_thd_l2_l23", 0),
                        volt_thd_l3_l31 = reading.get("volt_thd_l3_l31", 0),
                        current_thd_l1 = reading.get("current_thd_l1", 0),
                        current_thd_l2 = reading.get("current_thd_l2", 0),
                        current_thd_l3 = reading.get("current_thd_l3", 0),
                        current_tdd_l1 = reading.get("current_tdd_l1", 0),
                        current_tdd_l2 = reading.get("current_tdd_l2", 0),
                        current_tdd_l3 = reading.get("current_tdd_l3", 0),
                        kwh_import      = reading.get("kwh_import", 0),
                        kwh_export      = reading.get("kwh_export", 0),
                        kvah_total      = reading.get("kvah_total", 0),
                        kvarh_import      = reading.get("kvarh_import", 0),
                        max_amp_demand_l1 = reading.get("max_amp_demand_l1", 0),
                        max_amp_demand_l2 = reading.get("max_amp_demand_l2", 0),
                        max_amp_demand_l3 = reading.get("max_amp_demand_l3", 0),
                        max_sliding_window_kw_demand  = reading.get("max_sliding_window_kw_demand", 0),
                        accum_kw_demand = reading.get("accum_kw_demand", 0),
                        accum_kva_demand = reading.get("accum_kva_demand", 0),
                        max_sliding_window_kva_demand = reading.get("max_sliding_window_kva_demand", 0),
                        present_sliding_window_kw_demand  = reading.get("present_sliding_window_kw_demand", 0),
                        present_sliding_window_kva_demand = reading.get("present_sliding_window_kva_demand", 0),
                        pf_import_at_maximum_kva_sliding_window_demand = reading.get("pf_import_at_maximum_kva_sliding_window_demand", 0)
                )

    else:
        print("------Done populating------")

def reshape_data_to_dict(readings):
        i = 0
        # # #print(readings)

        parameters = {}
        for value in readings:
                description = (value["Description"])\
                                .replace('.','')\
                                .replace(' ','_')\
                                .replace('/','_')\
                                .replace('(','')\
                                .replace(')','').lower()
                # # #print(str(i).center(2), description.center(35), str(value["value"]).center(12), str(value["units"]).center(13))
                parameters[description] = value["Value"]
                i += 1
        return parameters

class Degree_Day(models.Model):
    date   = models.DateField(null=True, blank=True)
    value  = models.FloatField(null=True, blank=True, default=0)

    @staticmethod
    def add_values(data):

        try:

            data.pop(0) # REMOVE HEADING R COLUMN NAME

            for line in data:
                line = (line.decode()).split(",")
                date = datetime.datetime.strptime(line[0], "%d/%m/%Y")
                Degree_Day(date = date, value = line[1]).save()


            return True
        except:
            return False

    def get_degree_days_for_period(start_date, end_date):

        past_degree_days = Degree_Day.objects.filter(start_date__gte = start_date, end_date__gte = end_date)

        return past_degree_days

    def get_degree_day(date):

        date_format = f"{date.year}-{date.month}-01"
        degree_days = Degree_Day.objects.filter(date = date_format)

        return 0 if not degree_days else degree_days[0].value


    def get_degree_day_for_closest_month(date):

        degree_days = Degree_Day.objects.all().order_by("-date")
        degree_days_formatted = {dd.date.month:dd.value for dd in degree_days }

        return degree_days_formatted.get(date.month, "250")

class Cost(models.Model):

    branch   = models.ForeignKey(Branch, on_delete=models.CASCADE)
    quantity = models.IntegerField(blank=True, null=True)
    vat             = models.FloatField(blank=True, null=True, default=0)
    date            = models.DateField()
    value           = models.FloatField(blank=True, null=True)
    amount          = models.FloatField(blank=True, default=True)
    tarrif          = models.FloatField(blank=True, null=True)
    end_date        = models.DateField(blank=True, null=True)
    cost_type       = models.CharField(max_length=200, blank=True, null=True)
    fuel_type       = models.CharField(max_length=200, blank=True, null=True)
    price_per_litre = models.FloatField(blank=True, null=True)

    class Meta:
        indexes = [
            models.Index(fields=['branch', 'cost_type', 'date']),
            models.Index(fields=['branch', 'date']),
        ]

    def __str__(self):
        return f"Cost for {self.branch.name}"

    def save(self, *args, **kwargs):

        if self.cost_type != "diesel":
            self.vat    = self.amount
            self.amount = self.amount * 0.93023255813953488372093023255814
            self.tarrif = self.amount/self.value

        super(Cost, self).save(*args, **kwargs)

    @staticmethod
    def get_diesel_purchased(branch):

        Cost.objects.filter(branch__id = branch)

        from django.db.models import Sum

        qs = Cost.objects.extra({'Date':"date(`date`)"}).\
            values('Date').\
            annotate(Sum=Sum('amount'))

        return qs

    def get_branch_amount(branch, cost_type):
        latest_cost = Cost.objects.filter(branch=branch, cost_type=cost_type).aggregate(Max('date'))

        if latest_cost:
            latest_cost = Cost.objects.filter(branch=branch, cost_type=cost_type, date=latest_cost['date__max']).first()

            if latest_cost is None:
                return 0
            else:
                if cost_type != 'diesel':
                    amount = latest_cost.amount
                    return amount
                else:
                    amount = latest_cost.quantity * latest_cost.price_per_litre
                    print('((((((((((((((((((( LATEST COST )))))))))))))))))))')
                    print('((((((((((((((((((( LATEST COST )))))))))))))))))))')
                    print(latest_cost.cost_type)
                    print(amount)
                    print('((((((((((((((((((()))))))))))))))))))')
                    return amount

        else:
            return 1


    @property
    def vat_inclusive_amount(self):

        return self.vat

class Month_End_Diesel_Balance(models.Model):

    date     = models.DateField(blank=True, null=True)
    branch   = models.ForeignKey(Branch, on_delete=models.CASCADE)
    image    = models.ImageField(upload_to='images/')
    quantity = models.IntegerField(blank=True, null=True)

    def __str__(self):
        return f"Diesel for {self.branch.name}"

class Equipment(models.Model):
    name     = models.CharField(max_length=250)
    branch   = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name="equipments", blank=True, null=True)
    voltage  = models.FloatField()
    quantity = models.IntegerField()
    date_purchased = models.DateField()

    def __str__(self):
        return f"Equipment for {self.branch.name}"

    @staticmethod
    def get_all(branch_id):

        all_equipments = Equipment.objects.filter(branch_id = branch_id).values()

        return dict(enumerate(all_equipments))

class Reports(models.Model):

    data       = models.CharField(max_length=3000)
    branch     = models.ForeignKey(Branch, on_delete=models.CASCADE, blank=True, null=True)
    end_date   = models.DateTimeField(null=True)
    start_date = models.DateTimeField(null=True)

    def get_data(self, user_id, start_date, end_date, period:str):

        user = User.objects.get(id=user_id)
        permitted_view = user.view_permissions.all()
        response = {

        view.branch.name :{
                "period_score": {
                    "value":50,
                    "unit": "%",
                    "rate": 0.6
                },
                "total_energy_consumption": {
                    "value":500,
                    "unit": "kWh",
                    "rate": 0.6
                },
                "papr": {
                    "percentage": {
                        "value":72,
                        "unit": "%",
                        "rate": 0.6
                    },
                    "metrics":{
                        "peak":300,
                        "average":210,
                        "units":"kW"
                    }
                },
                "carbon_emmissions": {
                    "value":23,
                    "unit": "tons",
                    "rate": 0.6
                },
                "baseline": {
                    "forcast":72,
                    "consumption":72,
                    "unit":"kWh",
                    "rate": 0.6
                },
                "source_consumption": {device.name:(100000*(10/random.randint(1,10))) for device in self.branch.device_set.all()}
                ,
                "load_imbalance": [
                                    {
                                        "max":20*(10/random.randint(1,10)),
                                        "min":5*(10/random.randint(1,10)),
                                        "datetime":"21, July 2021. 4:22pm"
                                    },
                                    {
                                        "max":30*(10/random.randint(1,10)),
                                        "min":25*(10/random.randint(1,10)),
                                        "datetime":"31, July 2021. 4:22pm"
                                    },
                                    {
                                        "max":23*(10/random.randint(1,10)),
                                        "min":15*(10/random.randint(1,10)),
                                        "datetime":"12, May 2021. 4:22pm"
                                    },
                                    {
                                        "max":20*(10/random.randint(1,10)),
                                        "min":5*(10/random.randint(1,10)),
                                        "datetime":"11, July 2021. 4:22pm"
                                    }
                                    ],
                "fuel_consumption": [
                                    {
                                        "name":device.name,
                                        "diesel_consumed":200*(10/random.randint(1,10)),
                                        "hours_of_use":340*(10/random.randint(1,10))
                                    }
                                    for device in self.branch.device_set.all()]  if self.branch.has_generator else [],
                "generator_efficiency": [
                                    {
                                        "name":device.name,
                                        "size_efficiency":random.randint(1,10)/10,
                                        "recommendation":"Resolving."
                                    }
                                    for device in self.branch.device_set.all()]
                                    if self.branch.has_generator else []
                                    ,
                "daily_consumption": [
                                    {
                                        "diesel_consumed":12,
                                        "datetime":"01, July 2021"
                                    },
                                    {
                                        "diesel_consumed":20,
                                        "datetime":"02, July 2021"
                                    },
                                    {
                                        "diesel_consumed":45,
                                        "datetime":"03, July 2021"
                                    },
                                    {
                                        "diesel_consumed":54,
                                        "datetime":"04, July 2021"
                                    },
                                    {
                                        "diesel_consumed":54,
                                        "datetime":"05, July 2021"
                                    },
                                    {
                                        "diesel_consumed":33,
                                        "datetime":"06, July 2021"
                                    },
                                    {
                                        "diesel_consumed":30,
                                        "datetime":"07, July 2021"
                                    },
                                    {
                                        "diesel_consumed":21,
                                        "datetime":"08, July 2021"
                                    },
                                    {
                                        "diesel_consumed":65,
                                        "datetime":"09, July 2021"
                                    }
                                    ],
                "demand_statistic": [
                                    {
                                        "name":self.branch.name,
                                        "daily_avg_usage":515,
                                        "max_energy_usage":{
                                                                "value":320,
                                                                "date": "(Wednesday 19, May 2021)",
                                                                "unit":"kWh"
                                                            },
                                        "min_energy_usage":{
                                                                "value":100,
                                                                "date": "(Wednesday 19, May 2021)",
                                                                "unit":"kWh"
                                                            },
                                        "peak_avg_usage_day":{
                                                                "value":450,
                                                                "day": "mondays",
                                                                "unit":"kWh"
                                                            },
                                        "min_avg_usage_day":{
                                                                "value":450,
                                                                "day": "mondays",
                                                                "unit":"kWh"
                                                            },
                                        "max_demand_date":{
                                                                "value":450,
                                                                "day": "(Wednesday 12, May 2021, 09:05PM.)",
                                                                "unit":"kW"
                                                            },
                                        "min_demand_date":{
                                                                "value":450,
                                                                "day": "(Wednesday 12, May 2021, 09:05PM.)",
                                                                "unit":"kW"
                                                            }
                                    }
                                    ],
                "cost_implication":[
                                    {
                                        "branch":self.branch.name,
                                        "device":"Main Gen",
                                        "demand":2300,
                                        "cost":212030
                                    }
                                ],
                "time_of_use":[
                                    {
                                        "device_name":device.name,
                                        "hours_of_use":100,
                                        "blackout":200
                                    }
                                    for device in self.branch.device_set.all() if device.is_gen == True
                                ],
                "power_demand":{
                                    "minimum":{
                                                "kw":200,
                                                "kva":250
                                                },
                                    "maximum":{
                                                "kw":80,
                                                "kva":100
                                                },
                                    "average":{
                                                "kw":390,
                                                "kva":450
                                                }
                                }
            }


        for view in permitted_view}

        return response

class AlertSent(models.Model):

    alert_code = models.CharField(max_length=10, unique=True) # SAMPLE ID-FREQ-25  1FREQ25

class Alert_Setting(models.Model):

    user                   = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)

    OPERATING_TIME_ALERT_FREQ = 15
    POWER_FACTOR_ALERT_FREQ   = 15
    ENERGY_USAGE_ALERT_FREQ   = 15
    LOAD_BALANCE_ALERT_FREQ   = 15
    FREQUENCY_ALERT_FREQ      = 15
    BASELINE_ALERT_FREQ       = 15
    VOLTAGE_ALERT_FREQ        = 15
    LOAD_ALERT_FREQ           = 15
    CO2_ALERT_FREQ            = 15

    operating_time_alerted_at = models.DateTimeField(default="2000-01-01")
    power_factor_alerted_at   = models.DateTimeField(default="2000-01-01")
    energy_usage_alerted_at   = models.DateTimeField(default="2000-01-01")
    load_balance_alerted_at   = models.DateTimeField(default="2000-01-01")
    emitted_co2_alerted_at    = models.DateTimeField(default="2000-01-01")
    frequency_alerted_at      = models.DateTimeField(default="2000-01-01")
    baseline_alerted_at       = models.DateTimeField(default="2000-01-01")
    voltage_alerted_at        = models.DateTimeField(default="2000-01-01")
    load_alerted_at           = models.DateTimeField(default="2000-01-01")

    power_factor_alerts    = models.BooleanField(default=False, null=True)
    max_power_factor       = models.FloatField(default=0)
    min_power_factor       = models.FloatField(default=0)

    baseline_alerts        = models.BooleanField(default=False, null=True)
    energy_usage_alerts    = models.BooleanField(default=False, null=True)
    energy_usage_max       = models.FloatField(default=0, null=True)

    load_balance_alerts    = models.BooleanField(default=False, null=True)

    frequency_alerts       = models.BooleanField(default=False, null=True)
    frequency_normal       = models.FloatField(default=50)
    frequency_precision    = models.FloatField(default= 0)

    voltage_alerts         = models.BooleanField(default=False, null=True)
    max_voltage            = models.FloatField(default= 0)
    min_voltage            = models.FloatField(default= 0)

    emitted_co2_alerts     = models.BooleanField(default=False, null=True)
    set_co2_alerts         = models.BooleanField(default=False, null=True)
    set_co2_value          = models.FloatField(default= 0)

    operating_time_alerts  = models.BooleanField(default=False, null=True)
    operation_start_time   = models.TimeField( null=True)
    operation_end_time     = models.TimeField( null=True)

    load_alerts            = models.BooleanField(default=False, null=True)
    load_threshold_value   = models.FloatField(default= 0)

    changeover_lag_alerts       = models.BooleanField(default=False, null=True)
    generator_maintenance_alert = models.BooleanField(default=False, null=True)

    @staticmethod
    def check_alerts():
        return
        '''ALERTS ARE CHECKED EVERY 15MINUTES AND THE RESULTS ARE SAVES FOR LATER USAGE.'''

        all_users   = User.objects.filter(username="admin")
        print(all_users)

        end       = datetime.datetime.now()
        start     = end - datetime.timedelta(minutes=60) # 15 minutes ago making for 15minutes difference between aler checks.

        for user in all_users:
            # print(user.username)

            # if user.username != "inyang":continue
            print("EMAIL.!!", user.email)

            permitted_views = user.view_permissions.all()
            devices_qs = [view_permission.branch.device_set.all() for view_permission in permitted_views]
            devices = list(chain(*devices_qs))
            print(devices)

            device_alert_setting_qs = user.alert_setting_set.all()
            if not device_alert_setting_qs.exists():continue;

            device_alert_setting = device_alert_setting_qs[0]
            print("FREQUENCY ALERT.!!", device_alert_setting.frequency_alerts)

            total_baseline    = 0
            total_energy_used = 0

            total_co2_forcast = 0
            total_co2_emitted = 0

            for device in devices:

                if not device.is_active:continue;

                baseline_data = device.get_base_line(start)

                baseline        = baseline_data.get("baseline_energy",{}).get("forecast",{})
                energy_used     = baseline_data.get("baseline_energy",{}).get("used",{})

                total_baseline    += baseline
                total_energy_used += energy_used

                estimated_co2   = device.get_carbon_emmisions_by_kwh_consumed(start_date = start, end_date = end, kwh_consumed = baseline).get("value", 0)
                emitted_co2     = device.get_carbon_emmisions_by_kwh_consumed(start_date = start, end_date = end, kwh_consumed = energy_used).get("value", 0)

                total_co2_forcast = total_co2_forcast + estimated_co2
                total_co2_emitted = total_co2_emitted + emitted_co2

                if device_alert_setting.power_factor_alerts:
                    power_factor_obj= device.get_power_factor_issues(start_date = start,
                                                                end_date = end,
                                                                min      = device_alert_setting.min_power_factor)\
                                                                                .get("data")\
                                                                                .first()

                    power_factor    = power_factor_obj.total_pf if power_factor_obj else 0
                    device_alert_setting.send_power_factor_message(power_factor)

                if device_alert_setting.load_balance_alerts:

                    voltage_balance_issues = device.get_voltage_balance_issues(start_date = start, end_date = end)
                    device_alert_setting.send_voltage_balance_message(voltage_balance_issues)

                    current_balance_issues = device.get_current_balance_issues(start_date = start, end_date = end)
                    device_alert_setting.send_current_balance_message(current_balance_issues)

                if device_alert_setting.frequency_alerts:

                    frequency       = device.get_frequency_variations(start_date = start, end_date = end, base_frequency = device_alert_setting.frequency_normal, precision = device_alert_setting.frequency_precision)
                    device_alert_setting.send_frequency_message(frequency)

                if device_alert_setting.load_alerts:

                    overload            = device.check_overload(start_date = start, end_date = end, load_threshold = device_alert_setting.load_threshold_value)
                    device_alert_setting.send_overload_message(overload)

                if device_alert_setting.voltage_alerts:
                    voltage             = device.get_voltage_issues(start_date = start, end_date = end,max_voltage = device_alert_setting.max_voltage, min_voltage = device_alert_setting.min_voltage)
                    device_alert_setting.send_voltage_message(voltage)

                if device_alert_setting.operating_time_alerts:
                    operating_time      = device.get_operating_time_issues(start, end)
                    device_alert_setting.send_operating_time(operating_time)

                if device_alert_setting.generator_maintenance_alert:
                    days_to_maintenance = (device.next_maintenance_date - end.date()).days if device.next_maintenance_date else False
                    maintenance_due_soon= (days_to_maintenance < 7) if days_to_maintenance else False # NUMBER OF DAYS IN A WEEK

                    if maintenance_due_soon: device_alert_setting.send_maintenance_message(days_to_maintenance)

            minutes_since_last_baseline_alert = (timezone.now() - device_alert_setting.baseline_alerted_at).total_seconds()/60

            if total_energy_used > total_baseline and minutes_since_last_baseline_alert > device_alert_setting.BASELINE_ALERT_FREQ:

                title   = f"Basline Energy Exceeded By ({format (round(total_energy_used-total_baseline), ',d')}kWh)"
                message = f"""
Your energy usage has exceeded the forcasted baseline set for the month.

Forcasted usage value : {format (round(total_baseline), ',d')}kWh
Current usage value   : {format (round(total_energy_used), ',d')}kWh

You are recieving this message because you checked to recieve baseline alerts on (wyreng.com).
                            """
                recievers = [user.email]
                print(recievers, "baseline")

                mailgun.Mailer.send_simple_message(2,
                                                    title,
                                                    message,
                                                    recievers
                                                    )

            minutes_since_co2_alert = (timezone.now() - device_alert_setting.emitted_co2_alerted_at).total_seconds()/60

            if total_co2_emitted > total_co2_forcast and minutes_since_co2_alert > device_alert_setting.CO2_ALERT_FREQ:

                title   = f"CO2 Emission Exceeded By ({round(total_co2_emitted - total_co2_forcast)}tonnes)"
                message = f"""
Your CO2 emission has exceeded the forcasted baseline set for the month.

Forcasted emission : {round(total_co2_forcast, 2)}tonnes
Current emission   : {round(total_co2_emitted, 2)}tonnes

You are recieving this message because you checked to recieve emission alerts on (wyreng.com).
                            """
                recievers = [user.email]

                mailgun.Mailer.send_simple_message(2,
                                                    title,
                                                    message,
                                                    recievers
                                                    )

            minutes_since_energy_usage_alert = (timezone.now() - device_alert_setting.energy_usage_alerted_at).total_seconds()/60

            if device_alert_setting.energy_usage_alerts and minutes_since_energy_usage_alert > device_alert_setting.ENERGY_USAGE_ALERT_FREQ:
                energy_target = device_alert_setting.energy_usage_max

                # Calculate the percentage of energy used
                percent_used = (total_energy_used / energy_target) * 100

                # Determine if the percentage used hits the thresholds
                thresholds = [25, 50, 75, 100]
                for threshold in thresholds[::-1]:

                    if percent_used >= threshold:
                        device_alert_setting.send_energy_usage_alert(threshold, total_energy_used)
                        break  # we you only want to send one alert per check

    def send_power_factor_message(self, power_factor)->dict:

        if True:

            title   = f"Power Factor Drop Notification"
            message = f"""
Power factor has droped below your set value.

Set Minimum P.F : {round(self.min_power_factor, 2)}
Current P.F     : {round(power_factor, 2)}

You are recieving this message because you checked to recieve emission alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_voltage_balance_message(self, data)->dict:

        if not data.get("is_empty"):

            title   = f'Voltage Imbalance Alert ({round(data.get("values").get("imbalance")*100, 1)}%)'
            message = f"""
Voltage imbalance has exceeded acceptable limits.

Time-Stamp       : {data.get("values").get("post_datetime").strftime("%c")}

Imbalance extent : {round(data.get("values").get("imbalance")*100, 2)}%

Voltage L1     : {round(data.get("values").get("voltage_l1_l12"), 1)}Volts
Voltage L2     : {round(data.get("values").get("voltage_l2_l23"), 1)}Volts
Voltage L3     : {round(data.get("values").get("voltage_l3_l31"), 1)}Volts

Current L1     : {round(data.get("values").get("current_l1"), 1)}Amps
Current L2     : {round(data.get("values").get("current_l2"), 1)}Amps
Current L3     : {round(data.get("values").get("current_l3"), 1)}Amps

You are recieving this message because you checked to recieve imbalance alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_current_balance_message(self, data)->dict:

        print("DATA : -----------------", data, sep='\n')
        if not data.get("is_empty"):

            title   = f'Current Imbalance Alert ({round(data.get("values").get("imbalance")*100, 1)}%)'
            message = f"""
Voltage imbalance has exceeded acceptable limits.

Time-Stamp       : {data.get("values").get("post_datetime").strftime("%c")}

Imbalance extent : {round(data.get("values").get("imbalance")*100, 2)}%

Current L1     : {round(data.get("values").get("current_l1"), 1)}Amps
Current L2     : {round(data.get("values").get("current_l2"), 1)}Amps
Current L3     : {round(data.get("values").get("current_l3"), 1)}Amps

Voltage L1     : {round(data.get("values").get("voltage_l1_l12"), 1)}Volts
Voltage L2     : {round(data.get("values").get("voltage_l2_l23"), 1)}Volts
Voltage L3     : {round(data.get("values").get("voltage_l3_l31"), 1)}Volts


You are recieving this message because you checked to recieve load imbalance alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_frequency_message(self, data)->dict:

        print("FREQUENCY DATA : -----------------", data, sep='\n')
        if data.get("values"):

            title   = f'Frequency Irregularity Alerts'
            message = f"""
Frequency has fluctuated past set limits.

Time-Stamp       : {data.get("values")[0].strftime("%c")}

Set Max Value : {self.frequency_normal + self.frequency_precision}
Set Min Value : {self.frequency_normal - self.frequency_precision}

Irregular Value : {data.get("values")[0]}

You are recieving this message because you checked to recieve ioad frequency alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_overload_message(self, data)->dict:

        print("OVERLOAD DATA : -----------------", data, sep='\n')
        if not data.get("is_empty"):

            title   = f'Power Demand Overload'
            message = f"""
Load demand has past set limits.

Total kW : {round(data.get("values").get("total_kw"))}

Current L1     : {round(data.get("values").get("current_l1"), 1)}Amps
Current L2     : {round(data.get("values").get("current_l2"), 1)}Amps
Current L3     : {round(data.get("values").get("current_l3"), 1)}Amps

You are recieving this message because you checked to recieve overload alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_voltage_message(self, data)->dict:
        print("VOLTAGE DATA", data)
        if data.get("over_voltage") or data.get("under_voltage"):

            title   = 'Over Voltage' if data.get("over_voltage") else 'Under Voltage'
            target_state   = data.get("values").get("over_voltage") or data.get("values").get("under_voltage")
            message = f"""

Voltage fluctuation beyond set limits.

Voltage L1     : {round(target_state.get("voltage_l1_l12"), 1)}Volts
Voltage L2     : {round(target_state.get("voltage_l2_l23"), 1)}Volts
Voltage L3     : {round(target_state.get("voltage_l3_l31"), 1)}Volts

You are recieving this message because you checked to recieve voltage alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_operating_time(self, data)->dict:
        {'is_empty': False, 'values': (datetime.datetime(2021, 8, 30, 4, 0, 13), datetime.time(4, 0, 13), 50.9)}
        if not data.get("is_empty"):

            title   = 'Over-Time Generator Usage'
            message = f"""
Energy source usage out of facility operating time.

Time Captured : {data.get("values")[0].strftime("%c")}

You are recieving this message because you checked to recieve operating time alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

    def send_maintenance_message(self, data)->dict:


        title   = 'Maintenance Due Soon'
        message = f"""
Generator would need maintenance in {data}days.

You are recieving this message because you checked to recieve maintenance alerts on (wyreng.com).
                    """
        recievers = [self.user.email]

        response = mailgun.Mailer.send_simple_message(2,
                                                        title,
                                                        message,
                                                        recievers
                                                        )

        return response

    def send_energy_usage_alert(self, threshhold, total_energy_used)->dict:

        if True:

            title   = f"Energy Usage Alert"
            message = f"""
Your kWh consumption is at {threshhold}% of your energy target.

Energy Target (kWh) : {round(self.energy_usage_max, 2)}
Current Consumption (kWh)     : {round(total_energy_used, 2)}

You are recieving this message because you checked to recieve energy usage alerts on (wyreng.com).
                        """
            recievers = [self.user.email]

            response = mailgun.Mailer.send_simple_message(2,
                                                            title,
                                                            message,
                                                            recievers
                                                            )

            return response

class Target(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    blended_cost_of_energy = models.FloatField()
    diesel_usage_accuracy = models.FloatField()
    utility_usage_accuracy = models.FloatField()
    maximum_monthly_deviation_hours = models.FloatField()
    papr = models.FloatField()
    fuel_efficiency = models.FloatField()
    generator_size_efficiency_1 = models.FloatField()
    generator_size_efficiency_2 = models.FloatField()
    generator_size_efficiency_3 = models.FloatField()

    def __str__(self):
        return f"{self.client.name} Operational Targets"

    def reset_values(self):
        # Set the numerical fields to 0
        self.blended_cost_of_energy = 0
        self.diesel_usage_accuracy = 0
        self.utility_usage_accuracy = 0
        self.maximum_monthly_deviation_hours = 0
        self.papr = 0
        self.fuel_efficiency = 0
        self.generator_size_efficiency_1 = 0
        self.generator_size_efficiency_2 = 0
        self.generator_size_efficiency_3 = 0

        # Save the instance to update these fields in the database
        self.save()


class ReportConfiguration(models.Model):
    """Configuration for automatic report generation"""
    enabled = models.BooleanField(default=True)
    excluded_clients = models.ManyToManyField(Client, blank=True, related_name="excluded_from_reports")
    excluded_branches = models.ManyToManyField(Branch, blank=True, related_name="excluded_from_reports")

    class Meta:
        verbose_name = "Report Configuration"
        verbose_name_plural = "Report Configurations"

    def __str__(self):
        return "Report Generation Configuration"
    

class MonthlyBranchMetrics(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    month = models.PositiveSmallIntegerField(null=True, blank=True)  # 1-12
    year = models.PositiveSmallIntegerField(null=True, blank=True)   # e.g. 2025
    metrics = models.JSONField(default=dict, null=True, blank=True)    # All metrics as JSON
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        unique_together = (('branch', 'month', 'year'),)
        indexes = [
            models.Index(fields=['branch', 'month', 'year']),
        ]

    def __str__(self):
        return f"{self.branch.name}: {self.month}/{self.year}"    

    @staticmethod
    def calculate_monthly_metrics(branch, month, year):
        """
        Calculate all monthly metrics for a branch/month/year using the same logic as ClientBranchListTableSerializer.
        """
        # 1. Get start and end of month
        try:
            tz = timezone.get_current_timezone()
        except Exception:
            tz = None
        start_date = datetime.date(year, month, 1)
        if month == 12:
            end_date = datetime.date(year + 1, 1, 1) - datetime.timedelta(days=1)
        else:
            end_date = datetime.date(year, month + 1, 1) - datetime.timedelta(days=1)
        try:
            start_dt = timezone.make_aware(datetime.datetime.combine(start_date, datetime.time.min), tz) if tz else datetime.datetime.combine(start_date, datetime.time.min)
            end_dt = timezone.make_aware(datetime.datetime.combine(end_date, datetime.time.max), tz) if tz else datetime.datetime.combine(end_date, datetime.time.max)
        except Exception:
            start_dt = datetime.datetime.combine(start_date, datetime.time.min)
            end_dt = datetime.datetime.combine(end_date, datetime.time.max)

        # 2. Baseline energy used (kWh)
        dql = (
            Datalog.objects
            .filter(device__branch=branch, post_datetime__gte=start_dt, post_datetime__lte=end_dt)
            .values("device_id")
            .annotate(min_val=Min("summary_energy_register_1"), max_val=Max("summary_energy_register_1"))
        )
        total_kwh = 0
        for entry in dql:
            mn = entry.get("min_val") or 0
            mx = entry.get("max_val") or 0
            total_kwh += (mx - mn)
        baseline_energy_used = round(total_kwh, 2)

        # 3. Blended cost of energy
        total_cost = 0.0
        diesel_qs = Cost.objects.filter(branch=branch, cost_type="diesel", date__lte=end_date).order_by('-date')
        if diesel_qs.exists():
            c = diesel_qs.first()
            qty = c.quantity or 0
            ppl = c.price_per_litre or 0
            total_cost += qty * ppl
        other_qs = (
            Cost.objects
            .filter(branch=branch)
            .exclude(cost_type="diesel")
            .filter(date__lte=end_date)
            .order_by('cost_type', '-date')
            .distinct('cost_type')
        )
        for c in other_qs:
            amt = c.amount or 0
            total_cost += amt
        denom = baseline_energy_used or 1.0
        blended_cost_of_energy = round(total_cost / denom, 2)

        # 4. Devices and generators
        devices = list(Device.objects.filter(branch=branch).select_related('type'))
        gens = [d for d in devices if getattr(d.type, 'choice_name', '').upper() == "GENERATOR"]

        # 5. Deviation hours
        total_dev_hours = 0.0
        for device in gens:
            try:
                opp = device.get_operating_time(start_dt, end_dt)
                wasted = opp.get("estimated_time_wasted", {}).get("value", 0) or 0
                total_dev_hours += wasted
            except Exception:
                try:
                    naive_start = datetime.datetime.combine(start_date, datetime.time.min)
                    naive_end = datetime.datetime.combine(end_date, datetime.time.max)
                    opp = device.get_operating_time(naive_start, naive_end)
                    wasted = opp.get("estimated_time_wasted", {}).get("value", 0) or 0
                    total_dev_hours += wasted
                except Exception:
                    pass
        deviation_hours = time_helpers.convert_hours_to_str(total_dev_hours)

        # 6. PAPR
        papr_agg = (
            Reading.objects
            .filter(branch=branch, post_datetime__gte=start_dt, post_datetime__lte=end_dt)
            .aggregate(peak=Max("total_kw"), avg=Avg("total_kw"))
        )
        peak_kw = papr_agg.get("peak") or 0
        avg_kw = papr_agg.get("avg") or 0
        papr_val = round(peak_kw / avg_kw, 2) if avg_kw else 0

        # 7. Placeholders for diesel/utility usage accuracy, fuel_efficiency
        diesel_usage_accuracy = 0.0
        utility_usage_accuracy = 0.0
        fuel_efficiency = 0.0

        # 8. Generator size efficiencies (top 3)
        gen_usages = []
        for gd in gens:
            try:
                eff_data = gd.get_gen_efficiency(end_dt)
                usage_val = eff_data.get("usage", 0)
                if isinstance(usage_val, str):
                    try:
                        usage_val = float(usage_val)
                    except:
                        usage_val = 0
                gen_usages.append(usage_val or 0)
            except Exception:
                gen_usages.append(0)
            if len(gen_usages) >= 3:
                break
        while len(gen_usages) < 3:
            gen_usages.append(0)

        return {
            "baseline_energy_used": baseline_energy_used,
            "blended_cost_of_energy": blended_cost_of_energy,
            "diesel_usage_accuracy": diesel_usage_accuracy,
            "utility_usage_accuracy": utility_usage_accuracy,
            "deviation_hours": deviation_hours,
            "papr": papr_val,
            "fuel_efficiency": fuel_efficiency,
            "generator_size_efficiency_1": gen_usages[0],
            "generator_size_efficiency_2": gen_usages[1],
            "generator_size_efficiency_3": gen_usages[2],
        }

    
class Region(models.Model):
    region          = models.CharField(max_length=200)
    client          = models.ForeignKey(Client, on_delete=models.CASCADE)
    created_at      = models.DateTimeField(auto_now_add=True)
    last_updated    = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = (('region', 'client'),)

    def __str__(self):
        return f"{self.region}"