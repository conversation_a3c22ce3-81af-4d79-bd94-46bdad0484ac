from json import load
from typing import overload
from main.models import Alert_Setting
from django.core.management.base import BaseCommand
import unittest
from main.views import time_helpers
from main.models import Device
from main.models import *

class Command(BaseCommand):
    help = """
    If you need Arguments, please check other modules in 
    django/core/management/commands.
    """

    def handle(self, **options):
        suite = unittest.TestLoader().loadTestsFromTestCase(TestChronology)
        unittest.TextTestRunner().run(suite)


class TestChronology(unittest.TestCase):
     
    def setUp(self):
        from main.models import Device
        import datetime
        print ("Write your pre-test prerequisites here=-=========")
        
        # devices = Device.objects.all()
        # for device in devices:
        #     print(f"{device.id} {device.name} ")

        # self.deviceid = int(input("Please set device id : > "))

        self.branch = Branch.objects.all().last() 
        
        self.branch   
        

#     # def test_equality(self):
#     #     """
#     #     Tests that 1 + 1 always equals 2.
#     #     """
#     #     from main.models import Device
        
#     #     device = Device.objects.all()[0]
        
#     #     start_date = "01-01-2021 00:00"
#     #     end_date = "01-03-2021 00:00"

#     #     start_date = time_helpers.convert_date(start_date)
#     #     end_date = time_helpers.convert_date(end_date)
        
#     #     # print(device.get_billing_data_for_mailing( start_date, end_date))
#     #     print(device.get_date_of_min_max_comsumption( start_date, end_date))
    
    # def test_get_kwh(self):
    #     """
    #     Method get_kwh_for_period in of Devices
    #     """
    #     from main.models import Device, Branch
        
    #     # device = Device.objects.all()[0]
        
    #     start_date = "2021-03-01"
    #     end_date = "2021-03-30"

    #     data = device.get_agg_kwh_for_period("2021-03-01", "2021-03-20")
        
    #     print(data)

    # def test_get_tou(self):
        
    #     from main.models import Branch

    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch
        
    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)

    #     data = branch.get_hours_of_use(start_date, end_date)
        
    #     print(data)
    
    # def test_baseline(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[3]
    #     end_date = datetime.datetime.now()
        
    #     data = device.threaded_baseline(end_date)
        
    #     print(data)
    
    # def test_papr(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[3]
    #     end_date = datetime.datetime.now()
        
    #     data = device.get_papr(end_date)
        
    #     print(data)
    
    # def test_co2_score(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[3]
    #     end_date = datetime.datetime.now()
        
    #     data = device.get_carbon_emmisions_score_cards(end_date)
        
    #     print(data)
 
    # def test_co2_score(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-05-2021 00:00"
    #     end_date = "31-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     # data = device.branch.get_hours_of_use(start_date, end_date)
        
    # #     print(data)
        
    #     data = device.get_change_over_lags(start_date, end_date)
        
    #     print(data)
   
    # def test_capacity_factor(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[1]
    #     end_date = datetime.datetime.now()
        
    #     data = device.get_capacity_factor(end_date)
        
    #     print(data)
    
    # def test_get_fuel_consumption(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[1]
    #     end_date = datetime.datetime.now()
        
    #     data = device.get_fuel_consumption(end_date, 50)
        
    #     print(data)
    
    
    # def test_get_fuel_consumption_score_cards(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[1]
    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_fuel_consumption_score_cards(start_date, end_date)
        
    #     print(data)
    

    # def test_change_over_lags(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[1]
    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_change_over_lags(start_date, end_date)
        
    #     print(data)
    
    # def test_operating_time(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[1]
    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_operating_time(start_date, end_date)
        
    #     print(data)
    
    # def test_operating_time(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.all()[3]
    #     print(device.name)
    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_base_line(end_date)
        
    #     print(data)
    
    # def test_get_base_line(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_base_line(end_date)
        
    #     print(data)
    
    # def test_get_hours_of_use(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.branch.get_hours_of_use(start_date, end_date)
        
    #     print(data)
    
    # def test_get_time_of_use(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.branch.get_time_of_use(start_date, end_date)
        
    #     # print(data)

    # def test_get_cost_of_energy(self):
        
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-05-2021 00:00"
    #     end_date = "30-05-2021 00:00"

    #     # start_date = "2021-03-01"
    #     # end_date = "2021-03-30"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_cost_of_energy(start_date, end_date)
        
    #     print(data)

    # def test_get_cost_of_energy(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     print(branch.has_generator)
    
    # def test_get_cost_of_energy(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        

    #     print("This is the cost here..: ")
    #     costs = branch.get_cost_of_per_kwh(start_date, end_date)
    #     print(costs)

    # def test_power_factor_issues(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)

    #     device.get_power_factor_issues(start_date = start_date, end_date = end_date, min = 0.98)

    # def test_check_alerts(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        

    #     Alert_Setting.check_alerts()

    # def test_solar_hours(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-08-2021 05:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        

    #     data = device.get_solar_hours_consumption(start_date, end_date)
    #     print(data)

    # def test_solar_hours(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-08-2021 05:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     diesel_data = branch.get_diesel_overview()     
    #     # utility_data  = branch.get_utility_overview() 
    #     # print(diesel_data)

    # def test_usage_vs_baseline(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = branch.get_forcast_vs_usage(end_date)   
    #     print(data)

    # def test_usage_vs_baseline(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = device.get_base_line( end_date)   
    #     print(data)

    # def test_periodic_energy(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = branch.get_periodic_energy(start_date, end_date, "daily")   
    #     print(data)


    #     # utility_data  = branch.get_utility_overview() 
    #     # print(diesel_data)

    # def test_branch_gen_efficiency(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = branch.gen_efficiency( end_date)   
    #     print(data)

    # def test_get_current_balance_issues(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     data = branch.get_current_balance_issues( start_date, end_date)   
    #     print(data)


    #     # utility_data  = branch.get_utility_overview() 
    #     # print(diesel_data)

    # def test_branch_total_energy(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     diesel_data = branch.get_total_energy(start_date, end_date)   
    #     print(diesel_data)

    # def test_branch_fuel_consumption(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     fuel_consumption = branch.get_fuel_consumption(start_date, end_date)   
    #     print(fuel_consumption)
    
    # def test_branch_baseline(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     baseline = branch.get_forcast_usage(start_date, end_date)   
    #     print(baseline)
    
    # def test_branch_power_demand_aggregate(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     power_demand_stats = branch.demand_stats(start_date, end_date)   
    #     print(power_demand_stats)

    #     # utility_data  = branch.get_utility_overview() 
    #     # print(diesel_data)
 
    # def test_branch_report_data(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-07-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     power_demand_stats = branch.get_report_data(end_date, "week")   
    #     print(power_demand_stats)

    # def test_energy_cost(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-10-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     energy_cost = branch.energy_cost(start_date, end_date)   
    #     print(energy_cost)
  
    # def test_demand_stats(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)
    #     branch = device.branch

    #     start_date = "01-10-2021 00:00"
    #     end_date = "01-11-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     energy_cost = branch.energy_stats(start_date, end_date)   
    #     print(energy_cost)

        # utility_data  = branch.get_utility_overview() 
        # print(diesel_data)

    # def test_get_load_balance_issues(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-07-2021 00:00"
    #     end_date   = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     load_balance_issues = device.get_load_balance_issues(start_date = start_date, end_date = end_date)
        
    #     self.assertIsInstance(load_balance_issues,dict)

    # def test_get_frequency_variation(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-07-2021 00:00"
    #     end_date   = "01-08-2021 00:00"
    #     baseline   = 50
    #     precision  = 0.1

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)
        
    #     frequency_issues = device.get_frequency_variations(start_date = start_date, end_date = end_date, base_frequency=baseline, precision = precision)

    #     print(frequency_issues)

        
    #     self.assertIsInstance(frequency_issues,dict)

    # def test_get_frequency_variation(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-07-2021 00:00"
    #     end_date   = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)

    #     max_voltage = 235
    #     min_voltage = 220
        
    #     voltage_issues = device.get_voltage_issues(start_date = start_date, end_date = end_date, max_voltage=max_voltage, min_voltage = min_voltage)

    #     print(voltage_issues)
        
    #     self.assertIsInstance(voltage_issues,dict)

        # def test_get_frequency_variation(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-07-2021 00:00"
    #     end_date   = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)

    #     max_voltage = 235
    #     min_voltage = 220
        
    #     voltage_issues = device.get_voltage_issues(start_date = start_date, end_date = end_date, max_voltage=max_voltage, min_voltage = min_voltage)

    #     print(voltage_issues)
        
    #     self.assertIsInstance(voltage_issues,dict)


    # def test_overload(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-07-2021 00:00"
    #     end_date   = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)

    #     max_kw = 2
        
    #     overload = device.check_overload(start_date = start_date, end_date = end_date, load_threshold = max_kw)

    #     print(overload)
        
    #     self.assertIsInstance(overload,dict)

    def test_subscription(self):
    
    #     from main.models import Device
    #     import datetime
        
    #     device = Device.objects.get(pk = self.deviceid)

    #     start_date = "01-07-2021 00:00"
    #     end_date   = "01-08-2021 00:00"

    #     start_date = time_helpers.convert_date(start_date)
    #     end_date = time_helpers.convert_date(end_date)

    #     max_kw = 2
        
    #     overload = device.check_overload(start_date = start_date, end_date = end_date, load_threshold = max_kw)

    #     print(overload)
        
    #     self.assertIsInstance(overload,dict)

    