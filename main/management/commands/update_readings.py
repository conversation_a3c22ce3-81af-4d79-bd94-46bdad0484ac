from django.core.management.base import BaseCommand
from main.models import Reading
# from main.helpers.fetch_readings import run_migrations



class Command(BaseCommand):
    help = 'Displays current time'

    def handle(self, *args, **kwargs):
        try:
            # for i in range(20):
            Reading().update()
                 
            self.stdout.write("Update Readings Successfull")

        except SyntaxError:
            
            self.stdout.write("Update Readings Failed")              
      