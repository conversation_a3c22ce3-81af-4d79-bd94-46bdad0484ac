from django.core.management.base import BaseCommand
from main.models import Reading, Device
# from main.helpers.fetch_readings import run_migrations



class Command(BaseCommand):
    help = 'checks for active devices that stopped posting for atleast 1hr.'

    def handle(self, *args, **kwargs):
        try:
            response = Device().check_non_posting_members()

            self.stdout.write(response.get("message"))

        except SyntaxError:

            self.stdout.write("Checking Failed")
      