from django.core.management.base import BaseCommand
from main.models import *
# from main.helpers.fetch_readings import run_migrations



class Command(BaseCommand):
    help = 'Displays current time'

    def handle(self, *args, **kwargs):
        for device in Device.objects.all():
            if device.provider == "ACCRELL":
                continue
            else:
                device.update_last_readings()
        try:

            # for i in range(30):

                 
            self.stdout.write("Update Datalogs Successfull")

        except SyntaxError:
            
            self.stdout.write("Update Datalogs Failed")              
      


# def update_historic_scores(request):

#         devices = Device.objects.all()                        

#         for device in devices:
#                 device.populate_previous_scores()
