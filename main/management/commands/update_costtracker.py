from django.core.management.base import BaseCommand
from main.models import Branch
import json
import datetime
# from main.helpers.fetch_readings import run_migrations



class Command(BaseCommand):
    help = 'Displays current time'

    def handle(self, *args, **kwargs):
        try:
            for branch in Branch.objects.all():

                print("------------------------------------")
                print("------------------------------------")
                print("------------------------------------")
                print("------------------------------------")
                print(f"--------{branch.name}---------")
                print("------------------------------------")
                print("------------------------------------")
                print("------------------------------------")
                print("------------------------------------")

                diesel_data = branch.get_diesel_overview()
                utility_data  = branch.get_utility_overview()
                ipp_data = branch.get_ipp_overview()
                branch.cost_data = json.dumps(dict(diesel=diesel_data, utility=utility_data, ipp=ipp_data))
                branch.save()

            self.stdout.write("Update Readings Successfull")

        except :

            self.stdout.write("Update Readings Failed")
