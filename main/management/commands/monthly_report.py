import datetime
from django.core.management.base import BaseCommand
from django.urls import reverse
from main.models import KW_TO_KVA_MULTIPLIE<PERSON>, <PERSON>, <PERSON>_<PERSON>, Branch, Device, ViewPermission
from account.models import Client, User
from main.scripts import time_helpers, mailgun
from django.template.loader import render_to_string
from wyre import settings


class Command(BaseCommand):
    help = 'Sends Monthly Reports to Branch Emails'

    def handle(self, *args, **kwargs):
        client = Client.objects.get(id=15)
        branches = Branch.objects.filter(client=client)
        
        start_date, end_date = time_helpers.get_previous_month_start_and_end_dates_from_today()

        # start_date month and year in string formart:
        start_date_month = start_date.strftime("%B")
        start_date_year = start_date.strftime("%Y")

        title = f"Wyre-{start_date_month}-{start_date_year}-Energy-Report"

        for branch in branches:

            devices = Device.objects.filter(branch=branch)
            base_line = int(branch.get_branch_baseline(end_date))
            base_line = '{:,}'.format(base_line)

            total_energy_consumed = 0
            branch_efficiency = []

            max_values = []
            avg_values = []

            data = ""
            text = f"Here is your Energy report for {start_date_month}-{start_date_year}. You can login your account for more details"

            for device in devices:

                device_energy_consumed = int(device.get_total_kwh_for_period(start_date, end_date))
                hours = str(time_helpers.decimal_to_hours_minutes(round(device.get_hours_of_use(start_date, end_date), 3))[0])
                minutes = str(time_helpers.decimal_to_hours_minutes(round(device.get_hours_of_use(start_date, end_date), 3))[1])

                if device.is_gen:
                    efficiency = f"{str(round((device.get_gen_efficiency(end_date).get('max_load')/device.gen_size)*100))} %"
                    branch_efficiency.append(efficiency)
                    src = "https://www.backend.wyreng.com/static/images/monthly-report/image-10.png"
                    efficiency_field = 'Generator Efficiency'
                else:
                    src = "https://www.backend.wyreng.com/static/images/monthly-report/image-3.png"
                    efficiency = '-'
                    efficiency_field = '-'

                agg_data = device.get_agg_kwh_for_period(start_date, end_date)
                max_values.append(int(agg_data.get("max")/KW_TO_KVA_MULTIPLIER))
                avg_values.append(int(agg_data.get("avg")/KW_TO_KVA_MULTIPLIER))

                total_energy_consumed += device_energy_consumed

                device_energy_consumed = '{:,}'.format(device_energy_consumed)
                device_energy_consumed = str(device_energy_consumed)

                data += """
                <!-- Gen-1 -->
                <div class="u-col u-col-25" style="font-family: 'Montserrat'; max-width: 100%;min-width: 150px;display: table-cell;vertical-align: top; flex: 1 1 0;">
                <div
                    style="font-family: 'Montserrat'; background-color: #f5f4f4;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                    <!--[if (!mso)&(!IE)]><!-->
                    <div
                    style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 10px solid #ffffff;border-left: 5px solid #ffffff;border-right: 5px solid #ffffff;border-bottom: 10px solid #ffffff;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                    <!--<![endif]-->
            
                    <table id="u_content_image_12" style="font-family:'Montserrat';" role="presentation" cellpadding="0"
                        cellspacing="0" width="100%" border="0">
                        <tbody>
                        <tr>
                            <td class="v-container-padding-padding"
                            style="overflow-wrap:break-word;word-break:break-word;padding:6px;font-family:'Montserrat';"
                            align="left">
            
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                <td style="padding-right: 0px;padding-left: 0px;" align="center">
            
                                    <img align="center" border="0" src="""+src+""" alt="" title=""
                                    style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 26%;max-width: 35.88px;"
                                    width="35.88" class="v-src-width v-src-max-width" />
            
                                </td>
                                </tr>
                            </table>
            
                            </td>
                        </tr>
                        </tbody>
                    </table>
            
                    <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0"
                        width="100%" border="0">
                        <tbody>
                        <tr>
                            <td class="v-container-padding-padding"
                            style="overflow-wrap:break-word;word-break:break-word;padding:1px;font-family:'Montserrat';"
                            align="left">
            
                            <div class="v-font-size"
                                style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; text-align: center; word-wrap: break-word;">
                                <p style="line-height: 140%;">"""+device.name+"""</p>
                                <div class="v-font-size"
                                style="font-family: 'Montserrat' text-align: center; word-wrap: break-word;">
                                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%;">Total Energy</p>
                                <p style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%;">"""+device_energy_consumed+""" kWh</p>
                                </div>
                            </div>
            
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0"
                        width="100%" border="0">
                        <tbody>
                        <tr>
                            <td class="v-container-padding-padding"
                            style="overflow-wrap:break-word;word-break:break-word;padding:4px;font-family:'Montserrat';"
                            align="left">
            
                            <div class="v-font-size"
                                style="font-family: 'Montserrat' text-align: center; word-wrap: break-word;">
                                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; text-align: center;">Time of Use</p>
                                <p style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%; text-align: center;">"""+hours+""" Hrs """+minutes+""" Min</p>
                            </div>
            
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0"
                        width="100%" border="0">
                        <tbody>
                        <tr>
                            <td class="v-container-padding-padding"
                            style="overflow-wrap:break-word;word-break:break-word;padding:4px;font-family:'Montserrat'
                            ;"
                            align="left">
            
                            <div class="v-font-size"
                                style="font-family: 'Montserrat'; font-size: 16px; line-height: 140%; text-align: center; word-wrap: break-word;">
                                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%;">"""+efficiency_field+"""</p>
                                <p style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%;">"""+efficiency+"""</p>
                            </div>
            
                            </td>
                        </tr>
                        </tbody>
                    </table>
            
                    <!--[if (!mso)&(!IE)]><!-->
                    </div><!--<![endif]-->
                </div>
                </div>
                """

            try:
                if len(max_values) > 0:
                    peak_demand = str(max(max_values))
                else:
                    peak_demand = '-'
                
                if len(avg_values) > 0:
                    average_demand = str(max(avg_values))
                else:
                    average_demand = '-'
                
                if len(branch_efficiency) > 0:
                    branch_efficiency = str(min(branch_efficiency))
                else:
                    branch_efficiency = '-'
                    
            except (ValueError, TypeError) as e:
                peak_demand = '-'
                average_demand = '-'
                branch_efficiency = '-'

            total_energy_consumed = round(total_energy_consumed, settings.DECIMAL_PLACES)
            total_energy_consumed = '{:,}'.format(total_energy_consumed)
            total_energy_consumed = str(total_energy_consumed)
            base_line = str(base_line)

            html_message = """
            <!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
            <head>
            <!--[if gte mso 9]>
            <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
            </xml>
            <![endif]-->
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="x-apple-disable-message-reformatting">
            <!--[if !mso]><!--><meta http-equiv="X-UA-Compatible" content="IE=edge"><!--<![endif]-->
            <title></title>
            
                <style type="text/css">
                @media only screen and (min-width: 620px) {
            .u-row {
                width: 600px !important;
            }
            .u-row .u-col {
                vertical-align: top;
            }

            .u-row .u-col-25 {
                width: 150px !important;
            }

            .u-row .u-col-33p33 {
                width: 199.98px !important;
            }

            .u-row .u-col-50 {
                width: 300px !important;
            }

            .u-row .u-col-100 {
                width: 600px !important;
            }

            }

            @media (max-width: 620px) {
            .u-row-container {
                max-width: 100% !important;
                padding-left: 0px !important;
                padding-right: 0px !important;
            }
            .u-row .u-col {
                min-width: 320px !important;
                max-width: 100% !important;
                display: block !important;
            }
            .u-row {
                width: 100% !important;
            }
            .u-col {
                width: 100% !important;
            }
            .u-col > div {
                margin: 0 auto;
            }
            }
            body {
            margin: 0;
            padding: 0;
            }

            table,
            tr,
            td {
            vertical-align: top;
            border-collapse: collapse;
            }

            p {
            margin: 0;
            }

            .ie-container table,
            .mso-container table {
            table-layout: fixed;
            }

            * {
            line-height: inherit;
            }

            a[x-apple-data-detectors='true'] {
            color: inherit !important;
            text-decoration: none !important;
            }

            table, td { color: #000000; } #u_body a { color: #0000ee; text-decoration: underline; } #u_content_text_deprecated_1 a { color: #8744e2; text-decoration: none; } @media (max-width: 480px) { #u_content_heading_2 .v-container-padding-padding { padding: 20px 10px !important; } #u_content_heading_2 .v-font-size { font-size: 18px !important; } #u_content_image_12 .v-container-padding-padding { padding: 6px !important; } #u_content_image_12 .v-src-width { width: auto !important; } #u_content_image_12 .v-src-max-width { max-width: 16% !important; } #u_content_image_13 .v-src-width { width: auto !important; } #u_content_image_13 .v-src-max-width { max-width: 16% !important; } #u_content_image_14 .v-src-width { width: auto !important; } #u_content_image_14 .v-src-max-width { max-width: 16% !important; } #u_content_image_15 .v-src-width { width: auto !important; } #u_content_image_15 .v-src-max-width { max-width: 16% !important; } #u_content_image_18 .v-src-width { width: auto !important; } #u_content_image_18 .v-src-max-width { max-width: 16% !important; } #u_content_image_19 .v-src-width { width: auto !important; } #u_content_image_19 .v-src-max-width { max-width: 16% !important; } #u_content_image_20 .v-src-width { width: auto !important; } #u_content_image_20 .v-src-max-width { max-width: 16% !important; } #u_content_image_21 .v-src-width { width: auto !important; } #u_content_image_21 .v-src-max-width { max-width: 17% !important; } #u_content_image_22 .v-src-width { width: auto !important; } #u_content_image_22 .v-src-max-width { max-width: 17% !important; } #u_content_button_1 .v-container-padding-padding { padding: 10px !important; } #u_content_button_1 .v-size-width { width: auto !important; } #u_content_button_1 .v-padding { padding: 2px 20px !important; } #u_content_text_deprecated_1 .v-container-padding-padding { padding: 10px !important; } }
                </style>
            
            

            <!--[if !mso]><!--><link href="https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap" rel="stylesheet" type="text/css"><link href="https://fonts.googleapis.com/css?family=Raleway:400,700&display=swap" rel="stylesheet" type="text/css"><!--<![endif]-->

            </head>

            <body class="clean-body u_body" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #ffffff;color: #000000">
            <!--[if IE]><div class="ie-container"><![endif]-->
            <!--[if mso]><div class="mso-container"><![endif]-->
            <table id="u_body" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #ffffff;width:100%" cellpadding="0" cellspacing="0">
            <tbody>
            <tr style="vertical-align: top">
                <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #ffffff;"><![endif]-->
                

            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat';" align="left">
                    
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td style="padding-right: 0px;padding-left: 0px;" align="center">
                
                <img align="center" border="0" src="https://www.backend.wyreng.com/static/images/monthly-report/image-12.jpeg" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;" width="600" class="v-src-width v-src-max-width"/>
                
                </td>
            </tr>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>



            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #ffffff;width: 600px;padding: 3px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
            <div style="background-color: #ffffff;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 3px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:20px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; text-align: left; word-wrap: break-word;">
                <p style="line-height: 140%;">Hello """+branch.name+""",</p>
            <p style="line-height: 140%;"> </p>
            <p style="line-height: 140%;">"""+text+""".</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table id="u_content_heading_2" style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:13px;font-family:'Montserrat';" align="left">
                    
            <h1 class="v-font-size" style="margin: 0px; line-height: 140%; text-align: center; word-wrap: break-word; font-family: 'Montserrat'; font-size: 22px; font-weight: 400;"><strong>Check out the highlights from your location</strong></h1>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>



            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #5c3592;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #5c3592;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #5c3592;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
            <div style="background-color: #5c3592;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 1px 1px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-size: 18px; font-weight: 400; color: #ffffff; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; font-weight: 400;">Total Energy</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px 0px 10px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%; color: #ffffff;">"""+total_energy_consumed+""" kWh</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>


            <!-- 1st Grid -->
            <div class="device_display">
            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
                <div class="u-row"
                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                    <!--[if (mso)|(IE)]><td align="center" width="140" style="background-color: #f5f4f4;width: 140px;padding: 0px;border-top: 10px solid #ffffff;border-left: 5px solid #ffffff;border-right: 5px solid #ffffff;border-bottom: 10px solid #ffffff;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                    
                    <!-- DEVICES-SECTION -->
                    <div class="devices_section" style="display: table-header-group;">
                    """ + data + """
                    </div>
                    
                    <!--[if (mso)|(IE)]></td><![endif]-->
                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
                </div>
            </div>
            </div>



            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px 10px 10px;font-family:'Montserrat';" align="left">
                    
            <table height="0px" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <tbody>
                <tr style="vertical-align: top">
                    <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <span>&#160;</span>
                    </td>
                </tr>
                </tbody>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>





            <!-- 2nd Grid -->
            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2; ">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0" top: 20px;>
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 10px 5px;font-family:'Montserrat';" align="left">
                    
            <table height="0px" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top; -ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <tbody>
                <tr style="vertical-align: top">
                    <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <span>&#160;</span>
                    </td>
                </tr>
                </tbody>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>



            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2;">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #f2f2f2;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #f2f2f2;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="290" style="width: 290px;padding: 0px;border-top: 5px solid #ffffff;border-left: 5px solid #ffffff;border-right: 5px solid #ffffff;border-bottom: 5px solid #ffffff;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-50" style="max-width: 320px;min-width: 300px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 5px solid #ffffff;border-left: 5px solid #ffffff;border-right: 5px solid #ffffff;border-bottom: 5px solid #ffffff;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table id="u_content_image_21" style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:6px;font-family:'Montserrat';" align="left">
                    
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td style="padding-right: 0px;padding-left: 0px;" align="center">
                
                <img align="center" border="0" src="https://www.backend.wyreng.com/static/images/monthly-report/image-4.png" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 18%;max-width: 51.84px;" width="51.84" class="v-src-width v-src-max-width"/>
                
                </td>
            </tr>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:1px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%;">Average Demand</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:4px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="line-height: 140%;">"""+average_demand+""" kVA</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="290" style="width: 290px;padding: 0px;border-top: 5px solid #ffffff;border-left: 5px solid #ffffff;border-right: 5px solid #ffffff;border-bottom: 5px solid #ffffff;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-50" style="max-width: 320px;min-width: 300px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 5px solid #ffffff;border-left: 5px solid #ffffff;border-right: 5px solid #ffffff;border-bottom: 5px solid #ffffff;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table id="u_content_image_22" style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:6px;font-family:'Montserrat';" align="left">
                    
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td style="padding-right: 0px;padding-left: 0px;" align="center">
                
                <img align="center" border="0" src="https://www.backend.wyreng.com/static/images/monthly-report/image-7.png" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 18%;max-width: 51.84px;" width="51.84" class="v-src-width v-src-max-width"/>
                
                </td>
            </tr>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:1px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%;">Peak Demand</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:4px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="line-height: 140%;">"""+peak_demand+""" kVA</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>


            <!-- 3rd Grid -->
            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
            </div>
            <div class="u-col u-col-33p33" style="max-width: 320px;min-width: 200px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px; margin-left: 163px; margin-bottom: 15px; padding: 0px; background-color: #f2f2f2; border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding:0px 45px 0px 45px; border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table id="u_content_image_19" style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:8px 6px 6px;font-family:'Montserrat';" align="left">
                    
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td style="padding-right: 0px;padding-left: 0px;" align="center">
                
                <img align="center" border="0" src="https://www.backend.wyreng.com/static/images/monthly-report/image-6.png" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 24%;max-width: 45.12px;" width="45.12" class="v-src-width v-src-max-width"/>
                
                </td>
            </tr>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:3px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="font-family: 'Montserrat'; font-size: 14px; line-height: 140%;">Forecasted Consumption</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:4px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%; text-align: center; word-wrap: break-word;">
                <p style="font-family: 'Montserrat'; font-size: 16px; font-weight: 700; line-height: 140%;">"""+base_line+"""kWh</p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!-- End of 3rd Grid -->


            <div class="u-row-container" style="padding: 0px;background-color: #f2f2f2">
            <div class="u-row" style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                <div style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: #f2f2f2;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->
                
            <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
            <div class="u-col u-col-100" style="max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;">
            <div style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
            <!--[if (!mso)&(!IE)]><!--><div style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;"><!--<![endif]-->
            
            <table id="u_content_button_1" style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat';" align="left">
                    
            <!--[if mso]><style>.v-button {background: transparent !important;}</style><![endif]-->
            <div align="center">
            <!--[if mso]><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="https://www.unlayer.com" style="height:33px; v-text-anchor:middle; width:336px;" arcsize="12%"  stroke="f" fillcolor="#f9cf40"><w:anchorlock/><center style="color:#ffffff;font-family:'Raleway',sans-serif;"><![endif]-->  
                <a href="https://dashboard.wyreng.com" target="_blank" class="v-button v-size-width v-font-size" style="box-sizing: border-box;display: inline-block;font-family:'Montserrat';text-decoration: none;-webkit-text-size-adjust: none;text-align: center;color: #ffffff; background-color: #f9cf40; border-radius: 4px;-webkit-border-radius: 4px; -moz-border-radius: 4px; width:58%; max-width:100%; overflow-wrap: break-word; word-break: break-word; word-wrap:break-word; mso-border-alt: none;font-size: 14px;">
                <span class="v-padding" style="display:block;padding:2px 20px;line-height:210%;"><span style="font-family: 'Montserrat'; line-height: 29.4px; font-size: 14px;"><strong><span style="line-height: 29.4px;">See the updates in your account!</span></strong></span></span>
                </a>
            <!--[if mso]></center></v:roundrect><![endif]-->
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table id="u_content_text_deprecated_1" style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px 80px;font-family:'Montserrat';" align="left">
                    
            <div class="v-font-size" style="font-size: 14px; line-height: 160%; text-align: center; word-wrap: break-word;">
                <p style="font-size: 14px; line-height: 160%;">If you have any questions, please email us at <a rel="noopener" href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:8px 0px;font-family:'Montserrat';" align="left">
                    
            <table height="0px" align="center" border="0" cellpadding="0" cellspacing="0" width="96%" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                <tbody>
                <tr style="vertical-align: top">
                    <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                    <span>&#160;</span>
                    </td>
                </tr>
                </tbody>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat';" align="left">
                    
            <div align="center">
            <div style="display: table; max-width:149px;">
            <!--[if (mso)|(IE)]><table width="149" cellpadding="0" cellspacing="0" border="0"><tr><td style="border-collapse:collapse;" align="center"><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse:collapse; mso-table-lspace: 0pt;mso-table-rspace: 0pt; width:149px;"><tr><![endif]-->
            
                
                <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 18px;" valign="top"><![endif]-->
                <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 18px">
                <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                    <a href="https://www.linkedin.com/company/wyre-energy/" title="LinkedIn" target="_blank">
                    <img src="https://www.backend.wyreng.com/static/images/monthly-report/image-2.png" alt="LinkedIn" title="LinkedIn" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                    </a>
                </td></tr>
                </tbody></table>
                <!--[if (mso)|(IE)]></td><![endif]-->
                
                <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 18px;" valign="top"><![endif]-->
                <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 18px">
                <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                    <a href="https://www.instagram.com/wyre_app/" title="Instagram" target="_blank">
                    <img src="https://www.backend.wyreng.com/static/images/monthly-report/image-1.png" alt="Instagram" title="Instagram" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                    </a>
                </td></tr>
                </tbody></table>
                <!--[if (mso)|(IE)]></td><![endif]-->
                
                <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 0px;" valign="top"><![endif]-->
                <table align="left" border="0" cellspacing="0" cellpadding="0" width="32" height="32" style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 0px">
                <tbody><tr style="vertical-align: top"><td align="left" valign="middle" style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                    <a href="https://www.wyreng.com/" title="GooglePlus" target="_blank">
                    <img src="https://www.backend.wyreng.com/static/images/monthly-report/image-9.png" alt="GooglePlus" title="GooglePlus" width="32" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                    </a>
                </td></tr>
                </tbody></table>
                <!--[if (mso)|(IE)]></td><![endif]-->
                
                
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
            </div>
            </div>

                </td>
                </tr>
            </tbody>
            </table>

            <table style="font-family:'Montserrat';" role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
            <tbody>
                <tr>
                <td class="v-container-padding-padding" style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat';" align="left">
                    
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td style="padding-right: 0px;padding-left: 0px;" align="center">
                
                <img align="center" border="0" src="https://www.backend.wyreng.com/static/images/monthly-report/image-11.gif" alt="" title="" style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;" width="600" class="v-src-width v-src-max-width"/>
                
                </td>
            </tr>
            </table>

                </td>
                </tr>
            </tbody>
            </table>

            <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->
            </div>
            </div>
            <!--[if (mso)|(IE)]></td><![endif]-->
                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                </div>
            </div>
            </div>


                <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
            </table>
            <!--[if mso]></div><![endif]-->
            <!--[if IE]></div><![endif]-->
            </body>

            </html>

            
            """
            
            receievers = [branch.email]
            bcc = ['<EMAIL>', '<EMAIL>']

            response = mailgun.Mailer.send_monthly_report(2, title, html_message, receievers, bcc)
            if response["status"] == True:
                self.stdout.write(self.style.SUCCESS(f"Report sent successfully to {branch.email}."))
            else:
                self.stdout.write(self.style.ERROR("Failed to send Report."))

