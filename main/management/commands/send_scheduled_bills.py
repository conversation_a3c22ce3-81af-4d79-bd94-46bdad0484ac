from django.core.management.base import BaseCommand
from main.models import Bill, Bill_Mails
# from main.helpers.fetch_readings import run_migrations



class Command(BaseCommand):
    help = 'Displays current time'

    def handle(self, *args, **kwargs):
        for receiver in Bill_Mails.objects.all():
            try:
                response = receiver.auto_send_reports()
                print(response)
                 
            except SyntaxError:
                
                self.stdout.write(f"{receiver} mailing failed.!")     

        self.stdout.write("Update Readings Successfull")

      