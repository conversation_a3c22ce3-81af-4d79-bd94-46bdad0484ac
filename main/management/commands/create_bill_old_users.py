from django.core.management.base import BaseCommand
from main.models import <PERSON>, User, Bill_Mails
# from main.helpers.fetch_readings import run_migrations



class Command(BaseCommand):
    help = 'Displays current time'

    def handle(self, *args, **kwargs):
        try:
            all_users = User.objects.all()

            for user in all_users:
                if Bill_Mails.objects.filter(user = user): continue # skip if user already has bill mail

                Bill_Mails.objects.create(user = user, email = user.email, client = user.client)
                 
            self.stdout.write("Create Bill Mails For all Users Successfull")

        except SyntaxError:
            
            self.stdout.write("Create Bill Mails For all Users Failed")              
      