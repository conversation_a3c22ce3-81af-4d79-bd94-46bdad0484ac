from django.core.management.base import BaseCommand
from main.models import *


class Command(BaseCommand):
    help = 'alerts customers of load inbalance'

    def handle(self, *args, **kwargs):
        branches = Branch.objects.all()

        try:

            for branch in branches:

                branch.cache_diesel_entry_overview_history()                           

            self.stdout.write("Successfull")

        except Exception as e:
            
            print(e)