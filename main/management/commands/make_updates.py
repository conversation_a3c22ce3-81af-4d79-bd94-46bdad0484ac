from django.core.management.base import BaseCommand
from django.utils import timezone
from main.models import *
from datetime import datetime

class Command(BaseCommand):
    help = 'Displays current time'
    tarrifs = []

    def handle(self, *args, **kwargs):
        devices = Device.objects.all()
        delete_all_tariffs()
        now = datetime.now()
        device_tariffs = {'350kVA GEN': 0, 'IPP': 58, 'EKEDC RESID': 65.32, 'Main Supply': 50, 'EKEDC': 50, 'Main Incomer': 60, '85kVA GEN': 0}
        
        # print({device.name:0 for device in devices})
        for device in devices:
            for year in range(2019,2022):
                for month in range(1,13):
                    Tariff.objects.create(client = device.branch.client, 
                    branch = device.branch, 
                    device = device,
                    amount = device_tariffs[device.name],
                    start_date = f"{year}-{month}-01",
                    end_date = f"{year}-{month}-01")

                    print(year, month)
                    if year == 2021 and month == 6:
                        break

        # for device in devices:

        #     last_tarrif = device.tariff_set.all()
        #     print(last_tarrif)

        #     if last_tarrif.exists():
        #         amount = amount
        #     else:
        #         amount = 0
            
        #     # Tariff.objects.create(client = device.branch.client, 
        #     # branch = device.branch, 
        #     # device = device,
        #     # amount = 50,
        #     # start_date = f"{now.year}-{now.month}-01",
        #     # end_date = f"{now.year}-{now.month}-28")

        # self.stdout.write("Done")


def delete_all_tariffs():
    Tariff.objects.all().delete()