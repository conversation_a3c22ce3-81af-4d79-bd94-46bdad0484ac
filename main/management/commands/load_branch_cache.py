from django.core.management.base import BaseCommand
from django.utils import timezone
from main.models import *
from datetime import datetime

class Command(BaseCommand):
    help = 'Displays current time'

    def handle(self, *args, **kwargs):
        start_of_month = time_helpers.get_start_and_end_of_month_from_date(timezone.now())[0]
        end_date = timezone.now()

        for branch in Branch.objects.all():
            print(branch.name)

            branch.device_dashboard_data(start_of_month, end_date, force = True)
        

