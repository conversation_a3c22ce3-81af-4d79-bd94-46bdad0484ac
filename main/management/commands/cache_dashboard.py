from django.core.management.base import BaseCommand
from main.models import *


class Command(BaseCommand):
    help = 'alerts customers of load inbalance'

    def handle(self, *args, **kwargs):
        try:

            users = User.objects.all()

            for user in users:
                                
                user.load_cache_data()

            self.stdout.write("Update Readings Successfull")

        except SyntaxError:
            
            self.stdout.write("Update Readings Failed")              