from multiprocessing.connection import Client
from rest_framework import serializers
from .models import Al<PERSON>_Setting, Branch, Cost, Device, Equipment, Month_End_Diesel_Balance,  ViewPermission, Bill, Bill_Mails, FuelConsumption, Reading, Client, Datalog

class BranchSerializer(serializers.ModelSerializer):

    class Meta:
        model = Branch
        fields = '__all__'

class BranchLeanSerializer(serializers.ModelSerializer):

    class Meta:
        model = Branch
        fields = ['id', 'name', ]

class ClientsAllSerializer(serializers.ModelSerializer):

    class Meta:
        model = Client
        fields = ['id', 'name', 'address', 'client_type', 'address']

class FuelEntryCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = FuelConsumption
        fields = '__all__'

class ClientCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = Client
        fields = '__all__'

class DeviceSerializer(serializers.ModelSerializer):

    class Meta:
        model = Device
        fields = '__all__'

class DeviceMaintenanceSerializer(serializers.ModelSerializer):

    class Meta:
        model = Device
        fields = ['id', 'name', 'next_maintenance_date', 'gen_size']

class DeviceListSerializer(serializers.ModelSerializer):
    branch_name = serializers.CharField(source='branch.name')
    client_name = serializers.CharField(source='client.name')

    class Meta:
        model = Device
        fields = ['device_id', 'name', 'branch_name', 'client_name', 'last_posted', 'non_post_attention', 'hours_since_last_post']

class ReadingListSerializer(serializers.ModelSerializer):

    class Meta:
        model = Reading
        fields = '__all__'

class DatalogListSerializer(serializers.ModelSerializer):

    class Meta:
        model = Datalog
        fields = '__all__'

class ViewSerializer(serializers.ModelSerializer):

    class Meta:
        model = ViewPermission
        fields = '__all__'

class BillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bill
        fields = '__all__'

class Alert_SettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Alert_Setting
        fields = '__all__'

class BillMailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bill_Mails
        fields = '__all__'

class CostTrackerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = '__all__'

class EquipmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Equipment
        fields = '__all__'

class MonthEndDieselSerializer(serializers.ModelSerializer):
    class Meta:
        model = Month_End_Diesel_Balance
        fields = '__all__'

class DieselCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = ["id", "date", "quantity", "price_per_litre"]

class UtilityCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = ["id", "date", "amount", "value", "tarrif", "vat_inclusive_amount", "vat"]

class IPPCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = Cost
        fields = ["id", "date", "amount", "value", "tarrif", "vat_inclusive_amount", "vat"]



class AveragePowerFactorSerializer(serializers.Serializer):
    start_date = serializers.DateField()
    end_date   = serializers.DateField()
    device_id  = serializers.IntegerField()

class ACB_serializer(serializers.Serializer):
    meter_id = serializers.CharField()
    state   = serializers.CharField()

class ACB_List_serializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        exclude = ('last_reading', 'operating_hours_start', 'operating_hours_end', 'bill_recievers', 'type', 'fuel_type', 'is_source', 'next_maintenance_date',)