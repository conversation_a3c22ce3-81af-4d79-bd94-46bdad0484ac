from rest_framework.exceptions import APIException
from rest_framework import status
from django.contrib import auth
from django.contrib.auth import login
from rest_framework import permissions

# ADMIN PERMISSION

class IsAdmin(permissions.BasePermission):
    """
    View based permission check api key.
    """

    def has_permission(self, request, view):

        user_role = request.user.roles

        if user_role <= 2: # MUST BE ADMIN OR SUPER ADMIN

            return True

        else:

            raise NoAdminPermission()

class NoAdminPermission(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {'error': True, 'message': 'You Need to have Admin permissions'}
    default_code = 'Not permitted'

# END ADMIN PERMISSION

# SUPER ADMIN PERMISSION
class IsSuperAdmin(permissions.BasePermission):
    """
    View based permission check api key.
    """

    def has_permission(self, request, view):

        user_role = request.user.roles

        if user_role == 1: # MUST BE ADMIN OR SUPER ADMIN

            return True

        else:

            raise NoSuperAdminPermission()

class NoSuperAdminPermission(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {'error': True, 'message': 'You Need to have SuperAdmin permissions'}
    default_code = 'Not permitted'

# END SEND ADMIN PERMISSION