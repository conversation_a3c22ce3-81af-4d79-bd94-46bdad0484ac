from celery import shared_task
import datetime
import logging
from django.core.management.base import BaseCommand
from main.models import Bill, Bill_Mails, Branch, Device, MonthlyBranchMetrics, FuelConsumption, Reading, Datalog, Cost, MonthlyReport
from datetime import date, timedelta, timezone
from django.utils import timezone
from django.db.models import Avg, Max, Min, base
from django.db import IntegrityError, transaction, connections
from concurrent.futures import ProcessPoolExecutor, as_completed
from account.models import Client
from main.scripts import time_helpers, mailgun
from django.template.loader import render_to_string
import requests, random, time, json
from paho.mqtt import client as mqtt_client
from main.models import Alert_Setting
from main.models import Alert_Setting, ReportConfiguration
import calendar
from django.conf import settings
from main.scripts import time_helpers


logger = logging.getLogger(__name__)


broker = '*************'
port = 1883
# generate client ID with pub prefix randomly
client_id = f'python-mqtt-{random.randint(0, 1000)}'
username = 'wyre'
password = '********'

@shared_task
def quika_new_icash_local():
    print("RUNNING")
    return 1+2

@shared_task
def toggle_device( meter_id, state):

    def connect_mqtt():
        def on_connect(client, userdata, flags, rc):
            if rc == 0:
                print("Connected to MQTT Broker!")
            else:
                print("Failed to connect, return code %d\n", rc)

        client = mqtt_client.Client(client_id)
        client.username_pw_set(username, password)
        client.on_connect = on_connect
        client.connect(broker, port)
        return client


    def publish(client, topic, meter_id, state):
        msg_count = 0
        # time.sleep(5)
        topic = topic.format(meter_id)
        print(topic)
        result = client.publish(topic, json.dumps({
                                                    "msgid": "1",
                                                    "method": "operate",
                                                    "sn": "12307221900001",
                                                    "timestamp " : 1694658360,
                                                    "payload" : {
                                                    "addr" : "1_1",
                                                    "Switch" : str(state)
                                                    }
                                                    }))
        print(result)

    client = connect_mqtt()
    topic = "indicate/server/{}"
    publish(client, topic, meter_id, state)
    client.loop_write()

@shared_task
def paho_listen():

    broker = '*************'
    port = 1883
    receivetopic = [("indicate/server/#",0), ("data/up/#",0)]

    # generate client ID with pub prefix randomly
    client_id = f'python-mqtt-{random.randint(0, 1000)}'
    username = 'wyre'
    password = '********'

    def connect_mqtt():
        def on_connect(client, userdata, flags, rc):
            if rc == 0:
                print("Connected to MQTT Broker!")
            else:
                print("Failed to connect, return code %d\n", rc)

        client = mqtt_client.Client(client_id)
        client.username_pw_set(username, password)
        client.on_connect = on_connect
        client.connect(broker, port)
        return client

    def subscribe(client: mqtt_client):
        def on_message(client, userdata, msg):
            print(f"Received `{msg.payload.decode()}` from `{msg.topic}` topic")
            response = json.loads(msg.payload.decode())

            device_id = response.get("sn", "0")
            d1 = response.get("reported", {"NONE", "NONE"})
            d2 = d1.get("1_1", {"NONE", "NONE"})
            switch_state = d2.get("RlySta", "No switch")
            print(switch_state)

            if switch_state == "No switch":
                pass
            else:
                device_qs = Device.objects.filter(device_id = device_id)

                if device_qs.exists():
                    device = device_qs.last()
                    device.switch_state = "ON" if switch_state == "1" else "OFF"
                    print(device.switch_state, device)
                    device.save()

        client.subscribe(receivetopic)
        client.on_message = on_message

    def run():
        client = connect_mqtt()
        subscribe(client)
        client.loop_forever()

    run()


@shared_task
def trigger_otd_via_view():
    response = requests.get("https://backend.wyreng.com/api/v1/push_otd/")
    return response.content


@shared_task
def check_for_alertable():

    # Alert_Setting.check_alerts()

    return True


@shared_task
def push_otd():

    """PUSH OVER TIME GEN USE DURATION"""

    client = Client.objects.get(id=15)
    branches = Branch.objects.filter(client=client)

    start_date, end_date = time_helpers.get_start_and_end_dates()
    start_date = time_helpers.convert_date(start_date)
    end_date = time_helpers.convert_date(end_date)

    today = datetime.datetime.today()
    yesterday = today - datetime.timedelta(days=1)
    yesterday_str = yesterday.strftime('%A, %d %B %Y')
    date = yesterday.strftime('%Y-%m-%d')
    # date = '2023-08-27'

    message = f"Operating Hours Deviation for {yesterday_str}\n\n{'BRANCH NAME'.ljust(20)}{'DEVICE NAME'.ljust(20)}{'UPTIME OUTSIDE OPERATING HOURS'.ljust(20)}{'WASTED ENERGY'.ljust(30)}\n"

    data = ""

    for branch in branches:
        devices = Device.objects.filter(branch=branch)

        gens = [device for device in devices if device.is_gen]

        for device in gens:

            branch_name = device.branch.name
            device_name = device.name

            value = device.deviation_time_kwh_total_time(date)
            wasted_energy = value['wasted_energy']

            wasted_seconds = value['wasted_time'].total_seconds()
            hours = int(wasted_seconds // 3600)
            minutes = int((wasted_seconds % 3600) // 60)

            uptime = f"{hours} Hour(s) : {minutes} Minutes"

            if uptime != "0 Hour(s) : 0 Minutes":
                message += f"{branch_name.ljust(20)}{device_name.ljust(20)}{uptime.ljust(20)}{wasted_energy.ljust(30)}\n\n"

                data+="""<div
                                style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                <div class="u-col u-col-14p29"
                                    style="max-width: 320px;min-width: 85.74px;display: table-cell;vertical-align: top;">
                                    <div
                                        style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                            style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--<![endif]-->

                                            <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                cellpadding="0" cellspacing="0" width="100%" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="v-container-padding-padding"
                                                            style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;"
                                                            align="left">

                                                            <div class="v-text-align v-line-height v-font-size"
                                                                style="font-family: 'Montserrat',sans-serif; font-size: 12px; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                <p style="line-height: 140%;">"""+branch.name+"""</p>
                                                            </div>

                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div><!--<![endif]-->
                                    </div>
                                </div>

                                <!--[if (mso)|(IE)]></td><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                <div class="u-col u-col-14p29"
                                    style="max-width: 320px;min-width: 85.74px;display: table-cell;vertical-align: top;">
                                    <div
                                        style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                            style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--<![endif]-->

                                            <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                cellpadding="0" cellspacing="0" width="100%" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="v-container-padding-padding"
                                                            style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;"
                                                            align="left">

                                                            <div class="v-text-align v-line-height v-font-size"
                                                                style="font-family: 'Montserrat',sans-serif; font-size: 12px; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                <p style="line-height: 140%;">"""+device_name+"""</p>
                                                            </div>

                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div><!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                <div class="u-col u-col-14p27"
                                    style="max-width: 320px;min-width: 85.62px;display: table-cell;vertical-align: top;">
                                    <div
                                        style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                            style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--<![endif]-->

                                            <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                cellpadding="0" cellspacing="0" width="100%" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="v-container-padding-padding"
                                                            style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;"
                                                            align="left">

                                                            <div class="v-text-align v-line-height v-font-size"
                                                                style="font-family: 'Montserrat',sans-serif; font-size: 13px; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                <p style="line-height: 140%;">"""+uptime+"""</p>
                                                            </div>

                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div><!--<![endif]-->
                                    </div>
                                </div>
                                <!--[if (mso)|(IE)]></td><![endif]-->
                                <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                <div class="u-col u-col-14p27"
                                    style="max-width: 320px;min-width: 85.62px;display: table-cell;vertical-align: top;">
                                    <div
                                        style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                        <!--[if (!mso)&(!IE)]><!-->
                                        <div
                                            style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--<![endif]-->

                                            <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                cellpadding="0" cellspacing="0" width="100%" border="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="v-container-padding-padding"
                                                            style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;"
                                                            align="left">

                                                            <div class="v-text-align v-line-height v-font-size"
                                                                style="font-family: 'Montserrat',sans-serif; font-size: 13px; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                <p style="line-height: 140%;">"""+wasted_energy+"""</p>
                                                            </div>

                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                            <!--[if (!mso)&(!IE)]><!-->
                                        </div><!--<![endif]-->
                                    </div>
                                </div>

                                <!--[if (mso)|(IE)]></td><![endif]-->
                                <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>"""

    html_message ="""
    <!DOCTYPE HTML
        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:o="urn:schemas-microsoft-com:office:office">

    <head>
        <!--[if gte mso 9]>
    <xml>
    <o:OfficeDocumentSettings>
        <o:AllowPNG/>
        <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="x-apple-disable-message-reformatting">
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"><!--<![endif]-->
        <title></title>

        <style type="text/css">
            @media only screen and (min-width: 620px) {
                .u-row {
                    width: 600px !important;
                }

                .u-row .u-col {
                    vertical-align: top;
                }

                .u-row .u-col-14p27 {
                    width: 85.62px !important;
                }

                .u-row .u-col-14p28 {
                    width: 85.68px !important;
                }

                .u-row .u-col-14p29 {
                    width: 85.74px !important;
                }

                .u-row .u-col-100 {
                    width: 600px !important;
                }

            }

            @media (max-width: 620px) {
                .u-row-container {
                    max-width: 100% !important;
                    padding-left: 0px !important;
                    padding-right: 0px !important;
                }

                .u-row .u-col {
                    min-width: 320px !important;
                    max-width: 100% !important;
                    display: block !important;
                }

                .u-row {
                    width: 100% !important;
                }

                .u-col {
                    width: 100% !important;
                }

                .u-col>div {
                    margin: 0 auto;
                }
            }

            body {
                margin: 0;
                padding: 0;
            }

            table,
            tr,
            td {
                vertical-align: top;
                border-collapse: collapse;
            }

            p {
                margin: 0;
            }

            .ie-container table,
            .mso-container table {
                table-layout: fixed;
            }

            * {
                line-height: inherit;
            }

            a[x-apple-data-detectors='true'] {
                color: inherit !important;
                text-decoration: none !important;
            }

            @media (min-width: 481px) and (max-width: 768px) {}

            table,
            td {
                color: #000000;
            }

            #u_body a {
                color: #cca250;
                text-decoration: none;
            }

            @media (max-width: 480px) {
                #u_content_image_5 .v-src-width {
                    width: auto !important;
                }

                #u_content_image_5 .v-src-max-width {
                    max-width: 100% !important;
                }

                #u_content_heading_3 .v-container-padding-padding {
                    padding: 15px !important;
                }

                #u_content_heading_3 .v-font-size {
                    font-size: 21px !important;
                }

                #u_content_text_3 .v-container-padding-padding {
                    padding: 10px 22px !important;
                }

                #u_content_text_9 .v-container-padding-padding {
                    padding: 10px !important;
                }

                #u_content_text_9 .v-font-size {
                    font-size: 8px !important;
                }

                #u_content_text_9 .v-text-align {
                    text-align: left !important;
                }

                #u_content_text_10 .v-font-size {
                    font-size: 7px !important;
                }

                #u_content_text_11 .v-font-size {
                    font-size: 7px !important;
                }

                #u_content_text_11 .v-line-height {
                    line-height: 160% !important;
                }

                #u_content_text_12 .v-font-size {
                    font-size: 7px !important;
                }

                #u_content_text_13 .v-font-size {
                    font-size: 7px !important;
                }

                #u_content_text_14 .v-font-size {
                    font-size: 7px !important;
                }

                #u_content_text_15 .v-font-size {
                    font-size: 7px !important;
                }

                #u_content_social_1 .v-container-padding-padding {
                    padding: 9px !important;
                }
            }
        </style>



        <!--[if !mso]><!-->
        <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap" rel="stylesheet"
            type="text/css"><!--<![endif]-->

    </head>

    <body class="clean-body u_body"
        style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #f9f9f9;color: #000000">
        <table id="u_body"
            style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #f9f9f9;width:100%"
            cellpadding="0" cellspacing="0">
            <tbody>
                <tr style="vertical-align: top">
                    <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color: #f9f9f9;"><![endif]-->


                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #111114;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #111114;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100"
                                        style="max-width: 320px; display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_image_5" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <table width="100%" cellpadding="0" cellspacing="0"
                                                                    border="0">
                                                                    <tr>
                                                                        <td class="v-text-align"
                                                                            style="padding-right: 0px;padding-left: 0px;"
                                                                            align="center">

                                                                            <img align="center" border="0"
                                                                                src="https://www.backend.wyreng.com/static/images/image-4.jpeg" alt="" title=""
                                                                                style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;"
                                                                                width="600"
                                                                                class="v-src-width v-src-max-width" />

                                                                        </td>
                                                                    </tr>
                                                                </table>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #fffefe;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100"
                                        style="max-width: 320px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="background-color: #fffefe;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_heading_3" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:10px 55px;font-family:'Montserrat',sans-serif;"
                                                                align="left">
                                                                <h1 class="v-text-align v-line-height v-font-size"
                                                                    style="margin: 0px; color: #5c3592; line-height: 140%; text-align: center; word-wrap: break-word; font-family: 'Montserrat',sans-serif; font-size: 33px; ">
                                                                    <strong>""" +client.name+ """</strong></h1>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table id="u_content_text_3" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:10px 60px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div class="v-text-align v-line-height v-font-size"
                                                                    style="color: #000000; line-height: 170%; text-align: center; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 170%;"><span
                                                                            style="font-size: 14px; line-height: 23.8px;"><span
                                                                                style="line-height: 23.8px; font-size: 14px;">Operating
                                                                                Hours Deviation for """+yesterday_str+"""</span></span></p>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #fffbf2;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #fffbf2;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-14p29"
                                        style="max-width: 320px;min-width: 85.74px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_text_9" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:10px 0px 10px 2px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div class="v-text-align v-line-height v-font-size"
                                                                    style="font-size: 12px; font-weight: 700; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                    <p style="line-height: 140%;">Branch</p>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-14p29"
                                        style="max-width: 320px;min-width: 85.74px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_text_10" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div class="v-text-align v-line-height v-font-size"
                                                                    style="font-family: 'Montserrat',sans-serif; font-size: 12px; font-weight: 700; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                    <p style="line-height: 140%;">Device</p>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>

                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-14p27"
                                        style="max-width: 320px;min-width: 85.62px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_text_14" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:9px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div class="v-text-align v-line-height v-font-size"
                                                                    style="font-family: 'Montserrat',sans-serif; font-size: 12px; font-weight: 700; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                    <p style="line-height: 140%;">Time Spent</p>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>

                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-14p27"
                                        style="max-width: 320px;min-width: 85.62px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_text_14" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:9px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div class="v-text-align v-line-height v-font-size"
                                                                    style="font-family: 'Montserrat',sans-serif; font-size: 12px; font-weight: 700; line-height: 140%; text-align: left; word-wrap: break-word;">
                                                                    <p style="line-height: 140%;">Energy Wasted</p>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>

                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100"
                                        style="max-width: 320px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                    cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <table height="0px" align="center" border="0"
                                                                    cellpadding="0" cellspacing="0" width="100%"
                                                                    style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                                                    <tbody>
                                                                        <tr style="vertical-align: top">
                                                                            <td
                                                                                style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                                                                <span>&#160;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: transparent;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100"
                                        style="max-width: 320px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                    cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:0px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <table height="0px" align="center" border="0"
                                                                    cellpadding="0" cellspacing="0" width="100%"
                                                                    style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 1px solid #BBBBBB;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                                                    <tbody>
                                                                        <tr style="vertical-align: top">
                                                                            <td
                                                                                style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                                                                <span>&#160;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #fffbf2;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #fffbf2;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="85" style="width: 85px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    """ + data + """

                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="background-color: #ffffff;width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100"
                                        style="max-width: 320px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="background-color: #ffffff;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                    cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:20px 0px 0px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <table width="100%" cellpadding="0" cellspacing="0"
                                                                    border="0">
                                                                    <tr>
                                                                        <td class="v-text-align"
                                                                            style="padding-right: 0px;padding-left: 0px;"
                                                                            align="center">

                                                                            <img align="center" border="0"
                                                                                src="https://www.backend.wyreng.com/static/images/image-5.gif" alt="" title=""
                                                                                style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: inline-block !important;border: none;height: auto;float: none;width: 100%;max-width: 600px;"
                                                                                width="600"
                                                                                class="v-src-width v-src-max-width" />

                                                                        </td>
                                                                    </tr>
                                                                </table>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>



                        <div class="u-row-container" style="padding: 0px;background-color: transparent">
                            <div class="u-row"
                                style="Margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: #ffffff;">
                                <div
                                    style="border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;">
                                    <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding: 0px;background-color: transparent;" align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:600px;"><tr style="background-color: #ffffff;"><![endif]-->

                                    <!--[if (mso)|(IE)]><td align="center" width="600" style="width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;" valign="top"><![endif]-->
                                    <div class="u-col u-col-100"
                                        style="max-width: 320px;display: table-cell;vertical-align: top;">
                                        <div
                                            style="height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                            <!--[if (!mso)&(!IE)]><!-->
                                            <div
                                                style="box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;">
                                                <!--<![endif]-->

                                                <table id="u_content_social_1" style="font-family:'Montserrat',sans-serif;"
                                                    role="presentation" cellpadding="0" cellspacing="0" width="100%"
                                                    border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:8px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div align="center">
                                                                    <div style="display: table; max-width:158px;">
                                                                        <!--[if (mso)|(IE)]><table width="158" cellpadding="0" cellspacing="0" border="0"><tr><td style="border-collapse:collapse;" align="center"><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse:collapse; mso-table-lspace: 0pt;mso-table-rspace: 0pt; width:158px;"><tr><![endif]-->


                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 21px;" valign="top"><![endif]-->
                                                                        <table align="left" border="0" cellspacing="0"
                                                                            cellpadding="0" width="32" height="32"
                                                                            style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 21px">
                                                                            <tbody>
                                                                                <tr style="vertical-align: top">
                                                                                    <td align="left" valign="middle"
                                                                                        style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                                                                        <a href="https://twitter.com/"
                                                                                            title="Twitter" target="_blank">
                                                                                            <img src="https://www.backend.wyreng.com/static/images/image-2.png"
                                                                                                alt="Twitter"
                                                                                                title="Twitter" width="32"
                                                                                                style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 21px;" valign="top"><![endif]-->
                                                                        <table align="left" border="0" cellspacing="0"
                                                                            cellpadding="0" width="32" height="32"
                                                                            style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 21px">
                                                                            <tbody>
                                                                                <tr style="vertical-align: top">
                                                                                    <td align="left" valign="middle"
                                                                                        style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                                                                        <a href="https://instagram.com/"
                                                                                            title="Instagram"
                                                                                            target="_blank">
                                                                                            <img src="https://www.backend.wyreng.com/static/images/image-1.png"
                                                                                                alt="Instagram"
                                                                                                title="Instagram" width="32"
                                                                                                style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->

                                                                        <!--[if (mso)|(IE)]><td width="32" style="width:32px; padding-right: 0px;" valign="top"><![endif]-->
                                                                        <table align="left" border="0" cellspacing="0"
                                                                            cellpadding="0" width="32" height="32"
                                                                            style="width: 32px !important;height: 32px !important;display: inline-block;border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;margin-right: 0px">
                                                                            <tbody>
                                                                                <tr style="vertical-align: top">
                                                                                    <td align="left" valign="middle"
                                                                                        style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                                                                                        <a href="https://linkedin.com/"
                                                                                            title="LinkedIn"
                                                                                            target="_blank">
                                                                                            <img src="https://www.backend.wyreng.com/static/images/image-3.png"
                                                                                                alt="LinkedIn"
                                                                                                title="LinkedIn" width="32"
                                                                                                style="outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;clear: both;display: block !important;border: none;height: auto;float: none;max-width: 32px !important">
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <!--[if (mso)|(IE)]></td><![endif]-->


                                                                        <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                                                    </div>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                    cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <table height="0px" align="center" border="0"
                                                                    cellpadding="0" cellspacing="0" width="82%"
                                                                    style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;border-top: 2px solid #f9cf40;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                                                    <tbody>
                                                                        <tr style="vertical-align: top">
                                                                            <td
                                                                                style="word-break: break-word;border-collapse: collapse !important;vertical-align: top;font-size: 0px;line-height: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%">
                                                                                <span>&#160;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table style="font-family:'Montserrat',sans-serif;" role="presentation"
                                                    cellpadding="0" cellspacing="0" width="100%" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="v-container-padding-padding"
                                                                style="overflow-wrap:break-word;word-break:break-word;padding:0px 10px 13px;font-family:'Montserrat',sans-serif;"
                                                                align="left">

                                                                <div class="v-text-align v-line-height v-font-size"
                                                                    style="color: #b0b1b4; line-height: 180%; text-align: center; word-wrap: break-word;">
                                                                    <p style="font-size: 14px; line-height: 180%;"><span
                                                                            style="font-size: 12px; line-height: 21.6px;">©
                                                                            2022 All Rights Reserved</span></p>
                                                                </div>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <!--[if (!mso)&(!IE)]><!-->
                                            </div><!--<![endif]-->
                                        </div>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><![endif]-->
                                    <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->
                                </div>
                            </div>
                        </div>


                        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
        <!--[if mso]></div><![endif]-->
        <!--[if IE]></div><![endif]-->
    </body>

    </html>"""
    if message == f"Operating Hours Deviation for {yesterday_str}\n\n{'BRANCH NAME'.ljust(20)}{'DEVICE NAME'.ljust(20)}{'UPTIME OUTSIDE OPERATING HOURS'.ljust(30)}\n":
        print(("No Deviations."))
    else:
        title = "Wyre-Operating-Hours-Deviation-Report"
        receievers = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
                        '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
                        '<EMAIL>']
        for i in receievers:
            receievers = [i]
            bcc = []
            # receievers = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
            # bcc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

            response = mailgun.Mailer.send_daily_otd(2, title, html_message, receievers, bcc)
            if response["status"] == True:
                print(("OTD report sent successfully."))
            else:
                print(("Failed to send OTD report."))

@shared_task
def update_diesel_overview(branch_id, month, year):

    branch = Branch.objects.get(id=branch_id)

    branch.get_diesel_entry_overview(year, month)

@shared_task
def generate_monthly_reports():
    """
    Generate monthly reports for all branches
    Respects exclusions defined in ReportConfiguration
    """
    from main.models import Branch, Client, ReportConfiguration
    import logging

    logger = logging.getLogger(__name__)
    logger.info("Starting monthly report generation")

    # Get configuration
    config = ReportConfiguration.objects.first()
    if not config:
        config = ReportConfiguration.objects.create()

    if not config.enabled:
        logger.info("Monthly report generation is disabled")
        return "Monthly report generation is disabled"

    # Get excluded clients and branches IDs
    excluded_client_ids = list(config.excluded_clients.all().values_list('id', flat=True))
    excluded_branch_ids = list(config.excluded_branches.all().values_list('id', flat=True))

    # Get all branches excluding the ones that should be skipped
    branches = Branch.objects.exclude(id__in=excluded_branch_ids)
    if excluded_client_ids:
        branches = branches.exclude(client_id__in=excluded_client_ids)

    success_count = 0
    failure_count = 0
    failure_details = []

    # Process each branch
    for branch in branches:
        try:
            logger.info(f"Generating report for branch: {branch.name}")

            # Send to frontend service
            FRONTEND_URL = settings.FRONTEND_REPORT_ENDPOINT
            emails = [branch.email]

            payload = {
                "url": f"https://report-template-five.vercel.app/report?branch_id={branch.id}",
                "recipients": emails,
            }

            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(FRONTEND_URL, json=payload, headers=headers)

            if response.status_code == 200:
                success_count += 1
                logger.info(f"Successfully generated report for {branch.name}")
            else:
                failure_count += 1
                failure_details.append({
                    "branch": branch.name,
                    "error": response.text
                })
                logger.error(f"Failed to generate report for {branch.name}: {response.text}")

        except Exception as e:
            failure_count += 1
            failure_details.append({
                "branch": branch.name,
                "error": str(e)
            })
            logger.error(f"Error generating report for {branch.name}: {str(e)}")

    result = {
        "total_branches": branches.count(),
        "success_count": success_count,
        "failure_count": failure_count,
        "failures": failure_details
    }

    logger.info(f"Monthly report generation completed: {result}")
    return result


@shared_task
def populate_current_month_branch_metrics():
    """
    For each active branch, upsert (create or update) the MonthlyBranchMetrics for the current month.
    Runs every 2 hours between 6am and 11pm.
    """
    now = timezone.now()
    year = now.year
    month = now.month

    for client in Client.objects.filter(is_active=True):
        for branch in Branch.objects.filter(client=client, is_active=True):
            # Calculate metrics for this branch/month/year
            metrics = MonthlyBranchMetrics.calculate_monthly_metrics(branch, month, year)
            # Upsert (update or create) the record for this branch/month/year
            MonthlyBranchMetrics.objects.update_or_create(
                client=client,
                branch=branch,
                month=month,
                year=year,
                defaults={"metrics": metrics}
            )
    return f"Populated MonthlyBranchMetrics for {month}/{year}"



# @shared_task
# def generate_monthly_reports_task():
#     """
#     Generate & cache MonthlyReport.data for every active branch
#     for the previous calendar month—using naive datetimes and cleaning
#     up DB connections so you never run out of clients.
#     """
#     # 1) Ask your helper for the previous-month window
#     start_prev, end_prev, _, _ = time_helpers.get_previous_month_start_and_end_dates_from_today()

#     # 2) Normalize to dates
#     if isinstance(start_prev, datetime.datetime):
#         sd = start_prev.date()
#     else:
#         sd = start_prev

#     # 3) Build naive datetimes
#     start_dt = datetime.datetime(sd.year, sd.month, sd.day, 0, 0, 0)
#     last_day = calendar.monthrange(sd.year, sd.month)[1]
#     end_dt   = datetime.datetime(sd.year, sd.month, last_day, 23, 59, 59)

#     year, month = sd.year, sd.month

#     total = success = failures = 0
#     failure_details = []

#     # 4) Materialize all branch IDs in one go (no open cursors thereafter)
#     branch_ids = list(Branch.objects.filter(is_active=True).values_list("pk", flat=True))

#     for bid in branch_ids:
#         total += 1
#         try:
#             # 5) Re-fetch each branch by PK (fresh connection)
#             branch = Branch.objects.get(pk=bid)

#             # 6) Generate the report
#             report_data = branch.get_monthly_report(start_date=start_dt, end_date=end_dt)
#             if report_data is None:
#                 raise ValueError("get_monthly_report returned None")

#             # 7) Upsert it in a tiny transaction
#             with transaction.atomic():
#                 MonthlyReport.objects.update_or_create(
#                     branch=branch,
#                     year=year,
#                     month=month,
#                     defaults={"data": report_data}
#                 )
#             success += 1

#         except Exception as e:
#             failures += 1
#             failure_details.append({
#                 "branch_id": bid,
#                 "error": str(e)
#             })

#         finally:
#             # 8) Tidy up any stray connections opened during get_monthly_report()
#             connections.close_all()

#     return {
#         "period": f"{year}-{month:02d}",
#         "total_branches": total,
#         "successes": success,
#         "failures": failures,
#         "failure_details": failure_details,
#     }



@shared_task
def generate_monthly_reports_task():
    """
    Generate & cache MonthlyReport.data for every active branch
    for the previous calendar month—process sequentially and aggressively close DB connections to avoid too many clients.
    """
    import main.models
    import datetime, calendar
    from main.scripts import time_helpers
    from django.db import close_old_connections, connections, transaction

    # 1) Get previous month window
    start_prev, end_prev, _, _ = time_helpers.get_previous_month_start_and_end_dates_from_today()
    if isinstance(start_prev, datetime.datetime):
        sd = start_prev.date()
    else:
        sd = start_prev
    start_dt = datetime.datetime(sd.year, sd.month, sd.day, 0, 0, 0)
    last_day = calendar.monthrange(sd.year, sd.month)[1]
    end_dt   = datetime.datetime(sd.year, sd.month, last_day, 23, 59, 59)
    year, month = sd.year, sd.month

    # 2) Get all branch IDs
    branch_ids = list(main.models.Branch.objects.filter(is_active=True).values_list("pk", flat=True))

    total = success = failures = 0
    failure_details = []

    def process_branch(bid):
        from main.models import Branch, MonthlyReport
        from django.db import connections, transaction, close_old_connections

        close_old_connections()
        try:
            branch = Branch.objects.get(pk=bid)
            report_data = branch.get_monthly_report(start_date=start_dt, end_date=end_dt)
            if report_data is None:
                raise ValueError("get_monthly_report returned None")
            with transaction.atomic():
                MonthlyReport.objects.update_or_create(
                    branch=branch,
                    year=year,
                    month=month,
                    defaults={"data": report_data}
                )
            return (bid, True, None)
        except Exception as e:
            return (bid, False, str(e))
        finally:
            connections.close_all()

    # 3) Process sequentially to minimize open connections
    for bid in branch_ids:
        close_old_connections()
        total += 1
        bid, ok, err = process_branch(bid)
        if ok:
            success += 1
        else:
            failures += 1
            failure_details.append({"branch_id": bid, "error": err})
        connections.close_all()

    return {
        "period": f"{year}-{month:02d}",
        "total_branches": total,
        "successes": success,
        "failures": failures,
        "failure_details": failure_details,
    }