from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
from main.models import Reading, Datalog, Device, Client, ViewPermission, Branch, Bill_Mails, FuelConsumption, DieselOverviewHistory
from account.models import User
import datetime
import calendar
from main import tasks


## CREATE A DEFAULT READING FOR A DEVICE 
@receiver(post_save, sender = Device)
def create_device(sender, instance, created, **kwargs):

    if created:

        device = instance

        date_time_str = '2018-01-01'
        date_time_obj = datetime.datetime.strptime(date_time_str, '%Y-%m-%d')
        client = device.client

        created = Reading.objects.create(client = client, device = device, branch = device.branch, post_datetime =  date_time_obj, post_date = date_time_obj, post_time = date_time_obj)

        Datalog.objects.create(client = client, device = device, branch = device.branch, post_datetime = date_time_obj, post_date = date_time_obj, post_time = date_time_obj, digital_input_1 = 0, digital_input_2 = 0, digital_input_3 = 0, digital_input_4 = 0, summary_energy_register_1 = 0, summary_energy_register_2 = 0, total_kw = 0, pulse_counter = 0) 

        device.last_reading = default_last_read
        device.save()

@receiver(post_save, sender = Branch)
def create_branch(sender, instance, created, **kwargs):

    if created:
        superusers = User.objects.filter(is_superuser = True)
        branch = instance

        for user in superusers:

            created = ViewPermission.objects.create(user = user, branch = branch)

## CREATE A DEFAULT READING FOR A BILL RECIEVER 
@receiver(post_save, sender = User)
def create_bill_reciever(sender, instance, created, **kwargs):

    if created:

        user = instance

        client = user.client

        Bill_Mails.create_new(email = user.email, creator_id = user.id, internal_user= True)


default_last_read = {
                        "ResultCode": 200,
                        "Message": "null",
                        "Success": True,
                        "Data": [
                            {
                                "Data": [
                                    {
                                        "Description": "Voltage L1/L12",
                                        "Value": 0,
                                        "Units": "V"
                                    },
                                    {
                                        "Description": "Voltage L2/L23",
                                        "Value": 0,
                                        "Units": "V"
                                    },
                                    {
                                        "Description": "Voltage L3/L31",
                                        "Value": 0,
                                        "Units": "V"
                                    },
                                    {
                                        "Description": "Current L1",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "Current L2",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "Current L3",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "kW L1",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "kW L2",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "kW L3",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "kvar L1",
                                        "Value": 0.0,
                                        "Units": "kvar"
                                    },
                                    {
                                        "Description": "kvar L2",
                                        "Value": 0.0,
                                        "Units": "kvar"
                                    },
                                    {
                                        "Description": "kvar L3",
                                        "Value": 0.0,
                                        "Units": "kvar"
                                    },
                                    {
                                        "Description": "kVA L1",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    },
                                    {
                                        "Description": "kVA L2",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    },
                                    {
                                        "Description": "kVA L3",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    },
                                    {
                                        "Description": "Power factor L1",
                                        "Value": 0.0,
                                        "Units": "PF"
                                    },
                                    {
                                        "Description": "Power factor L2",
                                        "Value": 0.0,
                                        "Units": "PF"
                                    },
                                    {
                                        "Description": "Power factor L3",
                                        "Value": 0.0,
                                        "Units": "PF"
                                    },
                                    {
                                        "Description": "Total kW",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "Total kvar",
                                        "Value": 0.0,
                                        "Units": "kvar"
                                    },
                                    {
                                        "Description": "Total kVA",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    },
                                    {
                                        "Description": "Total PF",
                                        "Value": 0.0,
                                        "Units": "PF"
                                    },
                                    {
                                        "Description": "Avg Frequency",
                                        "Value": 0,
                                        "Units": "Hz"
                                    },
                                    {
                                        "Description": "Neutral current",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "kWh import",
                                        "Value": 0,
                                        "Units": "kWh"
                                    },
                                    {
                                        "Description": "kWh export",
                                        "Value": 0,
                                        "Units": "kWh"
                                    },
                                    {
                                        "Description": "kVAh total",
                                        "Value": 0,
                                        "Units": "kVAh"
                                    },
                                    {
                                        "Description": "Max Amp. Demand L1",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "Max Amp. Demand L2",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "Max Amp. Demand L3",
                                        "Value": 0.0,
                                        "Units": "A"
                                    },
                                    {
                                        "Description": "Max. sliding window kW Demand",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "Accum. kW Demand",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "Max. sliding window kVA Demand",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    },
                                    {
                                        "Description": "Present sliding window kW Demand",
                                        "Value": 0.0,
                                        "Units": "kW"
                                    },
                                    {
                                        "Description": "Present sliding window kVA Demand",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    },
                                    {
                                        "Description": "Accum. kVA Demand",
                                        "Value": 0.0,
                                        "Units": "kVA"
                                    }
                                ],
                                "DeviceID": 0,
                                "RecordTime": "1900-01-01T00:00:00.000"
                            }
                        ]
}

# COST TRACKER UPDATE SIGNALS

# Signal Handling:
# The signals are set up to handle post_save and post_delete events for the FuelConsumption
# model. When a FuelConsumption instance is saved or deleted, 
# the data for the corresponding branch, year, and month is deleted, 
# and the get_diesel_entry_overview method is called to recalculate and update the DieselOverviewHistory.

# Cache Management:
# The data is invalidated to ensure that the next time the data is requested, 
# it will be recalculated and updated with the latest information.

# With this setup, any changes to FuelConsumption will trigger the necessary updates to DieselOverviewHistory
# and the cache, ensuring that the data remains consistent and up-to-date.

@receiver(post_save, sender=FuelConsumption)
def update_diesel_overview_history_on_save(sender, instance, **kwargs):
    start_date = instance.start_date
    branch = instance.branch

    try:
        # Try to get and delete the DieselOverviewHistory entry for the month
        DieselOverviewHistory.objects.get(
            start_date=start_date, branch=branch.id
        ).delete()
    except DieselOverviewHistory.DoesNotExist:
        # Handle the case where the DieselOverviewHistory entry does not exist
        pass

    tasks.update_diesel_overview.delay(branch.id, start_date.month, start_date.year)

@receiver(post_delete, sender=FuelConsumption)
def update_diesel_overview_history_on_delete(sender, instance, **kwargs):
    start_date = instance.start_date
    branch = instance.branch

    try:
        # Try to get and delete the DieselOverviewHistory entry for the month
        DieselOverviewHistory.objects.get(
            start_date=start_date, branch=branch.id
        ).delete()
    except DieselOverviewHistory.DoesNotExist:
        # Handle the case where the DieselOverviewHistory entry does not exist
        pass

    tasks.update_diesel_overview.delay(branch.id, start_date.month, start_date.year)