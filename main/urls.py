from django.urls import path
from main import views

urlpatterns = [
    path('device/', views.devices),
    path('device/<int:device_id>', views.update_device),
    path('branch/', views.branches),
    path('branch/<int:branch_id>', views.update_branch),
    path('user_permit/<int:user_id>', views.user_pemit),
    path('summ_emergy_diff/<int:device_id>/<str:date1>/<str:date2>', views.range_data),
    path('kilo_per_day/<int:device_id>/<str:date1>/<str:date2>', views.kilo_per_day),
    path('stats_per_day/<int:device_id>/<str:date1>/<str:date2>', views.stats_per_day),
    path('dashboard/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard),
    ########################## DASHBOARD ENDPOINTS START #################################
    path('dashboard/total_energy/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_total_energy_endpoint),
    path('dashboard/device_usage/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_device_usage_endpoint),
    path('dashboard/daily_consumption/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_daily_consumption_endpoint),
    path('dashboard/load_overview/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_load_overview_endpoint),

    path('dashboard/branches_overview/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_branches_overview),
    path('dashboard/energy_consumption/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_energy_consumption, name='dashboard-energy-consumption'),
    path('dashboard/last_reading/<int:user_id>/<str:start_date>', views.dashboard_last_reading, name='dashboard-last-reading'),
    path('dashboard/power_demand/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>/', views.dashboard_power_demand, name='dashboard-power-demand'),
    path('dashboard/power_quality/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>/', views.dashboard_power_quality, name='dashboard-power-quality'), 
    ########################## DASHBOARD ENDPOINTS END #################################
    path('energy_consumption/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.energy_consumption),
    path('power_quality/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.power_quality),
    path('power_demand/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.power_demand),
    path('last_reading/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.last_reading),
    path('billing/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.billing),
    ########################## SCORECARD ENDPOINTS START #################################
    path('scorecard/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.scorecard),
    path('scorecard/baseline-energy/<int:user_id>/<str:start_date>/<str:end_date>/', views.scorecard_baseline_energy, name='baseline-energy'),
    path('scorecard/peak-to-avg-power-ratio/<int:user_id>/<str:start_date>/<str:end_date>/', views.scorecard_peak_to_avg_power_ratio, name='peak-to-avg-power-ratio'),
    path('scorecard/carbon-emissions/<int:user_id>/<str:start_date>/<str:end_date>/', views.scorecard_carbon_emissions, name='score-card-carbon-emissions'),
    path('scorecard/generator-size-efficiency/<int:user_id>/<str:start_date>/<str:end_date>/', views.scorecard_generator_size_efficiency, name='generator-size-efficiency'),
    path('scorecard/operating-time/<int:user_id>/<str:start_date>/<str:end_date>/', views.scorecard_operating_time, name='operating-time'),
    path('scorecard/fuel-consumption/<int:user_id>/<str:start_date>/<str:end_date>/', views.scorecard_fuel_consumption, name='fuel-consumption'),
    ########################## SCORECARD ENDPOINTS END #################################
    path('dashboard_data/<int:user_id>/<str:start_date>/<str:end_date>/<str:frequency>', views.dashboard_data),
    path('billing_data/<int:user_id>/<str:start_date>/<str:end_date>', views.billing_data),
    path('side_bar_data/<int:user_id>', views.side_bar_data),
    path('branch/', views.branches),
    path('clients/all', views.clients_all),
    path('clients/<int:client_id>/branches/all', views.client_branches_all),
    path('clients/<int:client_id>/<int:branch_id>/create_access', views.temporary_admin_access),
    path('report_download/<int:user_id>/<str:start_date>/<str:end_date>/', views.download_pdf_view),
    path('send_report/<int:user_id>/<str:start_date>/<str:end_date>/', views.mail_report_pdf_view),
    path('send_reports/', views.send_monthly_report),
    path('mail_schedules_data/<int:user_id>/', views.get_bill_schedule_data),
    path('add_external_bill_reciever/<int:user_id>/', views.add_external_bill_reciever),
    path('delete_mail_reciever/<int:user_id>/', views.delete_mail_reciever),
    path('add_assigned_devices/<int:user_id>/', views.add_assigned_devices),
    path("branch/<int:branch_id>/generators/",views.branch_generators,name="branch-generators"),
    path('cost_tracker/<int:user_id>/add_cost/<str:cost_type>/', views.cost_tracker),
    path('cost_tracker_overview/<int:user_id>/', views.cost_tracker_overview),
    path('diesel_tracker_overview/<int:user_id>/<int:year>/<int:month>', views.get_diesel_entry_overview),
    path('branch/<int:branch_id>/<int:user_id>/add_equipment/', views.equipment),
    path('branch/<int:branch_id>/<int:user_id>/edit_equipment/<int:equipment_id>/', views.equipment),
    path('branch/<int:branch_id>/<int:user_id>/edit_equipment/<int:equipment_id>/<str:action>/', views.equipment),
    path('equipments/<int:user_id>/', views.get_quipments),
    path('get_reports/<int:user_id>/<str:end_date>/<str:period>/', views.get_reports),
    path('get_reports_baseline/<int:user_id>/<str:end_date>/<str:period>/', views.get_reports_baseline),
    path('alerts_data/<int:user_id>/', views.alerts_data),
    path('average_power_factor/', views.average_power_factor),
    path('periodic_utility/<str:start_date>/<str:end_date>/', views.periodic_utility),
    path('branch_demand/<int:user_id>/<str:start_date>/<str:end_date>/', views.branch_demand),
    path('branch_demand_by_branch/<int:branch_id>/<str:start_date>/<str:end_date>/', views.branch_demand_by_branch),
    path('branch_uptime_deviations/<int:user_id>/<str:start_date>/<str:end_date>/', views.get_branch_uptime_outside_operating_hours),
    path('client_uptime_deviations/<int:client_id>/<str:start_date>/<str:end_date>/', views.get_client_uptime_outside_operating_hours),
    path('send_otd_report/', views.send_otd_report),
    path('get_all_devices/<str:password>/', views.get_all_devices),
    path('get_device_readings/<str:password>/<str:device_id>/<str:start_date>/<str:end_date>/', views.get_device_readings),
    path('get_device_zero_readings/<str:password>/<str:device_id>/<str:start_date>/<str:end_date>/', views.get_device_zero_readings),
    path('delete_device_zero_readings/<str:password>/<str:device_id>/<str:start_date>/<str:end_date>/', views.delete_device_zero_readings),
    path('get_aggregated_device_readings/<str:password>/<str:device_id>/<str:start_date>/<str:end_date>/', views.get_aggregated_device_readings),
    path('get_timed_device_readings/<str:password>/<str:device_id>/<str:start_date>/<str:end_date>/<str:start_time>/<str:end_time>/', views.get_timed_device_readings),
    path('device_billing/<int:device_id>/<str:start_date>/<str:end_date>/', views.get_biling_data),
    path('baseline/<int:device_id>/<str:end_date>/', views.get_base_line),
    path('blended_cost/<int:branch_id>/<str:start_date>/<str:end_date>/', views.get_branch_blended_cost),
    path('push_otd/', views.push_otd_view),

    # UPLOAD IMAGE OF PREVIOUS MONTH'S DATA
    path('add_month_end_cost/<int:user_id>/', views.upload_monthly_diesel_balance),
    path('fuel-entry/<int:branch_id>/', views.FuelConsumptionView.as_view()),
    path('monthly-fuel-entry/<int:branch_id>/', views.monthly_fuel_entry),
    path('update-fuel-entry/<int:pk>/', views.update_fuel_entry),
    path('delete-fuel-entry/<int:pk>/', views.delete_fuel_entry),

    # SET CLIENT'S DEVICES OPERATING HOURS
    path('set-client-operating-hours/<int:client_id>/', views.set_client_devices_operating_hours),
    path('post_weekly_diesel_usage/<int:branch_id>/', views.post_weekly_diesel_usage),
    path('reminder_mail_check/<int:branch_id>/', views.reminder_mail_check),

    path('cost_tracker/<str:cost_type>/', views.mail_cost_tracker),
    path('dashboard_deviation_check/<str:name>/<str:date>/', views.dashboard_deviation_check),
    path('device_deviation_check/<int:id>/<str:date>/', views.device_deviation_check),

    #SMART ACB CONTROL
    path('toggle_acb/', views.ACB_Control.as_view()),
    path('all_acb/<int:branch_id>/', views.ACB_View.as_view()),

    # NON POSTING FEATURE
    path('toggle_npa/<str:device_id>/', views.toggle_non_posting_attention),

    # INTERNAL TOOLS
    path('dashboard_deviation_check/<str:name>/<str:date>/', views.dashboard_deviation_check),
    path('device_deviation_check/<int:id>/<str:date>/', views.device_deviation_check),
    path('skyview_check/<int:branch_id>/<str:start_date>/<str:end_date>/', views.branch_energy_check),
    path('omole_check/<int:branch_id>/<str:start_date>/<str:end_date>/', views.branch_device_energy_check),
    path('device_usage_hours/<int:device_id>/<int:month>/<str:start_time>/<str:end_time>/', views.device_usage_details),
    path('chatbot/', views.chatbot_view),
    
    # REPORTS
    path("generate-report/", views.GenerateReportView.as_view()),
    path('report-configuration/', views.ReportConfigurationView.as_view(), name='report_configuration'),

    ##Test
      path('test-usage/<int:device_id>/<slug:start_date>/<slug:end_date>/',views.test_time_of_use,name='test_time_of_use'),
      path('fuel-efficiency-debug/', views.DeviceFuelEfficiencyDebugView.as_view()),

]

# import requests

# def send_simple_message():
#     return requests.post(
#         "https://api.mailgun.net/v3/mg.wyreng.com/messages",
#         auth=("api", "**************************************************"),
# <AUTHOR> <EMAIL>",
#               "to": ["<EMAIL>", "<EMAIL>"],
#               "subject": "Hello",
#               "text": "Testing some Mailgun awesomness!"})

# send_simple_message()
