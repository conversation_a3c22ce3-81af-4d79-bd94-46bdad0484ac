from main.views import devices
from django.test import TestCase, SimpleTestCase
import pandas as pd
from main.models import *
import datetime, random
from django.db.models import Q
from main.views import time_helpers

# # Create your tests here.
# class TestBillingSchedule(TestCase):

#     def setUp(self):
#         Client.objects.create(name = "test")
#         self.client = Client.objects.get(name = "test")

#         User.objects.create(first_name = "lawal", client=self.client, username = "lawal02")
#         self.user = User.objects.get(first_name = "lawal")

#         Branch.objects.create(name = "test_branch", client=self.client)
#         self.branch2 = Branch.objects.get(name = "test_branch")
        
#         Branch.objects.create(name = "Second Branch", client=self.client)
#         self.branch = Branch.objects.get(name = "Second Branch")

#         DeviceType.objects.create(choice_name = "IPP")
#         device_type = DeviceType.objects.all()[0]

#         device = Device.objects.create(name = "IPP", client=self.client, branch= self.branch, fuel_type="diesel", type= device_type)

#         device2 = Device.objects.create(name = "MAIN SOURCE", client=self.client, branch= self.branch2, fuel_type="diesel", type= device_type)

#         self.device = Device.objects.all()[0]
#         self.device2 = Device.objects.all()[1]

#         external_mail_reciever = Bill_Mails.objects.create(email = "<EMAIL>")
#         self.external_reciever = Bill_Mails.objects.all()[0]
#         self.external_reciever.email = "<EMAIL>"
#         self.external_reciever.save()

#         Bill_Mails.objects.filter(~Q(id=1)).delete() # delete all except first one

#     def test_daily_cost_of_energy(self):
        
#         target_device = Device.objects.all()[0]
#         result = target_device.get_cost_of_energy_daily("2020-12-01", "2021-01-29")
#         result = target_device.get_billing_data(datetime.datetime.now(), datetime.datetime.now())
        
#         self.assertIsInstance(result, dict)

#     def test_add_external_mail_reciever(self):

#         devices = [self.device.id]
#         response = self.external_reciever.add_reporting_device(*devices)
#         # print(response)
#         self.external_reciever.save()

#         self.assertIsInstance(response["data"], list)

#     def test_add_multiple_external_mail_recievers(self):

#         devices = [self.device.id, self.device2.id]
#         response = self.external_reciever.add_reporting_device(*devices)

#         self.assertIsInstance(response["data"], list)
#         self.assertEqual(len(response["data"]), 2)

#     def test_handles_wrong_device_for_external_mail_recievers(self):

#         devices = [self.device.id, self.device2.id, 100]
#         response = self.external_reciever.add_reporting_device(*devices)

#         self.assertEqual(response["task"], False)
    
#     def test_remove_external_mail_recievers(self):
        
#         devices = [self.device.id, self.device2.id]
#         self.external_reciever.add_reporting_device(*devices)

#         device = self.device.id
#         response = self.external_reciever.remove_reporting_device(device)
        
#         self.assertIsInstance(response["data"], list)
#         self.assertEqual(len(response["data"]), 1)

#     def test_create_new(self):

#         email_reciever = Bill_Mails.create_new(email = self.external_reciever.email, creator_id = self.user.id)

#         self.assertIsInstance(email_reciever, Bill_Mails)
    
#     def test_create_new_internal_reciever(self):
        
#         Bill_Mails.objects.all().delete()
#         email_reciever = Bill_Mails.create_new(email = self.external_reciever.email, creator_id = self.user.id, user = self.user, internal_user= True)

#         self.assertEquals(self.user.id, email_reciever.user.id)
#         self.assertIsInstance(email_reciever, Bill_Mails)
    
#     def test_create_new_added_correct_client(self):

#         email_reciever = Bill_Mails.create_new(email = self.external_reciever.email, creator_id = self.user.id)

#         self.assertEquals(email_reciever.client.id, self.client.id)
 
#     def test_set_frequency(self):
#         Bill_Mails.objects.all().delete()
#         email_reciever = Bill_Mails.create_new(email = self.external_reciever.email, creator_id = self.user.id, user = self.user, internal_user= True)

#         frequency = "M"

#         response = email_reciever.set_frequency(frequency)
        
#         self.assertEquals(response, frequency)

#     def test_new_bill_reciever_created_on_new_user(self):

#         user = User.objects.create(first_name = "lawal", client=self.client, username = "lawal05", email = "<EMAIL>")

#         bill_mail = Bill_Mails.objects.filter(email = "<EMAIL>")

#         self.assertTrue(bill_mail.exists())
   
#     def test_filter_users_added_recievers(self):
        
#         bill_mail = Bill_Mails.create_new(email = self.external_reciever.email, creator_id = self.user.id)
#         devices = [self.device.id, self.device2.id]
#         bill_mail.add_reporting_device(*devices)

#         recievers = Bill_Mails.get_all_added_by(user_id = self.user.id)

#         self.assertTrue(len(recievers)>0)

#     def test_update_bill_mail_frequency(self):
       
#         prefered_frequency = "DAILY"
#         bill_mail = Bill_Mails.objects.get(user = self.user)
#         bill_mail.set_frequency(prefered_frequency)

#         self.assertTrue(bill_mail.bill_frequency == prefered_frequency)
        

class TestBillingSchedule(TestCase):

    def setUp(self):
        Client.objects.create(name = "test")
        self.client = Client.objects.get(name = "test")

        User.objects.create(first_name = "lawal", client=self.client, username = "lawal02")
        self.user = User.objects.get(first_name = "lawal")

        Branch.objects.create(name = "test_branch", client=self.client)
        self.branch2 = Branch.objects.get(name = "test_branch")
        
        Branch.objects.create(name = "Second Branch", client=self.client)
        self.branch = Branch.objects.get(name = "Second Branch")

        DeviceType.objects.create(choice_name = "IPP")
        device_type = DeviceType.objects.all()[0]

        device = Device.objects.create(name = "IPP", client=self.client, branch= self.branch, fuel_type="diesel", type= device_type)

        device2 = Device.objects.create(name = "MAIN SOURCE", client=self.client, branch= self.branch2, fuel_type="diesel", type= device_type)

        self.device = Device.objects.all()[0]
        self.device2 = Device.objects.all()[1]

        external_mail_reciever = Bill_Mails.objects.create(email = "<EMAIL>")
        self.external_reciever = Bill_Mails.objects.all()[0]
        self.external_reciever.email = "<EMAIL>"
        self.external_reciever.save()

        Bill_Mails.objects.filter(~Q(id=1)).delete() # delete all except first one

    def test_daily_cost_of_energy(self):
        
        target_device = Device.objects.all()[0]
        result = target_device.get_cost_of_energy_daily("2020-12-01", "2021-01-29")
        result = target_device.get_billing_data(datetime.datetime.now(), datetime.datetime.now())
        
        self.assertIsInstance(result, dict)


# Create your tests here.
class TestCostTracker(TestCase):

    def setUp(self):
        Client.objects.create(name = "test")
        self.client = Client.objects.get(name = "test")

        User.objects.create(first_name = "lawal", client=self.client, username = "lawal02")
        self.user = User.objects.get(first_name = "lawal")

        Branch.objects.create(name = "test_branch", client=self.client)
        self.branch2 = Branch.objects.get(name = "test_branch")
        
        Branch.objects.create(name = "Second Branch", client=self.client)
        self.branch = Branch.objects.get(name = "Second Branch")

        DeviceType.objects.create(choice_name = "IPP")
        device_type = DeviceType.objects.all()[0]

        device = Device.objects.create(name = "IPP", client=self.client, branch= self.branch, fuel_type="diesel", type= device_type)

        device2 = Device.objects.create(name = "MAIN SOURCE", client=self.client, branch= self.branch2, fuel_type="diesel", type= device_type)

        self.device = Device.objects.all()[0]
        self.device2 = Device.objects.all()[1]

        equipments = ["fridge", "lights", "toaster"]
        
        start_date = "01-05-2021 00:00"
        end_date = "30-05-2021 00:00"

        self.start_date = time_helpers.convert_date(start_date)
        self.end_date = time_helpers.convert_date(end_date)

        for equipment in equipments:

            new_equipment = Equipment(  branch = self.branch, 
                                        name = random.choice(equipments), 
                                        quantity = random.randint(1,10),
                                        voltage = random.randint(200, 250),
                                        date_purchased = self.start_date
                                    )

            new_equipment.save()
        
        for i in range(50):

            new_cost = Cost(  branch = self.branch, 
                                        quantity = random.randint(1,10),
                                        price_per_litre = random.randint(200, 250),
                                        amount = random.randint(200, 20000),
                                        fuel_type = random.choice(["petrol", "diesel"]),
                                        cost_type = random.choice(["petrol", "diesel"]),
                                        tarrif = random.randint(200, 300),
                                        date = time_helpers.convert_date(f"{random.randint(1,30)}-05-2021 00:00")
                                    )

            new_cost.save()

    # def test_get_all_equipment(self):
        
    #     all_devices = Equipment.get_all(self.branch)
    #     print(all_devices)
    
    # def test_filter_cost(self):
        
    #     costs = Cost.get_diesel_purchased(self.branch.id)
    #     print(costs)

    def test_get_cost_of_per_kwh(self):
        
        print("This is the cost here..: ")
        costs = self.branch.get_cost_of_per_kwh(self.start_date, self.end_date)
        print(costs)