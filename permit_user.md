# User Permission Management API

## Update User Branch Permissions

This endpoint allows you to manage a user's branch permissions by adding and/or removing access to specific branches in a single request.

### Endpoint

```
PATCH /adminapp/user_pemit/{user_id}/
```

### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| user_id   | integer | The ID of the user whose permissions you want to update |

### Request Headers

| Header | Value |
|--------|-------|
| Content-Type | application/json |
| Authorization | Bearer {your_token} |

### Request Body

```json
{
    "user": <user_id>,
    "add": [<branch_id_1>, <branch_id_2>, ...],
    "remove": [<branch_id_3>, <branch_id_4>, ...]
}
```

#### Request Parameters Explained

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user | integer | Yes | The ID of the user whose permissions you want to update. Must match the user_id in the URL path. |
| add | array of integers | Yes | List of branch IDs to grant the user access to. Can be an empty array if you only want to remove permissions. |
| remove | array of integers | Yes | List of branch IDs to revoke the user's access from. Can be an empty array if you only want to add permissions. |

### Response

```json
{
    "status": true,
    "message": "Updated branch permissions",
    "added_branches": ["Branch Name 1", "Branch Name 2"],
    "already_existing_branches": ["Branch Name 3"],
    "deleted_branches": ["Branch Name 4"],
    "not_found_branches": ["Branch Name 5"],
    "branches": [
        {
            "id": 1,
            "name": "Branch Name 1"
        },
        {
            "id": 2,
            "name": "Branch Name 2"
        }
    ]
}
```

#### Response Parameters Explained

| Parameter | Type | Description |
|-----------|------|-------------|
| status | boolean | Indicates whether the operation was successful (true) or failed (false). |
| message | string | A human-readable message describing the result of the operation. |
| added_branches | array of strings | Names of branches that were successfully added to the user's permissions. Will be empty if no branches were added. |
| already_existing_branches | array of strings | Names of branches that the user already had permission for (from the "add" list). |
| deleted_branches | array of strings | Names of branches that were successfully removed from the user's permissions. Will be empty if no branches were removed. |
| not_found_branches | array of strings | Names of branches that couldn't be found in the database. These branches were not processed. |
| branches | array of objects | Complete list of all branches the user now has access to after the update operation. Each object contains the branch ID and name. |

### Example cURL

```bash
curl --location --request PATCH 'http://localhost:8000/adminapp/user_pemit/254/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_AUTH_TOKEN' \
--data-raw '{
    "user": 254,
    "add": [127, 93],
    "remove": [50, 89]
}'
```

### Response Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success - Permissions updated successfully |
| 400 | Bad Request - Invalid payload format or validation error |
| 401 | Unauthorized - Authentication token is missing or invalid |
| 403 | Forbidden - User doesn't have sufficient permissions to perform this action |
| 404 | Not Found - User not found |
| 500 | Internal Server Error - Something went wrong on the server |

### Notes

- Both `add` and `remove` arrays must be present in the request, but they can be empty.
- The operation is atomic - if any part fails, the entire operation will be rolled back.
- The response includes a complete list of all branches the user has access to after the update.
- You need admin permissions to use this endpoint.
- Branch IDs that don't exist will be reported in the `not_found_branches` array but won't cause the request to fail.
