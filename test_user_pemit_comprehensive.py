#!/usr/bin/env python
import os
import sys
import json
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wyre.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from main.models import Branch, ViewPermission
from adminapp.views import user_pemit
from rest_framework.test import force_authenticate

# Get the User model
User = get_user_model()

def test_user_pemit_update_comprehensive():
    # Get a user that exists in the database
    user = User.objects.filter(is_active=True).first()
    
    if not user:
        print("No active users found in the database.")
        return
    
    print(f"Testing with user: {user.username} (ID: {user.id})")
    
    # Step 1: First, let's add some permissions to the user
    print("\n--- Step 1: Adding initial permissions ---")
    
    # Get branches that the user doesn't have permissions for
    user_branch_ids = ViewPermission.objects.filter(user_id=user.id).values_list('branch_id', flat=True)
    branches_to_add_initially = list(Branch.objects.exclude(id__in=user_branch_ids)[:3].values_list('id', flat=True))
    
    if not branches_to_add_initially:
        print("No branches available to add initially.")
        return
    
    print(f"Branches to add initially: {branches_to_add_initially}")
    
    # Create a request factory
    factory = RequestFactory()
    
    # Create a request to add initial permissions
    payload = {
        'user': user.id,
        'add': branches_to_add_initially,
        'remove': []
    }
    
    request = factory.generic('UPDATE', f'/adminapp/user_pemit/{user.id}/', 
                             data=json.dumps(payload), 
                             content_type='application/json')
    
    # Authenticate the request
    force_authenticate(request, user=user)
    
    # Call the view function directly
    response = user_pemit(request, user_id=user.id)
    
    # Print the response
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.data}")
    
    # Step 2: Now let's test both adding and removing permissions
    print("\n--- Step 2: Testing add and remove operations ---")
    
    # Get updated user permissions
    user_branch_ids = ViewPermission.objects.filter(user_id=user.id).values_list('branch_id', flat=True)
    
    # Branches to remove (from those we just added)
    branches_to_remove = list(branches_to_add_initially[:1])  # Remove the first branch we added
    
    # Branches to add (that the user still doesn't have)
    branches_to_add = list(Branch.objects.exclude(id__in=user_branch_ids)[:2].values_list('id', flat=True))
    
    print(f"Branches to add: {branches_to_add}")
    print(f"Branches to remove: {branches_to_remove}")
    
    # Create a request for the comprehensive test
    payload = {
        'user': user.id,
        'add': branches_to_add,
        'remove': branches_to_remove
    }
    
    request = factory.generic('UPDATE', f'/adminapp/user_pemit/{user.id}/', 
                             data=json.dumps(payload), 
                             content_type='application/json')
    
    # Authenticate the request
    force_authenticate(request, user=user)
    
    # Call the view function directly
    response = user_pemit(request, user_id=user.id)
    
    # Print the response
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.data}")
    
    # Step 3: Verify the final state
    print("\n--- Step 3: Verifying final state ---")
    
    # Get the final permissions
    final_permissions = ViewPermission.objects.filter(user_id=user.id)
    final_branch_ids = [perm.branch_id for perm in final_permissions]
    final_branch_names = [perm.branch.name for perm in final_permissions]
    
    print(f"Final branch IDs with permissions: {final_branch_ids}")
    print(f"Final branch names with permissions: {final_branch_names}")
    
    # Check if the operations were successful
    for branch_id in branches_to_add:
        if branch_id in final_branch_ids:
            print(f"✅ Branch ID {branch_id} was successfully added")
        else:
            print(f"❌ Branch ID {branch_id} was NOT added")
    
    for branch_id in branches_to_remove:
        if branch_id not in final_branch_ids:
            print(f"✅ Branch ID {branch_id} was successfully removed")
        else:
            print(f"❌ Branch ID {branch_id} was NOT removed")

if __name__ == "__main__":
    test_user_pemit_update_comprehensive()
