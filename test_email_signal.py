#!/usr/bin/env python3
import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wyre.settings')
django.setup()

# Import models
from account.models import User, Client
from django.contrib.auth.hashers import make_password

# Check if a test client exists, or create one
test_client = Client.objects.filter(name="Test Email").first()
if not test_client:
    print("Creating test client...")
    test_client = Client.objects.create(
        name="Test Email",
        client_type="STANDARD",
        phone_number="**********",
        email="<EMAIL>",
        address="123 Test Street"
    )
    print(f"Test client created with ID: {test_client.id}")
else:
    print(f"Using existing test client with ID: {test_client.id}")

# Create a test user
username = f"testuser_{os.getpid()}"
email = "<EMAIL>"  # Replace with your email to receive the test

print(f"Creating test user {username} with email {email}...")
test_user = User.objects.create(
    username=username,
    email=email,
    first_name="Test",
    last_name="User",
    client=test_client,
    roles=4,  # OPERATOR
    has_no_password=True,
    password="temporary"  # This will be replaced by the signal
)

print(f"Test user created with ID: {test_user.id}")
print("If the signal is working correctly, an email should have been sent to the user's email address.")
print("Check your email and the console output for confirmation.")
